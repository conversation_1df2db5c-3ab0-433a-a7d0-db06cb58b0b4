# Supabase API Key Migration Documentation v1.0

## Overview
This document details the migration from Supabase's legacy JWT-based API keys to the new publishable/secret key system implemented in June 2025.

## Background

### Legacy System Issues
The old JWT-based `anon` and `service_role` keys had several limitations:
- **Tight coupling** between JWT secret and Postgres roles
- **No independent rotation** without downtime
- **No rollback capability** for problematic rotations
- **Mobile app challenges** with forced rotations during app store reviews
- **Long expiry duration** (10 years) creating security risks
- **Large JWT size** leading to logging and security issues
- **Symmetric signing** preventing future Auth features

### New System Benefits
- **Advanced security features** with enterprise-ready capabilities
- **Zero-downtime rotation** of API keys
- **Instant revocation** by deleting keys
- **Browser protection** preventing secret key misuse
- **Audit logging** for all key operations
- **Asymmetric JWT support** foundation

## Migration Process

### Timeline
- **June 2025**: Early preview launched
- **July 2025**: Full feature launch
- **November 2025**: Monthly migration reminders begin
- **Late 2026**: Legacy keys deprecated

### Key Changes

#### Environment Variables
**Before:**
```env
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**After:**
```env
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY=sb_publishable_72OBmtd31lmttRVS-cuiDw_mU0zQ2qm
```

#### Code Changes

**Supabase Client (`lib/supabase.ts`):**
```typescript
// Before
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// After
const supabasePublishableKey = process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!
```

**Middleware (`middleware.ts`):**
```typescript
// Before
const supabase = createServerClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  // ...
)

// After
const supabase = createServerClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!,
  // ...
)
```

## New API Key Types

### Publishable Keys
- **Format**: `sb_publishable_...`
- **Privileges**: Low (equivalent to old `anon` key)
- **Usage**: Safe for client-side code, mobile apps, GitHub actions
- **Availability**: Platform-wide

### Secret Keys
- **Format**: `sb_secret_...`
- **Privileges**: Elevated (equivalent to old `service_role` key)
- **Usage**: Backend servers, admin panels, Edge Functions only
- **Availability**: Platform-wide
- **Security**: Hidden by default, audit logged

### Legacy Keys (Transitional)
- **anon**: JWT format, low privileges
- **service_role**: JWT format, elevated privileges
- **Status**: Available during migration period
- **Future**: Will be deprecated in late 2026

## Key Differences and Limitations

### Permission and Access Control
- Secret keys are hidden by default
- Individual key revelation is audit logged
- Enhanced security monitoring

### Instant Revocation
- Delete a secret key for immediate revocation
- No waiting period or propagation delay
- Immediate effect across all services

### Browser Protection
- Secret keys cannot be used in browsers
- Always fails with HTTP 401 Unauthorized
- Prevents accidental exposure

### Realtime Limitations
- Connections last 24 hours without signed-in user
- Sign users in to extend connections indefinitely
- May require configuration updates

### Edge Functions
- Functions with `--no-verify-jwt` option need explicit configuration
- Authorization header behavior changed
- JWT validation updates required

### Authorization Header Changes
- No longer accepts publishable/secret keys in Authorization header
- Must use user's JWT or leave header empty
- Backward compatibility only if header matches apikey header

## Implementation in Omnipixel MVP

### Files Modified
1. **`lib/supabase.ts`**: Updated client configuration
2. **`middleware.ts`**: Updated server-side auth
3. **`.env.example`**: Updated environment variable names
4. **Documentation**: Updated all references

### Testing Performed
- ✅ Authentication flow working
- ✅ Dashboard access functional
- ✅ Project management operational
- ✅ Middleware route protection active
- ✅ No breaking changes observed

### Rollback Plan
If issues arise, can temporarily re-enable legacy keys:
1. Add `NEXT_PUBLIC_SUPABASE_ANON_KEY` back to environment
2. Update code to use legacy key as fallback
3. Re-enable legacy keys in Supabase dashboard

## Security Considerations

### Key Management
- Store secret keys securely (never in client code)
- Use environment variables for all keys
- Rotate keys regularly using zero-downtime process
- Monitor audit logs for key usage

### Access Control
- Publishable keys for client-side only
- Secret keys for server-side only
- Never mix key types in same environment
- Validate key usage patterns

### Monitoring
- Set up alerts for key usage anomalies
- Monitor audit logs for unauthorized access
- Track key rotation schedules
- Review access patterns regularly

## Troubleshooting

### Common Issues

**"Invalid API Key" Error:**
- Verify key format (sb_publishable_ or sb_secret_)
- Check environment variable names
- Ensure key is enabled in dashboard

**"Legacy API keys are disabled" Error:**
- Update to new key format
- Re-enable legacy keys temporarily if needed
- Check Edge Function configuration

**Realtime Connection Issues:**
- Verify user authentication status
- Check connection duration limits
- Update Realtime configuration if needed

### Debug Steps
1. Verify environment variables are loaded
2. Check Supabase dashboard for key status
3. Review network requests for key usage
4. Test with legacy keys to isolate issues
5. Check audit logs for key operations

## Future Considerations

### Asymmetric JWT Support
- Foundation laid for future JWT improvements
- Enhanced security with public/private key pairs
- Better integration with third-party services

### Enhanced Features
- More granular permissions
- Custom role assignments for secret keys
- Advanced audit and monitoring capabilities

### Migration Timeline
- Plan for complete migration before late 2026
- Test thoroughly in staging environments
- Coordinate with team for production deployment
- Monitor for any breaking changes

## Resources

### Documentation
- [Supabase API Keys Guide](https://supabase.com/docs/guides/api/api-keys)
- [GitHub Discussion #29260](https://github.com/orgs/supabase/discussions/29260)
- [Migration Timeline](https://supabase.com/docs/guides/api/api-keys#migration-timeline)

### Support
- Supabase Support for migration assistance
- Community discussions for best practices
- Documentation updates for latest changes
