import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function createClient(url?:string, key?:string) {
  const cookieStore = await cookies()
  url = url ?? process.env.NEXT_PUBLIC_SUPABASE_URL!
  key = key ?? process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!

  return createServerClient(
    url,
    key,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )
}