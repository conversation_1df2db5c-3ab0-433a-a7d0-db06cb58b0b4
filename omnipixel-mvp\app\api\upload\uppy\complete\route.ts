import { NextRequest, NextResponse } from 'next/server'
import { s3Client, BUCKET_NAME, isAWSConfigured } from '@/lib/aws'
import { PutObjectTaggingCommand } from '@aws-sdk/client-s3'
import { createClient } from '@/utils/supabase/server'

export async function POST(request: NextRequest) {
  try {
    // Check if AWS is configured
    if (!isAWSConfigured()) {
      return NextResponse.json(
        { error: 'File upload is not configured. Please contact the administrator.' },
        { status: 503 }
      )
    }

    // Create Supabase client for server-side auth
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { filename: originalFilename, projectId, s3Key: receivedS3Key, fileSize } = body

    // Debug: Log the received S3 key
    console.log('🔍 Received S3 key:', receivedS3Key)

    // Handle case where s3Key might be just the filename (fallback from upload handler)
    let s3Key = receivedS3Key
    let s3Filename = ''
    let buildId = ''

    if (receivedS3Key.includes('/')) {
      // Full S3 key provided
      s3Filename = receivedS3Key.split('/').pop() || ''
      buildId = s3Filename.replace('.zip', '')
      console.log('🔍 Using full S3 key:', s3Key)
    } else {
      // Only filename provided, reconstruct full S3 key
      // We'll get the project owner's user ID later and update this
      s3Filename = receivedS3Key
      buildId = s3Filename.replace('.zip', '')
      s3Key = `uploads/${user.id}/${projectId}/${s3Filename}` // Temporary, will be updated
      console.log('🔍 Reconstructed full S3 key (temporary):', s3Key)
    }

    console.log('🔍 Final S3 key:', s3Key)
    console.log('🔍 Extracted filename:', s3Filename)
    console.log('🔍 Extracted build ID:', buildId)

    // Validate required fields
    if (!originalFilename || !projectId || !s3Key || !buildId) {
      return NextResponse.json(
        { error: 'Missing required fields: filename, projectId, s3Key, or invalid S3 key format' },
        { status: 400 }
      )
    }

    // Get user profile to check if they're a platform admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    const isPlatformAdmin = profile?.role === 'platform_admin'

    // Verify that the project exists and user has access (owner or platform admin)
    let query = supabase
      .from('projects')
      .select('id, user_id, name, auto_release')
      .eq('id', projectId)

    // If not platform admin, restrict to user's own projects
    if (!isPlatformAdmin) {
      query = query.eq('user_id', user.id)
    }

    const { data: project, error: projectError } = await query.single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    }

    // Update S3 key to use project owner's user ID for consistent file organization
    if (!receivedS3Key.includes('/')) {
      s3Key = `uploads/${project.user_id}/${projectId}/${s3Filename}`
      console.log('🔍 Updated S3 key with project owner ID:', s3Key)
    }

    // Check build limits - prevent upload if user already has 2 builds
    const { data: existingBuilds, error: buildsError } = await supabase
      .from('builds')
      .select('id, status')
      .eq('project_id', projectId)
      .not('status', 'in', '(failed,archived)')

    if (buildsError) {
      console.error('Error checking existing builds:', buildsError)
      return NextResponse.json(
        { error: 'Failed to check existing builds' },
        { status: 500 }
      )
    }

    if (existingBuilds && existingBuilds.length >= 2) {
      return NextResponse.json(
        {
          error: 'Build limit reached',
          message: 'You can have a maximum of 2 builds per project. Please delete an existing build before uploading a new one.'
        },
        { status: 400 }
      )
    }

    // Get the latest version number for this project
    const { data: latestBuild } = await supabase
      .from('builds')
      .select('version')
      .eq('project_id', projectId)
      .order('version', { ascending: false })
      .limit(1)
      .single()

    const nextVersion = latestBuild ? latestBuild.version + 1 : 1

    // Use the build ID and filename from the upload params (already uploaded to S3)
    // The file is already stored as [build-id].zip in S3

    // Determine initial status based on auto_release setting only
    let initialStatus: 'inactive' | 'processing' = 'inactive'
    let isCurrent = false

    // Check if this is the first build for the project
    const isFirstBuild = existingBuilds.length === 0

    console.log('🔍 Auto-release enabled:', project.auto_release)
    console.log('🔍 Is first build:', isFirstBuild)

    if (project.auto_release) {
      // Only trigger Stream Service upload if auto-release is explicitly enabled
      // Set to processing since it will be sent to StreamPixel
      // BUT don't set as current until the stream servicewebhook confirms
      initialStatus = 'processing'
      isCurrent = false // Wait for Stream Service webhook to set this

      // DON'T deactivate existing current build yet - wait for Stream Service confirmation
    }

    // Store metadata in S3 (source of truth) - this will trigger Lambda processing
    try {
      // console.log('🏷️ Storing S3 metadata for:', s3Key)
      // console.log('Using bucket:', BUCKET_NAME)

      // Prepare S3 tags (metadata)
      // const s3Tags = [
      //   { Key: 'project-id', Value: projectId },
      //   { Key: 'user-id', Value: user.id },
      //   { Key: 'filename', Value: s3Filename || `${buildId}.zip` }, // Build ID based filename (e.g., uuid.zip)
      //   { Key: 'original-filename', Value: originalFilename }, // Keep original filename for reference
      //   { Key: 'build-id', Value: buildId },
      //   { Key: 'version', Value: nextVersion.toString() },
      //   { Key: 'file-size', Value: (fileSize || 0).toString() },
      //   { Key: 'status', Value: initialStatus },
      //   { Key: 'streampixel-status', Value: 'ready' },
      //   { Key: 'auto-release', Value: project.auto_release.toString() },
      //   { Key: 'is-current', Value: isCurrent.toString() },
      //   { Key: 'uploaded-at', Value: new Date().toISOString() }
      // ]

      // console.log('S3 tags to apply:', s3Tags)

      // Store metadata in S3 tags
      // await s3Client.send(new PutObjectTaggingCommand({
      //   Bucket: BUCKET_NAME,
      //   Key: s3Key,
      //   Tagging: {
      //     TagSet: s3Tags
      //   }
      // }))

      // console.log('✅ S3 metadata stored successfully for:', s3Key)

      // Create database record as cache (Lambda will also do this, but we do it here for immediate UI response)
      const { data: build, error: buildError } = await supabase
        .from('builds')
        .insert({
          id: buildId, // Use the build ID from S3 filename
          project_id: projectId,
          filename: s3Filename || `${buildId}.zip`, // Build ID based filename (e.g., uuid.zip)
          original_filename: originalFilename, // Store original filename for reference
          s3_key: s3Key,
          version: nextVersion,
          file_size: fileSize || 0,
          status: initialStatus, // inactive by default, active if first build or auto-release
          streampixel_status: 'ready',
          is_current: isCurrent, // true if first build or auto-release enabled
        })
        .select()
        .single()

      if (buildError) {
        console.error('Error creating build record:', buildError)
        // Don't fail the request - S3 is source of truth, database is just cache
        console.log('Database cache creation failed, but S3 metadata is stored')
      }

      // If auto-release is enabled or first build, trigger Stream Service upload
      if (initialStatus === 'processing') {
        try {
          console.log('🚀 Auto-triggering Stream Service upload for build:', buildId)

          // Update build status to show it's preparing for upload
          await supabase
            .from('builds')
            .update({
              streampixel_status: 'waiting_for_file',
              updated_at: new Date().toISOString()
            })
            .eq('id', buildId)

          // Trigger StreamPixel upload asynchronously (don't await to avoid blocking the response)
          fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/streampixel/upload`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Cookie': request.headers.get('Cookie') || '', // Forward cookies for auth
            },
            body: JSON.stringify({
              buildId: buildId,
              projectId: projectId
            })
          }).then(async (streamPixelResponse) => {
            if (!streamPixelResponse.ok) {
              console.error('Stream Service auto-upload failed:', await streamPixelResponse.text())
              // Update build status to show failure
              await supabase
                .from('builds')
                .update({
                  streampixel_status: 'failed',
                  error_message: 'Auto-upload to StreamPixel failed',
                  updated_at: new Date().toISOString()
                })
                .eq('id', buildId)
            } else {
              console.log('✅ Stream Service auto-upload triggered successfully')
            }
          }).catch(async (streamPixelError) => {
            console.error('Error triggering Stream Service upload:', streamPixelError)
            // Update build status to show failure
            await supabase
              .from('builds')
              .update({
                streampixel_status: 'failed',
                error_message: 'Error triggering StreamPixel upload',
                updated_at: new Date().toISOString()
              })
              .eq('id', buildId)
          })
        } catch (error) {
          console.error('Error setting up Stream Service upload:', error)
        }
      }

      return NextResponse.json({
        message: 'Upload completed successfully',
        build: build || {
          id: buildId,
          project_id: projectId,
          filename: s3Filename || `${buildId}.zip`,
          original_filename: originalFilename,
          s3_key: s3Key,
          version: nextVersion,
          file_size: fileSize || 0,
          status: initialStatus,
          streampixel_status: 'ready',
          is_current: isCurrent,
        }
      })

    } catch (s3Error: unknown) {
      console.error('❌ Error storing S3 metadata:', s3Error)

      // Type-safe error handling
      interface S3ErrorMetadata {
              httpStatusCode?: number;
            }
            interface S3ErrorDetails {
              name?: string;
              message?: string;
              code?: string;
              statusCode?: number;
            }
            const errorDetails: S3ErrorDetails = {}
            if (s3Error && typeof s3Error === 'object') {
              const name = (s3Error as Record<string, unknown>).name
              errorDetails.name = typeof name === 'string' ? name : undefined
              errorDetails.message = (s3Error as Record<string, unknown>).message as string | undefined
              errorDetails.code = (s3Error as Record<string, unknown>).Code as string | undefined
              errorDetails.statusCode = (s3Error as { $metadata?: S3ErrorMetadata }).$metadata?.httpStatusCode
            }
      console.error('S3 Error details:', errorDetails)

      return NextResponse.json(
        {
          error: 'Failed to store file metadata',
          details: errorDetails.message || 'Unknown S3 error'
        },
        { status: 500 }
      )
    }

    // Note: Stream Service upload is auto-triggered only for auto-release enabled projects

  } catch (error) {
    console.error('Error completing upload:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
