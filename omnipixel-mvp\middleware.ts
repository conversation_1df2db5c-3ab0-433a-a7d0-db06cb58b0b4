import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { updateSession } from './utils/supabase/middleware'

export async function middleware(req: NextRequest) {
  // Skip middleware for API/static/image routes and favicon
  
// return NextResponse.next();
  
  return await updateSession(req);

}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
