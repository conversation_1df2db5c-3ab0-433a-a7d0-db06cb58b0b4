-- Add config column to projects table for stream player configuration
ALTER TABLE projects 
ADD COLUMN IF NOT EXISTS config JSONB DEFAULT '{
  "autoConnect": false,
  "touchInput": true,
  "keyBoardInput": true,
  "resolutionMode": "Dynamic Resolution Mode",
  "maxStreamQuality": "1080p (1920x1080)",
  "primaryCodec": "H264",
  "fallBackCodec": "VP8",
  "isPasswordProtected": false,
  "password": "",
  "loadingMessage": "Loading stream...",
  "connectingMessage": "Connecting to stream...",
  "disconnectedMessage": "Stream disconnected",
  "reconnectingMessage": "Reconnecting...",
  "errorMessage": "Stream error occurred",
  "connectButtonText": "Connect to Stream"
}'::jsonb;

-- Add comment to the config column
COMMENT ON COLUMN projects.config IS 'Stream player configuration including settings, security, and UI messages';

-- Create index on config for faster queries
CREATE INDEX IF NOT EXISTS idx_projects_config_password_protected 
ON projects USING GIN ((config->'isPasswordProtected'));

-- Update existing projects to have default config if they don't have one
UPDATE projects 
SET config = '{
  "autoConnect": false,
  "touchInput": true,
  "keyBoardInput": true,
  "resolutionMode": "Dynamic Resolution Mode",
  "maxStreamQuality": "1080p (1920x1080)",
  "primaryCodec": "H264",
  "fallBackCodec": "VP8",
  "isPasswordProtected": false,
  "password": "",
  "loadingMessage": "Loading stream...",
  "connectingMessage": "Connecting to stream...",
  "disconnectedMessage": "Stream disconnected",
  "reconnectingMessage": "Reconnecting...",
  "errorMessage": "Stream error occurred",
  "connectButtonText": "Connect to Stream"
}'::jsonb
WHERE config IS NULL;
