{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/utils/supabase/middleware.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\r\nimport { NextResponse, type NextRequest } from 'next/server'\r\n\r\nexport async function updateSession(request: NextRequest) {\r\n  let supabaseResponse = NextResponse.next({\r\n    request,\r\n  })\r\n\r\n  const supabase = createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return request.cookies.getAll()\r\n        },\r\n        setAll(cookiesToSet) {\r\n          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))\r\n          supabaseResponse = NextResponse.next({\r\n            request,\r\n          })\r\n          cookiesToSet.forEach(({ name, value, options }) =>\r\n            supabaseResponse.cookies.set(name, value, options)\r\n          )\r\n        },\r\n      },\r\n    }\r\n  )\r\n\r\n  // IMPORTANT: Avoid writing any logic between createServerClient and\r\n  // supabase.auth.getClaims(). A simple mistake could make it very hard to debug\r\n  // issues with users being randomly logged out.\r\n\r\n  // IMPORTANT: Don't remove getClaims()\r\n  const { data } = await supabase.auth.getClaims()\r\n\r\n  const user = data?.claims\r\n\r\n  // Handle root path redirection\r\n  if (request.nextUrl.pathname === '/') {\r\n    const url = request.nextUrl.clone()\r\n    url.pathname = user ? '/dashboard' : '/login'\r\n    return NextResponse.redirect(url)\r\n  }\r\n\r\n  // Protect routes\r\n  const protectedRoutes = [\r\n    '/dashboard',\r\n    '/projects',\r\n    '/admin'\r\n  ]\r\n\r\n  const isProtectedRoute = protectedRoutes.some(route => \r\n    request.nextUrl.pathname.startsWith(route)\r\n  )\r\n\r\n  if (\r\n    !user &&\r\n    (isProtectedRoute ||\r\n    (!request.nextUrl.pathname.startsWith('/login') &&\r\n     !request.nextUrl.pathname.startsWith('/auth') &&\r\n    !request.nextUrl.pathname.startsWith('/api') &&\r\n    !request.nextUrl.pathname.startsWith('/embed')))\r\n  ) {\r\n    const url = request.nextUrl.clone()\r\n    url.pathname = '/login'\r\n    return NextResponse.redirect(url)\r\n  }\r\n\r\n  // IMPORTANT: You *must* return the supabaseResponse object as it is. If you're\r\n  // creating a new response object with NextResponse.next() make sure to:\r\n  // 1. Pass the request in it, like so:\r\n  //    const myNewResponse = NextResponse.next({ request })\r\n  // 2. Copy over the cookies, like so:\r\n  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())\r\n  // 3. Change the myNewResponse object to fit your needs, but avoid changing\r\n  //    the cookies!\r\n  // 4. Finally:\r\n  //    return myNewResponse\r\n  // If this is not done, you may be causing the browser and server to go out\r\n  // of sync and terminate the user's session prematurely!\r\n\r\n  return supabaseResponse\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEO,eAAe,cAAc,OAAoB;IACtD,IAAI,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvC;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,sUAGhC;QACE,SAAS;YACP;gBACE,OAAO,QAAQ,OAAO,CAAC,MAAM;YAC/B;YACA,QAAO,YAAY;gBACjB,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,MAAM;gBAC7E,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACnC;gBACF;gBACA,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,iBAAiB,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO;YAE9C;QACF;IACF;IAGF,oEAAoE;IACpE,+EAA+E;IAC/E,+CAA+C;IAE/C,sCAAsC;IACtC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,SAAS;IAE9C,MAAM,OAAO,MAAM;IAEnB,+BAA+B;IAC/B,IAAI,QAAQ,OAAO,CAAC,QAAQ,KAAK,KAAK;QACpC,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;QACjC,IAAI,QAAQ,GAAG,OAAO,eAAe;QACrC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,iBAAiB;IACjB,MAAM,kBAAkB;QACtB;QACA;QACA;KACD;IAED,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;IAGtC,IACE,CAAC,QACD,CAAC,oBACA,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,aACrC,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,YACtC,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,WACrC,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAU,GAC/C;QACA,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;QACjC,IAAI,QAAQ,GAAG;QACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,+EAA+E;IAC/E,wEAAwE;IACxE,sCAAsC;IACtC,0DAA0D;IAC1D,qCAAqC;IACrC,qEAAqE;IACrE,2EAA2E;IAC3E,kBAAkB;IAClB,cAAc;IACd,0BAA0B;IAC1B,2EAA2E;IAC3E,wDAAwD;IAExD,OAAO;AACT"}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\nimport { updateSession } from './utils/supabase/middleware'\n\nexport async function middleware(req: NextRequest) {\n  // Skip middleware for API/static/image routes and favicon\n  \n// return NextResponse.next();\n  \n  return await updateSession(req);\n\n}\n\nexport const config = {\n  matcher: [\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAEA;;AAEO,eAAe,WAAW,GAAgB;IAC/C,0DAA0D;IAE5D,8BAA8B;IAE5B,OAAO,MAAM,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD,EAAE;AAE7B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;KACD;AACH"}}]}