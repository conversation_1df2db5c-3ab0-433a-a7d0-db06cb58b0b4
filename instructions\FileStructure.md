omnipixel-mvp/
├── app/                                   # Next.js App Router
│   ├── layout.tsx                         # Main layout, global UI (header/nav)
│   ├── page.tsx                           # Landing or redirect to /dashboard
│   ├── login/
│   │   └── page.tsx                       # Login/auth page
│   ├── dashboard/
│   │   └── page.tsx                       # User project dashboard
│   ├── projects/
│   │   └── [projectId]/
│   │       ├── page.tsx                   # Project overview (config + builds)
│   │       ├── upload/                    # ZIP upload UI
│   │       │   └── UploadSection.tsx
│   │       └── stream/
│   │           └── page.tsx               # Streaming page with player
│   └── admin/                             # Platform Manager area
│       ├── users/
│       │   └── page.tsx                   # User management UI
│       └── projects/
│           └── page.tsx                   # Admin project management
│
├── pages/api/                             # API routes
│   ├── get-presign-url.ts                 # Generates multipart presigned URLs
│   ├── notify-upload.ts                   # Triggers upload-complete Lambda
│   └── admin/
│       ├── users.ts                       # Create/edit/delete users
│       └── projects.ts                    # Link projects to users
│
├── lambda/                                # AWS Lambda functions (SAM-deployed)
│   ├── getPresignUrl/                     # local Lambda get-presign handler
│   │   └── app.js
│   └── handleUploadComplete/              # local Lambda for post-upload logic
│       └── app.js
│
├── infra/                                 # Infrastructure-as-code
│   └── template.yaml                      # AWS SAM config (S3, Lambdas, IAM)
│
├── components/                            # Reusable React components
│   ├── ProjectCard.tsx
│   ├── BuildList.tsx
│   ├── UploadProgress.tsx
│   └── Player.tsx
│
├── hooks/                                 # Custom React hooks
│   ├── useAuth.ts
│   ├── useProjects.ts
│   └── useUpload.ts
│
├── lib/                                   # Third-party client setup/helpers
│   ├── supabase.ts                        # Supabase client
│   ├── s3.ts                              # S3 multipart presign helper
│   ├── stream.ts                          # Streaming SDK init logic
│   └── admin.ts                           # Supabase Admin client (service key)
│
├── styles/                                # Global styling
│   └── globals.css
│
├── .env.example                           # Env vars: Supabase, AWS credentials, S3 bucket
├── next.config.js
├── tsconfig.json
└── package.json
