{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "regions": ["iad1"], "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "env": {}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/admin/:path*", "destination": "/admin/:path*"}], "redirects": [{"source": "/", "destination": "/dashboard", "permanent": false}]}