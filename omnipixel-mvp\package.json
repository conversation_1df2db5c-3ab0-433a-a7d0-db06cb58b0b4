{"name": "omnipixel-mvp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:all": "npm run test && npm run test:e2e", "setup:env": "node scripts/setup-env.js", "setup:aws": "node scripts/setup-aws.js", "deploy:aws": "node scripts/deploy-aws.js", "deploy:aws:staging": "node scripts/deploy-aws.js -e staging", "deploy:aws:prod": "node scripts/deploy-aws.js -e prod", "deploy:aws:help": "node scripts/deploy-aws.js -h", "supabase:generate-types": "npx supabase gen types typescript --linked > lib/database.types.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.850.0", "@aws-sdk/lib-storage": "^3.850.0", "@aws-sdk/s3-request-presigner": "^3.850.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.1", "@uppy/aws-s3": "^4.2.3", "@uppy/aws-s3-multipart": "^4.0.0", "@uppy/core": "^4.4.7", "@uppy/dashboard": "^4.3.4", "@uppy/drag-drop": "^4.1.3", "@uppy/file-input": "^4.1.3", "@uppy/informer": "^4.2.1", "@uppy/progress-bar": "^4.2.1", "@uppy/status-bar": "^4.1.3", "@uppy/xhr-upload": "^4.3.3", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "next": "15.4.3", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "streampixelsdk": "github:infinity-void-metaverse/Streampixel-Web-SDK", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tw-animate-css": "^1.3.5", "typescript": "^5"}}