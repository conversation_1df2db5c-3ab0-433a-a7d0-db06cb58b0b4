import {
  S3<PERSON>lient,
  PutObjectCommand,
  PutObjectAclCommand,
  GetObjectCommand,
  HeadObjectCommand,
  DeleteObjectCommand,
  ListObjectVersionsCommand,
  CreateMultipartUploadCommand,
  CompleteMultipartUploadCommand,
  AbortMultipartUploadCommand,
  UploadPartCommand
} from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import {
  createBackblazeS3Client,
  getBackblazeBucketName,
  isBackblazeConfigured,
  getBackblazePublicUrl
} from './backblaze-adapter'

// Use Backblaze B2 with S3-compatible API instead of AWS S3
console.log('🔧 Initializing Backblaze S3 Client...')
console.log('Environment check:')
console.log('- BACKBLAZE_APP_KEY_ID:', process.env.BACKBLAZE_APP_KEY_ID ? `${process.env.BACKBLAZE_APP_KEY_ID.substring(0, 10)}...` : 'MISSING')
console.log('- BACKBLAZE_APP_KEY:', process.env.BACKBLAZE_APP_KEY ? `${process.env.BACKBLAZE_APP_KEY.substring(0, 10)}...` : 'MISSING')
console.log('- BACKBLAZE_BUCKET_NAME:', process.env.BACKBLAZE_BUCKET_NAME || 'MISSING')

export const s3Client = createBackblazeS3Client()
console.log('✅ Backblaze S3 Client created successfully')

// Backblaze B2 bucket name
export const BUCKET_NAME = getBackblazeBucketName()

// Generate a unique S3 key for a file (without omnipixel prefix since bucket is already named omnipixel)
export function generateS3Key(userId: string, projectId: string, filename: string): string {
  const timestamp = Date.now()
  const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_')
  // No omnipixel prefix needed since bucket is already named omnipixel
  return `uploads/${userId}/${projectId}/${timestamp}_${sanitizedFilename}`
}

// Upload file to S3 with metadata (S3 as source of truth)
export async function uploadFileToS3(
  key: string,
  file: Buffer | Uint8Array | string,
  contentType: string = 'application/octet-stream',
  metadata?: {
    projectId?: string
    userId?: string
    filename?: string
    version?: string
    streamPixelProcess?: boolean
  }
): Promise<string> {
  console.log('🚀 Starting uploadFileToS3...')
  console.log('Parameters:')
  console.log('- key:', key)
  console.log('- file size:', file.length, 'bytes')
  console.log('- contentType:', contentType)
  console.log('- metadata:', metadata)

  if (!isBackblazeConfigured()) {
    console.error('❌ Backblaze not configured!')
    console.error('Environment variables:')
    console.error('- BACKBLAZE_APP_KEY_ID:', process.env.BACKBLAZE_APP_KEY_ID ? 'SET' : 'MISSING')
    console.error('- BACKBLAZE_APP_KEY:', process.env.BACKBLAZE_APP_KEY ? 'SET' : 'MISSING')
    console.error('- BACKBLAZE_BUCKET_NAME:', process.env.BACKBLAZE_BUCKET_NAME ? 'SET' : 'MISSING')
    throw new Error('Backblaze credentials not configured')
  }
  console.log('✅ Backblaze configuration check passed')

  // Prepare S3 metadata (source of truth)
  const s3Metadata: Record<string, string> = {}
  if (metadata) {
    if (metadata.projectId) s3Metadata['project-id'] = metadata.projectId
    if (metadata.userId) s3Metadata['user-id'] = metadata.userId
    if (metadata.filename) s3Metadata['filename'] = metadata.filename
    if (metadata.version) s3Metadata['version'] = metadata.version
    if (metadata.streamPixelProcess !== undefined) {
      s3Metadata['streampixel-process'] = metadata.streamPixelProcess ? 'true' : 'false'
    }
    s3Metadata['uploaded-at'] = new Date().toISOString()
  }

  console.log('📦 Creating PutObjectCommand with:')
  console.log('- Bucket:', BUCKET_NAME)
  console.log('- Key:', key)
  console.log('- ContentType:', contentType)
  console.log('- Metadata:', s3Metadata)

  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    Body: file,
    ContentType: contentType,
    Metadata: Object.keys(s3Metadata).length > 0 ? s3Metadata : undefined,
  })

  console.log('📡 Sending command to Backblaze B2...')
  console.log('S3 Client config:', {
    region: s3Client.config.region,
    endpoint: s3Client.config.endpoint
  })

  try {
    const result = await s3Client.send(command)
    console.log('✅ Upload successful! Result:', result)
    console.log('File uploaded to Backblaze B2 with metadata:', s3Metadata)

    const publicUrl = getBackblazePublicUrl(key)
    console.log('🔗 Generated public URL:', publicUrl)
    return publicUrl
  } catch (error: unknown) {
    console.error('❌ Upload failed with error:', error)

    // Type-safe error handling
    const errorDetails: Record<string, unknown> = {}
    if (error && typeof error === 'object') {
      const errorObj = error as Error & {
        Code?: string;
        $metadata?: {
          httpStatusCode?: number;
          requestId?: string;
          region?: string;
        };
        $fault?: string;
      }
      errorDetails.name = errorObj.name
      errorDetails.message = errorObj.message
      errorDetails.code = errorObj.Code
      errorDetails.statusCode = errorObj.$metadata?.httpStatusCode
      errorDetails.requestId = errorObj.$metadata?.requestId
      errorDetails.fault = errorObj.$fault
      errorDetails.region = errorObj.$metadata?.region
    }
    console.error('Error details:', errorDetails)

    // Additional debugging for AccessDenied errors
    if (errorDetails.code === 'AccessDenied') {
      console.error('🔍 AccessDenied debugging:')
      console.error('- Check if App Key has writeFiles permission')
      console.error('- Check if App Key is scoped to the correct bucket')
      console.error('- Verify bucket name matches exactly')
      console.error('- Current bucket name:', BUCKET_NAME)
      console.error('- App Key ID:', process.env.BACKBLAZE_APP_KEY_ID?.substring(0, 10) + '...')
    }

    throw error
  }
}

// Generate presigned URL for file upload
export async function generatePresignedUploadUrl(
  key: string,
  contentType: string,
  uploadId?: string,
  partNumber?: number,
  expiresIn: number = 3600 // 1 hour
): Promise<string> {
  if (!isBackblazeConfigured()) {
    throw new Error('Backblaze credentials not configured')
  }

  // For multipart upload parts
  if (uploadId && partNumber) {
    const command = new UploadPartCommand({
      Bucket: BUCKET_NAME,
      Key: key,
      UploadId: uploadId,
      PartNumber: partNumber,
    })
    return await getSignedUrl(s3Client, command, { expiresIn })
  }

  // For single file upload
  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    ContentType: contentType,
  })

  return await getSignedUrl(s3Client, command, { expiresIn })
}

// Generate presigned URL for file download
export async function generatePresignedDownloadUrl(
  key: string,
  expiresIn: number = 3600 // 1 hour
): Promise<string> {
  if (!isBackblazeConfigured()) {
    throw new Error('Backblaze credentials not configured')
  }

  const command = new GetObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
  })

  return await getSignedUrl(s3Client, command, { expiresIn })
}

// Delete file from S3
export async function deleteFileFromS3(key: string): Promise<void> {
  if (!isBackblazeConfigured()) {
    throw new Error('Backblaze credentials not configured')
  }

  console.log('🗑️ Deleting file from Backblaze B2:', {
    bucket: BUCKET_NAME,
    key: key
  })

  // First check if the file exists
  try {
    const headCommand = new HeadObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    })
    await s3Client.send(headCommand)
    console.log('📁 File exists in B2, proceeding with deletion')
  } catch (headError: unknown) {
    const error = headError as { name?: string; $metadata?: { httpStatusCode?: number }; message?: string }
    if (error.name === 'NotFound' || error.$metadata?.httpStatusCode === 404) {
      console.log('📁 File not found in B2, skipping deletion (already deleted or never existed)')
      return // File doesn't exist, consider deletion successful
    } else {
      console.warn('⚠️ Could not check file existence, proceeding with deletion anyway:', error.message || 'Unknown error')
    }
  }

  const command = new DeleteObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
  })

  try {
    const result = await s3Client.send(command)
    console.log('✅ File deleted successfully from B2:', {
      key: key,
      result: result
    })
  } catch (deleteError: unknown) {
    const error = deleteError as { name?: string; $metadata?: { httpStatusCode?: number }; message?: string }
    // S3 delete operations are idempotent - deleting a non-existent file is not an error
    if (error.name === 'NoSuchKey' || error.$metadata?.httpStatusCode === 404) {
      console.log('📁 File was already deleted or never existed, considering deletion successful')
      return
    }

    console.error('❌ Failed to delete file from B2:', {
      key: key,
      bucket: BUCKET_NAME,
      error: error,
      errorName: error.name,
      statusCode: error.$metadata?.httpStatusCode
    })
    throw deleteError
  }
}

// Create multipart upload
export async function createMultipartUpload(
  key: string,
  contentType: string = 'application/octet-stream'
): Promise<{ uploadId: string }> {
  if (!isBackblazeConfigured()) {
    throw new Error('Backblaze credentials not configured')
  }

  const command = new CreateMultipartUploadCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    ContentType: contentType,
  })

  const response = await s3Client.send(command)

  if (!response.UploadId) {
    throw new Error('Failed to create multipart upload')
  }

  return { uploadId: response.UploadId }
}

// Complete multipart upload
export async function completeMultipartUpload(
  key: string,
  uploadId: string,
  parts: Array<{ PartNumber: number; ETag: string }>
): Promise<string> {
  if (!isBackblazeConfigured()) {
    throw new Error('Backblaze credentials not configured')
  }

  const command = new CompleteMultipartUploadCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    UploadId: uploadId,
    MultipartUpload: {
      Parts: parts.sort((a, b) => a.PartNumber - b.PartNumber),
    },
  })

  const response = await s3Client.send(command)
  return response.Location || getBackblazePublicUrl(key)
}

// Abort multipart upload
export async function abortMultipartUpload(
  key: string,
  uploadId: string
): Promise<void> {
  if (!isBackblazeConfigured()) {
    throw new Error('Backblaze credentials not configured')
  }

  const command = new AbortMultipartUploadCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    UploadId: uploadId,
  })

  await s3Client.send(command)
}

// Check if Backblaze is configured (keeping function name for compatibility)
export function isAWSConfigured(): boolean {
  return isBackblazeConfigured()
}

// Get Backblaze file URL (public)
export function getS3FileUrl(key: string): string {
  return getBackblazePublicUrl(key)
}

// Get public Backblaze file URL (for public buckets)
export function getPublicS3FileUrl(key: string): string {
  return getBackblazePublicUrl(key)
}

// Check if a file URL is accessible (for polling after upload)
export async function checkFileAccessibility(url: string, timeoutMs: number = 10000): Promise<boolean> {
  try {
    // Create an AbortController for timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeoutMs)

    const response = await fetch(url, {
      method: 'HEAD',
      signal: controller.signal
    })

    clearTimeout(timeoutId)
    return response.ok
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        console.log(`File accessibility check timed out after ${timeoutMs}ms:`, url)
      } else {
        console.log('File not yet accessible:', url, error.message)
      }
    } else {
      console.log('File not yet accessible:', url, 'Unknown error')
    }
    return false
  }
}

// Poll until file is accessible, with exponential backoff and strict limits
export async function waitForFileAccessibility(
  url: string,
  maxAttempts: number = 10, // Reduced to 10 attempts max
  initialDelay: number = 5000, // Start with 5 seconds
  maxTotalTime: number = 5 * 60 * 1000 // Maximum 5 minutes total
): Promise<boolean> {
  console.log(`🔄 Polling for file accessibility: ${url}`)
  console.log(`📊 Max attempts: ${maxAttempts}, Max total time: ${maxTotalTime / 1000}s`)

  const startTime = Date.now()

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    // Check if we've exceeded the maximum total time
    const elapsedTime = Date.now() - startTime
    if (elapsedTime >= maxTotalTime) {
      console.log(`⏰ Polling stopped: Maximum time limit of ${maxTotalTime / 1000}s exceeded`)
      return false
    }

    console.log(`Attempt ${attempt}/${maxAttempts} - checking file accessibility... (elapsed: ${Math.round(elapsedTime / 1000)}s)`)

    try {
      const isAccessible = await checkFileAccessibility(url, 8000) // 8 second timeout per check
      if (isAccessible) {
        console.log(`✅ File is now accessible after ${attempt} attempts (${Math.round(elapsedTime / 1000)}s)`)
        return true
      }
    } catch (error) {
      console.error(`❌ Error during accessibility check (attempt ${attempt}):`, error)
      // Continue to next attempt even if this one failed
    }

    if (attempt < maxAttempts) {
      // Exponential backoff: 5s, 7.5s, 11.25s, 16.875s, etc.
      const delay = Math.min(initialDelay * Math.pow(1.5, attempt - 1), 30000) // Cap at 30s

      // Check if the delay would exceed our time limit
      if (elapsedTime + delay >= maxTotalTime) {
        console.log(`⏰ Skipping delay: Would exceed maximum time limit`)
        break
      }

      console.log(`⏳ File not ready, waiting ${Math.round(delay / 1000)}s before next attempt...`)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  const totalElapsed = Date.now() - startTime
  console.log(`❌ File not accessible after ${maxAttempts} attempts (${Math.round(totalElapsed / 1000)}s total)`)
  return false
}

// Make a specific S3 object public
export async function makeS3ObjectPublic(key: string): Promise<void> {
  if (!isAWSConfigured()) {
    throw new Error('Backblaze is not configured')
  }

  try {
    // First check if the object exists and is not a delete marker
    try {
      await s3Client.send(new GetObjectCommand({
        Bucket: BUCKET_NAME,
        Key: key
      }))
    } catch (headError: unknown) {
      if (headError && typeof headError === 'object' && 'name' in headError && headError.name === 'NoSuchKey') {
        throw new Error(`File not found in Backblaze B2: ${key}`)
      }
      if (headError && typeof headError === 'object' && 'name' in headError && headError.name === 'MethodNotAllowed') {
        throw new Error(`File is a delete marker (deleted file): ${key}`)
      }
      throw headError
    }

    // If object exists, set ACL to public-read
    await s3Client.send(new PutObjectAclCommand({
      Bucket: BUCKET_NAME,
      Key: key,
      ACL: 'public-read'
    }))
    console.log(`Made Backblaze B2 object public: ${key}`)
  } catch (error: unknown) {
    console.error('Error making Backblaze B2 object public:', error)

    // Provide more specific error messages
    if (error && typeof error === 'object' && 'name' in error && 'ResourceType' in error &&
        error.name === 'MethodNotAllowed' && error.ResourceType === 'DeleteMarker') {
      throw new Error(`Cannot make file public: File was deleted and only a delete marker exists. Please re-upload the file.`)
    }

    throw error
  }
}

// Check if S3 object exists and is not a delete marker
export async function checkS3ObjectExists(key: string): Promise<{ exists: boolean; isDeleteMarker: boolean; versions?: unknown[] }> {
  if (!isAWSConfigured()) {
    throw new Error('Backblaze is not configured')
  }

  try {
    // List object versions to check for delete markers
    const versionsResponse = await s3Client.send(new ListObjectVersionsCommand({
      Bucket: BUCKET_NAME,
      Prefix: key,
      MaxKeys: 10
    }))

    const versions = versionsResponse.Versions || []
    const deleteMarkers = versionsResponse.DeleteMarkers || []

    // Check if there's a current version (not deleted)
    const currentVersion = versions.find(v => v.Key === key && v.IsLatest)
    const currentDeleteMarker = deleteMarkers.find(dm => dm.Key === key && dm.IsLatest)

    return {
      exists: !!currentVersion && !currentDeleteMarker,
      isDeleteMarker: !!currentDeleteMarker,
      versions: [...versions, ...deleteMarkers]
    }
  } catch (error) {
    console.error('Error checking S3 object:', error)
    return { exists: false, isDeleteMarker: false }
  }
}

// Extract S3 key from URL
export function extractS3KeyFromUrl(url: string): string | null {
  const match = url.match(/https:\/\/[^\/]+\.s3\.[^\/]+\.amazonaws\.com\/(.+)/)
  return match ? match[1] : null
}

// Validate file type for uploads
export function validateFileType(filename: string, allowedTypes: string[] = []): boolean {
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'))
  const defaultAllowedTypes = ['.zip', '.exe', '.apk', '.ipa', '.dmg', '.pkg', '.deb', '.rpm']
  const typesToCheck = allowedTypes.length > 0 ? allowedTypes : defaultAllowedTypes
  return typesToCheck.includes(extension)
}

// Get content type from filename
export function getContentTypeFromFilename(filename: string): string {
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'))
  
  const contentTypes: Record<string, string> = {
    '.zip': 'application/zip',
    '.rar': 'application/x-rar-compressed',
    '.7z': 'application/x-7z-compressed',
    '.tar.gz': 'application/gzip',
    '.exe': 'application/x-msdownload',
    '.app': 'application/octet-stream',
    '.dmg': 'application/x-apple-diskimage',
    '.deb': 'application/vnd.debian.binary-package',
    '.rpm': 'application/x-rpm',
    '.apk': 'application/vnd.android.package-archive',
    '.ipa': 'application/octet-stream',
    '.aab': 'application/octet-stream',
    '.unity3d': 'application/octet-stream',
    '.unitypackage': 'application/octet-stream',
  }

  return contentTypes[extension] || 'application/octet-stream'
}

const awsLib = {
  s3Client,
  BUCKET_NAME,
  generateS3Key,
  uploadFileToS3,
  generatePresignedUploadUrl,
  generatePresignedDownloadUrl,
  deleteFileFromS3,
  createMultipartUpload,
  completeMultipartUpload,
  abortMultipartUpload,
  isAWSConfigured,
  getS3FileUrl,
  getPublicS3FileUrl,
  makeS3ObjectPublic,
  checkS3ObjectExists,
  extractS3KeyFromUrl,
  validateFileType,
  getContentTypeFromFilename,
}

export default awsLib
