# Authentication System Documentation v1.0

## Overview
The Omnipixel MVP uses Supabase Auth for user authentication with the new API key system (publishable/secret keys) implemented in June 2025.

## Architecture

### Server-Side Authentication
- **SSR Support**: Supabase SSR package for server-side auth
- **Middleware**: Route protection and role-based access
- **API Routes**: Protected API endpoints
- **Pages**: Server-side user data fetching

### Client-Side Authentication
- **Pages**: Login/signup page with form validation
- **Components**: Direct Supabase client usage for signout

## Key Components

### Server-Side Data Fetching
Pages fetch user data server-side using Supabase SSR:

```typescript
export default async function Page() {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login')

  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  return <Component user={user} profile={profile} />
}
```

**Features:**
- Fresh data on every request
- No client-server hydration mismatches
- Better performance and SEO
- Simplified state management

### Login Page (`app/login/page.tsx`)
Unified login/signup interface with:
- Email/password authentication
- Form validation
- Error handling
- Automatic redirection

### Middleware (`middleware.ts`)
Route protection with:
- Automatic redirects for unauthenticated users
- Admin route protection
- Session validation
- Cookie management

## Supabase Configuration

### Environment Variables
```env
NEXT_PUBLIC_SUPABASE_URL=https://qrnstvofnizsgdlubtbt.supabase.co
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY=sb_publishable_...
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Client Configuration (`lib/supabase.ts`)
```typescript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabasePublishableKey = process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!

export const supabase = createClient(supabaseUrl, supabasePublishableKey)
```

### Admin Client (`lib/admin.ts`)
Server-side client with elevated permissions:
```typescript
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})
```

## New API Key System

### Migration from Legacy Keys
- **Old**: `NEXT_PUBLIC_SUPABASE_ANON_KEY` (JWT format)
- **New**: `NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY` (sb_publishable_ format)

### Benefits
- **Enhanced Security**: Better separation of concerns
- **Zero-downtime Rotation**: Keys can be rotated without downtime
- **Improved DX**: More intuitive key management
- **Future-proof**: Foundation for asymmetric JWT support

### Key Types
1. **Publishable Key**: Safe for client-side use, low privileges
2. **Secret Key**: Server-side only, elevated privileges
3. **Legacy Keys**: Still supported during transition period

## User Roles

### User Role Types
```typescript
type UserRole = 'user' | 'platform_admin'
```

### Role-Based Access
- **Users**: Can manage their own projects and builds
- **Platform Admins**: Full access to all users and projects

### Role Checking
```typescript
export async function isUserPlatformAdmin(userId: string): Promise<boolean> {
  const { data, error } = await supabaseAdmin
    .from('profiles')
    .select('role')
    .eq('id', userId)
    .single()

  return data?.role === 'platform_admin'
}
```

## Route Protection

### Protected Routes
- `/dashboard` - Requires authentication
- `/projects/*` - Requires authentication + project ownership
- `/admin/*` - Requires platform admin role

### Middleware Logic
```typescript
// Redirect unauthenticated users to login
if (!session && req.nextUrl.pathname !== '/login') {
  return NextResponse.redirect(new URL('/login', req.url))
}

// Check admin routes
if (req.nextUrl.pathname.startsWith('/admin')) {
  const { data: profile } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', session.user.id)
    .single()

  if (profile?.role !== 'platform_admin') {
    return NextResponse.redirect(new URL('/dashboard', req.url))
  }
}
```

## Authentication Flow

### Sign Up Flow
1. User submits email/password
2. Supabase creates auth user
3. Database trigger creates profile
4. User receives confirmation email
5. Email confirmation activates account

### Sign In Flow
1. User submits credentials
2. Supabase validates credentials
3. Session created and stored in cookies
4. Profile fetched from database
5. User redirected to dashboard

### Sign Out Flow
1. User clicks sign out
2. Supabase clears session
3. Cookies cleared
4. User redirected to login

## Security Features

### Row Level Security
- All database access filtered by user ID
- Platform admins bypass user-level restrictions
- Service role bypasses all RLS for backend operations

### Session Management
- Automatic token refresh
- Secure cookie storage
- Server-side session validation

### CSRF Protection
- Built-in CSRF protection via Supabase
- Secure cookie attributes
- SameSite cookie policy

## Error Handling

### Common Error Scenarios
- Invalid credentials
- Email not confirmed
- Network connectivity issues
- Session expiration

### Error Display
- User-friendly error messages
- Form validation feedback
- Loading states during auth operations

## Testing Considerations

### Test Users
- Regular user account for testing user flows
- Platform admin account for testing admin features
- Test data seeded in development environment

### Authentication Testing
- Login/logout functionality
- Route protection
- Role-based access control
- Session persistence
