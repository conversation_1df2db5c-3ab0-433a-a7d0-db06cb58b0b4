-- Build Flow Improvements Migration - Part 1
-- Adds auto_release toggle, inactive build status, and original_filename column

-- 1. Add auto_release column to projects table
ALTER TABLE projects
ADD COLUMN IF NOT EXISTS auto_release BOOLEAN DEFAULT false;

-- Add comment to the auto_release column
COMMENT ON COLUMN projects.auto_release IS 'When enabled, new builds are automatically activated upon upload completion';

-- 2. Add 'inactive' status to build_status enum
ALTER TYPE build_status ADD VALUE IF NOT EXISTS 'inactive';

-- 3. Add original_filename column to builds table
ALTER TABLE builds
ADD COLUMN IF NOT EXISTS original_filename TEXT;

-- 4. Update existing projects to have auto_release = false (explicit default)
UPDATE projects
SET auto_release = false
WHERE auto_release IS NULL;


