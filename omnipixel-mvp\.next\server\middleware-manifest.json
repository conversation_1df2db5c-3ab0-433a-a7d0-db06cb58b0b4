{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_6813fc78._.js", "server/edge/chunks/[root-of-the-server]__c2258e89._.js", "server/edge/chunks/edge-wrapper_cfa3714e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3QuKliZi9urtbizTiSNs8TuZabyDOYtXickNI45x4xs=", "__NEXT_PREVIEW_MODE_ID": "22ccf035a4260cd4cfe3b987ed6d0335", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8b133897551afec9c399ca845d96d22da8b434e889f218e8594a7c5600146028", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "371b2d47a48325f69ca9371dcb8fb35785c999f71fd0b3c31fc80c4af3f1e0b7"}}}, "sortedMiddleware": ["/"], "functions": {}}