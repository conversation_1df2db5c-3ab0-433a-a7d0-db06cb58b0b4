{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_6813fc78._.js", "server/edge/chunks/[root-of-the-server]__c2258e89._.js", "server/edge/chunks/edge-wrapper_cfa3714e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3QuKliZi9urtbizTiSNs8TuZabyDOYtXickNI45x4xs=", "__NEXT_PREVIEW_MODE_ID": "9833f5afecc4061f470f14664808dcbb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e32090b14056c9f83abed1a2036cd30961177ca332860ec2a398142d2ffea007", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7457b83b81e52b43035cb058bcc5efcf5b4c7c4629b322a702f03ff6aeddd8c3"}}}, "sortedMiddleware": ["/"], "functions": {}}