{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_6813fc78._.js", "server/edge/chunks/[root-of-the-server]__c2258e89._.js", "server/edge/chunks/edge-wrapper_cfa3714e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3QuKliZi9urtbizTiSNs8TuZabyDOYtXickNI45x4xs=", "__NEXT_PREVIEW_MODE_ID": "f4d527f0c46b6edd54318b9bbfc5cdcf", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "070bf4a25056274934b814ded1309d6750f3a61ce0c9c9dfec1bbd72ec0cd99c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1b20ba564e40d58bfe551e2394149cc07595a1a424d11951cfa04fad2d3ffd7f"}}}, "sortedMiddleware": ["/"], "functions": {}}