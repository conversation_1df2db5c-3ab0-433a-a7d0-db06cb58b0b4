import { renderHook, act } from '@testing-library/react'
import { useFileUpload } from '@/hooks/use-file-upload'

// Mock fetch
const mockFetch = jest.fn()
global.fetch = mockFetch

describe('useFileUpload Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockFetch.mockClear()
  })

  it('initializes with correct default state', () => {
    const { result } = renderHook(() => useFileUpload())
    
    expect(result.current.uploading).toBe(false)
    expect(result.current.progress).toBe(null)
    expect(result.current.error).toBe(null)
  })

  it('clears error when clearError is called', () => {
    const { result } = renderHook(() => useFileUpload())
    
    // Simulate an error state
    act(() => {
      result.current.clearError()
    })
    
    expect(result.current.error).toBe(null)
  })

  describe('uploadFileWithProgress', () => {
    it('uses single upload for small files', async () => {
      const { result } = renderHook(() => useFileUpload())
      
      // Mock successful responses
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            presignedUrl: 'https://s3.amazonaws.com/test-bucket/test-key',
            s3Key: 'test-key',
          }),
        })
        .mockResolvedValueOnce({
          ok: true,
          headers: new Map([['ETag', '"test-etag"']]),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            build: { id: 'build-1' },
            fileUrl: 'https://s3.amazonaws.com/test-bucket/test-key',
          }),
        })

      const smallFile = new File(['test content'], 'small-file.zip', {
        type: 'application/zip',
      })
      Object.defineProperty(smallFile, 'size', { value: 50 * 1024 * 1024 }) // 50MB

      await act(async () => {
        await result.current.uploadFileWithProgress(smallFile, 'project-1')
      })

      // Should use single upload (3 calls: presigned URL, upload, complete)
      expect(mockFetch).toHaveBeenCalledTimes(3)
    })

    it('uses multipart upload for large files', async () => {
      const { result } = renderHook(() => useFileUpload())
      
      // Mock multipart upload responses
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            uploadId: 'test-upload-id',
            s3Key: 'test-key',
            partSize: 5 * 1024 * 1024,
            totalParts: 2,
          }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            presignedUrl: 'https://s3.amazonaws.com/test-bucket/test-key?partNumber=1',
          }),
        })
        .mockResolvedValueOnce({
          ok: true,
          headers: new Map([['ETag', '"part1-etag"']]),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            presignedUrl: 'https://s3.amazonaws.com/test-bucket/test-key?partNumber=2',
          }),
        })
        .mockResolvedValueOnce({
          ok: true,
          headers: new Map([['ETag', '"part2-etag"']]),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            build: { id: 'build-1' },
            fileUrl: 'https://s3.amazonaws.com/test-bucket/test-key',
          }),
        })

      const largeFile = new File(['test content'], 'large-file.zip', {
        type: 'application/zip',
      })
      Object.defineProperty(largeFile, 'size', { value: 200 * 1024 * 1024 }) // 200MB

      await act(async () => {
        await result.current.uploadFileWithProgress(largeFile, 'project-1')
      })

      // Should use multipart upload (6 calls: initiate, 2x presigned URL, 2x upload, complete)
      expect(mockFetch).toHaveBeenCalledTimes(6)
    })

    it('tracks progress during upload', async () => {
      const { result } = renderHook(() => useFileUpload())
      
      const progressCallback = jest.fn()
      
      // Mock responses
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            presignedUrl: 'https://s3.amazonaws.com/test-bucket/test-key',
            s3Key: 'test-key',
          }),
        })
        .mockResolvedValueOnce({
          ok: true,
          headers: new Map([['ETag', '"test-etag"']]),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            build: { id: 'build-1' },
            fileUrl: 'https://s3.amazonaws.com/test-bucket/test-key',
          }),
        })

      const file = new File(['test content'], 'test-file.zip', {
        type: 'application/zip',
      })
      Object.defineProperty(file, 'size', { value: 1024 })

      await act(async () => {
        await result.current.uploadFileWithProgress(file, 'project-1', progressCallback)
      })

      expect(progressCallback).toHaveBeenCalled()
    })

    it('handles upload errors correctly', async () => {
      const { result } = renderHook(() => useFileUpload())
      
      // Mock failed response
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve({ error: 'Upload failed' }),
      })

      const file = new File(['test content'], 'test-file.zip', {
        type: 'application/zip',
      })

      await act(async () => {
        try {
          await result.current.uploadFileWithProgress(file, 'project-1')
        } catch (error) {
          // Expected to throw
        }
      })

      expect(result.current.error).toBe('Upload failed')
      expect(result.current.uploading).toBe(false)
    })

    it('sets uploading state correctly', async () => {
      const { result } = renderHook(() => useFileUpload())
      
      // Mock slow response
      let resolveUpload: (value: any) => void
      const uploadPromise = new Promise(resolve => {
        resolveUpload = resolve
      })
      
      mockFetch.mockReturnValueOnce(uploadPromise)

      const file = new File(['test content'], 'test-file.zip', {
        type: 'application/zip',
      })

      // Start upload
      act(() => {
        result.current.uploadFileWithProgress(file, 'project-1')
      })

      // Should be uploading
      expect(result.current.uploading).toBe(true)

      // Resolve upload
      act(() => {
        resolveUpload!({
          ok: true,
          json: () => Promise.resolve({
            presignedUrl: 'https://s3.amazonaws.com/test-bucket/test-key',
            s3Key: 'test-key',
          }),
        })
      })

      // Mock remaining responses
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          headers: new Map([['ETag', '"test-etag"']]),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            build: { id: 'build-1' },
            fileUrl: 'https://s3.amazonaws.com/test-bucket/test-key',
          }),
        })

      await act(async () => {
        // Wait for upload to complete
        await new Promise(resolve => setTimeout(resolve, 100))
      })
    })
  })

  describe('uploadFile', () => {
    it('calls uploadFileWithProgress without progress callback', async () => {
      const { result } = renderHook(() => useFileUpload())
      
      // Mock successful responses
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            presignedUrl: 'https://s3.amazonaws.com/test-bucket/test-key',
            s3Key: 'test-key',
          }),
        })
        .mockResolvedValueOnce({
          ok: true,
          headers: new Map([['ETag', '"test-etag"']]),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            build: { id: 'build-1' },
            fileUrl: 'https://s3.amazonaws.com/test-bucket/test-key',
          }),
        })

      const file = new File(['test content'], 'test-file.zip', {
        type: 'application/zip',
      })

      await act(async () => {
        await result.current.uploadFile(file, 'project-1')
      })

      expect(mockFetch).toHaveBeenCalledTimes(3)
    })
  })
})
