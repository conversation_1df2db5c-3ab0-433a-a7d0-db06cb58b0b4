// app/admin/page.tsx
import { redirect } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'
import AdminClient from './client'
import { StreamPixelConfig } from 'streampixelsdk'

interface AdminProject {
  id: string
  name: string
  stream_project_id: string
  user_id: string
  config: StreamPixelConfig
  created_at: string
  updated_at: string
  profiles: {
    email: string
    role: string
  }
  builds: Array<{
    id: string
    filename: string
    original_filename?: string
    version: number
    created_at: string
  }>
}


interface RawProject {
  id: string
  name: string
  auto_release: boolean
  stream_project_id: string
  user_id: string
  config: StreamPixelConfig
  created_at: string
  updated_at: string
  profiles: Array<{
    email: string
    role: string
  }>
  builds: Array<{
    id: string
    filename: string
    original_filename?: string
    version: number
    created_at: string
  }>
}

// Helper: Normalize project structure for client use (to match original types)
function normalizeProjects(rawProjects: RawProject[]): AdminProject[] {
  return rawProjects.map((p) => ({
    id: p.id,
    name: p.name,
    stream_project_id: p.stream_project_id,
    user_id: p.user_id,
    config: p.config,
    created_at: p.created_at,
    updated_at: p.updated_at,
    profiles: {
      email: p.profiles[0]?.email || '',
      role: p.profiles[0]?.role || '',
    },
    builds: (p.builds || []).map((b) => ({
      id: b.id,
      filename: b.filename,
      original_filename: b.original_filename,
      version: b.version,
      created_at: b.created_at,
    })),
  }))
}

export default async function AdminPage() {
  const supabase = await createClient()

  // Auth
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login')

  // Profile
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()
  if (!profile || profile.role !== 'platform_admin') redirect('/dashboard')


  // Projects (with builds & profiles)
  const { data: rawProjects, error } = await supabase
    .from('projects')
    .select(`
      id, name, auto_release, stream_project_id, user_id, config, created_at, updated_at,
      profiles:profiles!projects_user_id_fkey(email, role),
      builds(id, filename, original_filename, version, created_at)
    `)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching projects:', error)
    throw new Error('Failed to fetch projects')
  }

  const projects = normalizeProjects(rawProjects || [])

  return (
    <AdminClient
      user={user}
      profile={profile}
      initialProjects={projects}
    />
  )
}
