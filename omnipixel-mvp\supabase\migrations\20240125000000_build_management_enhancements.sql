-- Add build status and management fields to builds table
CREATE TYPE build_status AS ENUM (
    'uploading',
    'processing', 
    'active',
    'archived',
    'failed'
);

-- Add new columns to builds table
ALTER TABLE builds 
ADD COLUMN status build_status NOT NULL DEFAULT 'uploading',
ADD COLUMN is_current BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN file_size BIGINT,
ADD COLUMN streampixel_build_id TEXT,
ADD COLUMN streampixel_status TEXT,
ADD COLUMN error_message TEXT,
ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create index for build status queries
CREATE INDEX idx_builds_status ON builds(status);
CREATE INDEX idx_builds_is_current ON builds(is_current);
CREATE INDEX idx_builds_project_status ON builds(project_id, status);

-- Function to enforce 2-build limit per project
CREATE OR REPLACE FUNCTION enforce_build_limit()
RETURNS TRIGGER AS $$
DECLARE
    build_count INTEGER;
    oldest_build_id UUID;
BEGIN
    -- Count current builds for this project (excluding failed builds)
    SELECT COUNT(*) INTO build_count
    FROM builds 
    WHERE project_id = NEW.project_id 
    AND status != 'failed';
    
    -- If we're at the limit (2 builds), archive the oldest non-current build
    IF build_count >= 2 THEN
        -- Find the oldest build that is not current
        SELECT id INTO oldest_build_id
        FROM builds 
        WHERE project_id = NEW.project_id 
        AND status != 'failed'
        AND is_current = false
        ORDER BY created_at ASC
        LIMIT 1;
        
        -- If we found an old build, archive it
        IF oldest_build_id IS NOT NULL THEN
            UPDATE builds 
            SET status = 'archived', updated_at = NOW()
            WHERE id = oldest_build_id;
        ELSE
            -- If no non-current build found, archive the oldest build
            SELECT id INTO oldest_build_id
            FROM builds 
            WHERE project_id = NEW.project_id 
            AND status != 'failed'
            ORDER BY created_at ASC
            LIMIT 1;
            
            IF oldest_build_id IS NOT NULL THEN
                UPDATE builds 
                SET status = 'archived', is_current = false, updated_at = NOW()
                WHERE id = oldest_build_id;
            END IF;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to enforce build limit
CREATE TRIGGER trigger_enforce_build_limit
    BEFORE INSERT ON builds
    FOR EACH ROW
    EXECUTE FUNCTION enforce_build_limit();

-- Function to set build as current and unset others
CREATE OR REPLACE FUNCTION set_current_build()
RETURNS TRIGGER AS $$
BEGIN
    -- If this build is being set as current
    IF NEW.is_current = true AND (OLD IS NULL OR OLD.is_current = false) THEN
        -- Unset all other builds in this project as current
        UPDATE builds 
        SET is_current = false, updated_at = NOW()
        WHERE project_id = NEW.project_id 
        AND id != NEW.id 
        AND is_current = true;
        
        -- Set this build as active if it's not already
        IF NEW.status != 'active' THEN
            NEW.status = 'active';
        END IF;
    END IF;
    
    -- Update the updated_at timestamp
    NEW.updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to manage current build status
CREATE TRIGGER trigger_set_current_build
    BEFORE UPDATE ON builds
    FOR EACH ROW
    EXECUTE FUNCTION set_current_build();

-- Function to automatically set first build as current
CREATE OR REPLACE FUNCTION set_first_build_current()
RETURNS TRIGGER AS $$
DECLARE
    current_build_count INTEGER;
BEGIN
    -- Count current builds for this project
    SELECT COUNT(*) INTO current_build_count
    FROM builds 
    WHERE project_id = NEW.project_id 
    AND is_current = true
    AND status != 'failed';
    
    -- If no current build exists, make this one current
    IF current_build_count = 0 THEN
        NEW.is_current = true;
        NEW.status = 'active';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to set first build as current
CREATE TRIGGER trigger_set_first_build_current
    BEFORE INSERT ON builds
    FOR EACH ROW
    EXECUTE FUNCTION set_first_build_current();

-- Update existing builds to have proper status
UPDATE builds SET 
    status = 'active',
    is_current = true,
    updated_at = NOW()
WHERE id IN (
    SELECT DISTINCT ON (project_id) id
    FROM builds
    ORDER BY project_id, version DESC
);

-- Set all other builds as archived
UPDATE builds SET 
    status = 'archived',
    is_current = false,
    updated_at = NOW()
WHERE status = 'uploading';

-- Add RLS policies for new columns
-- Users can view builds for their projects
CREATE POLICY "Users can view builds for their projects" ON builds
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = builds.project_id 
            AND projects.user_id = auth.uid()
        )
    );

-- Users can update builds for their projects (for status changes)
CREATE POLICY "Users can update builds for their projects" ON builds
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = builds.project_id 
            AND projects.user_id = auth.uid()
        )
    );

-- Platform admins can view all builds
CREATE POLICY "Platform admins can view all builds" ON builds
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'platform_admin'
        )
    );

-- Platform admins can update all builds
CREATE POLICY "Platform admins can update all builds" ON builds
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'platform_admin'
        )
    );

-- Platform admins can delete all builds
CREATE POLICY "Platform admins can delete all builds" ON builds
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'platform_admin'
        )
    );
