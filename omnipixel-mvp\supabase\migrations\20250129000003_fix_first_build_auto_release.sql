-- Fix the auto-release logic to only trigger StreamPixel when auto_release is enabled
-- Remove the "first build always processing" logic

-- Drop existing trigger and function
DROP TRIGGER IF EXISTS trigger_set_first_build_current ON builds;
DROP FUNCTION IF EXISTS set_first_build_current();

-- Create updated function that only processes when auto_release is enabled
CREATE OR REPLACE FUNCTION set_first_build_current()
RETURNS TRIGGER AS $$
DECLARE
    total_builds INTEGER;
    project_auto_release BOOLEAN;
    current_build_count INTEGER;
BEGIN
    -- Get project auto_release setting
    SELECT auto_release INTO project_auto_release
    FROM projects 
    WHERE id = NEW.project_id;
    
    -- Count total builds for this project (excluding failed)
    SELECT COUNT(*) INTO total_builds
    FROM builds 
    WHERE project_id = NEW.project_id 
    AND status NOT IN ('failed', 'archived');
    
    -- Count current builds for this project
    SELECT COUNT(*) INTO current_build_count
    FROM builds 
    WHERE project_id = NEW.project_id 
    AND is_current = true
    AND status NOT IN ('failed', 'archived');
    
    -- If no current build exists (first build scenario)
    IF current_build_count = 0 THEN
        -- Only set to processing if auto_release is enabled
        IF project_auto_release = true THEN
            NEW.is_current = false; -- Wait for StreamPixel webhook
            NEW.status = 'processing'; -- Will be sent to StreamPixel
        ELSE
            -- Manual activation: keep this build inactive
            NEW.status = 'inactive';
            NEW.is_current = false;
        END IF;
    ELSE
        -- If there's already a current build, this one should be inactive by default
        -- unless auto_release is enabled
        IF project_auto_release = true THEN
            -- Auto-release: DON'T deactivate current build yet, wait for StreamPixel
            -- Set new build to processing, but not current until webhook confirms
            NEW.is_current = false; -- Wait for StreamPixel webhook
            NEW.status = 'processing'; -- Will be sent to StreamPixel
        ELSE
            -- Manual activation: keep this build inactive
            NEW.status = 'inactive';
            NEW.is_current = false;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate trigger with updated function
CREATE TRIGGER trigger_set_first_build_current
    BEFORE INSERT ON builds
    FOR EACH ROW
    EXECUTE FUNCTION set_first_build_current();
