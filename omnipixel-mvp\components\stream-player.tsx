'use client'

import { useState, useEffect, useRef } from 'react'
import { StreamPixelApplication } from 'streampixelsdk'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import {
  Play,
  Square,
  Volume2,
  VolumeX,
  Maximize,
  Loader2,
  AlertCircle,
  ExternalLink,
  Code,
  Lock,
  Unlock
} from 'lucide-react'
import { Project } from '@/lib/supabase'

interface StreamPlayerProps {
  projectId: string
  buildId?: string
  className?: string
  config?: StreamPlayerConfig
  showControls?: boolean
  showHeader?: boolean
  showEmbedButton?: boolean
  width?: number
  height?: number
  isEmbedded?: boolean
  enableIframeComms?: boolean
  projectData?: Project // Pre-fetched project data for embed contexts
}

interface StreamPlayerConfig {
  autoConnect?: boolean
  touchInput?: boolean
  keyBoardInput?: boolean
  resolutionMode?: string
  maxStreamQuality?: string
  primaryCodec?: string
  fallBackCodec?: string
  isPasswordProtected?: boolean
  password?: string
  loadingMessage?: string
  connectingMessage?: string
  disconnectedMessage?: string
  reconnectingMessage?: string
  errorMessage?: string
  connectButtonText?: string
}

export function StreamPlayer({
  projectId,
  buildId,
  className = '',
  config: propConfig,
  showControls = true,
  showHeader = true,
  showEmbedButton = true,
  width = 1280,
  height = 720,
  isEmbedded = false,
  enableIframeComms = false,
  projectData: preloadedProjectData
}: StreamPlayerProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [streamProjectId, setStreamProjectId] = useState<string | null>(null)
  const [config, setConfig] = useState<StreamPlayerConfig | null>(null)
  const [projectData, setProjectData] = useState<Project | null>(preloadedProjectData || null)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [streamStatus, setStreamStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')
  const [loadingMessage, setLoadingMessage] = useState<string>('Loading stream...')

  const [isPasswordPromptOpen, setIsPasswordPromptOpen] = useState(false)
  const [enteredPassword, setEnteredPassword] = useState('')
  const [isPasswordValid, setIsPasswordValid] = useState(false)
  const [showEmbedDialog, setShowEmbedDialog] = useState(false)

  const containerRef = useRef<HTMLDivElement>(null) // for overlays/background
  const sdkMountRef = useRef<HTMLDivElement>(null)   // ONLY for SDK-injected DOM

  const streamRef = useRef<any>(null)
  const playerRef = useRef<any>(null)
  const uiControlRef = useRef<any>(null)
  const isInitializedRef = useRef<boolean>(false)
  const isInitializingRef = useRef<boolean>(false)

  // Project data and config loader
  useEffect(() => {
    const fetchProjectData = async () => {
      try {
        setIsLoading(true)
        setError(null)
        const response = await fetch(`/api/projects/${projectId}`)
        if (!response.ok) {
          // Handle different error cases
          if (response.status === 404) {
            throw new Error('Project not found')
          } else if (response.status === 401) {
            throw new Error('Authentication required')
          } else {
            throw new Error('Failed to fetch project data')
          }
        }
        const responseData = await response.json()
        const data = responseData.project || responseData
        setProjectData(data)
        setStreamProjectId(data.stream_project_id)
        const finalConfig = propConfig || data.config || {}
        setConfig(finalConfig)
        if (!data.stream_project_id) throw new Error('No Stream Service ID found in project data')
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load project configuration'
        setError(errorMessage)
        console.error('StreamPlayer: Failed to fetch project data:', err)
      } finally {
        setIsLoading(false)
      }
    }

    const initializeFromPreloadedData = () => {
      try {
        setIsLoading(true)
        setError(null)
        if (!preloadedProjectData) throw new Error('No project data provided')
        setStreamProjectId(preloadedProjectData.stream_project_id)
        const finalConfig = propConfig || preloadedProjectData.config || {}
        setConfig(finalConfig)
        if (!preloadedProjectData.stream_project_id) throw new Error('No Stream Service ID found in project data')
      } catch (err) {
        setError('Failed to load project configuration')
      } finally {
        setIsLoading(false)
      }
    }

    if (projectId) {
      if (preloadedProjectData) {
        // Use preloaded data (for embed contexts)
        initializeFromPreloadedData()
      } else if (projectId.startsWith('test-')) {
        // Handle test/demo projects without API calls
        setIsLoading(false)
        setError(null)
        setStreamProjectId('demo-stream-id')
        const finalConfig = propConfig || {}
        setConfig(finalConfig)
      } else {
        // Fetch data from API (for authenticated contexts)
        fetchProjectData()
      }
    }
  }, [projectId, propConfig, preloadedProjectData])

  // Clean up SDK DOM on unmount/disconnect
  useEffect(() => {
    return () => { cleanup() }
  }, [])

  // Auto-connect for embedded mode
  useEffect(() => {
    if (config?.autoConnect && streamProjectId && !isPlaying && !isInitializingRef.current) {
      console.log('🚀 Auto-connecting stream...', {
        autoConnect: config?.autoConnect,
        streamProjectId,
        isPlaying,
        isInitializing: isInitializingRef.current
      })
      handlePlay()
    }
  }, [config?.autoConnect, streamProjectId, isPlaying])

  // Prepare messages
  const messages = {
    loading: config?.loadingMessage ?? 'Loading stream...',
    connecting: config?.connectingMessage ?? 'Connecting to stream...',
    disconnected: config?.disconnectedMessage ?? 'Stream disconnected',
    reconnecting: config?.reconnectingMessage ?? 'Reconnecting...',
    error: config?.errorMessage ?? 'Stream error occurred',
    connectButton: config?.connectButtonText ?? 'Connect to Stream'
  }

  const createStreamConfig = () => {
    if (!streamProjectId || !config) return null
    return {
      AutoConnect: true,
      appId: streamProjectId,
      touchInput: config?.touchInput ?? true,
      keyBoardInput: config?.keyBoardInput ?? true,
      resolutionMode: (config?.resolutionMode as "Dynamic Resolution Mode" | "Fixed Resolution Mode" | "Crop on Resize Mode") ?? "Dynamic Resolution Mode",
      maxStreamQuality: config?.maxStreamQuality ?? "1080p (1920x1080)",
      primaryCodec: (config?.primaryCodec as "H264" | "VP8" | "AV1" | "VP9") ?? "H264",
      fallBackCodec: (config?.fallBackCodec as "H264" | "VP8" | "AV1" | "VP9") ?? "VP8"
    }
  }

 const initializeStream = async () => {
  if (isInitializingRef.current) return // guard against parallel inits
  isInitializingRef.current = true
  setIsLoading(true)
  setError(null)

  try {
    const streamConfig = createStreamConfig()
    if (!streamConfig) throw new Error('Cannot create stream config: missing required data')
    const { appStream, pixelStreaming, UIControl } = await StreamPixelApplication(streamConfig)
    streamRef.current = appStream
    playerRef.current = pixelStreaming
    uiControlRef.current = UIControl

    // Inject SDK DOM
    if (sdkMountRef.current && appStream.rootElement) {
      if (appStream.rootElement.parentNode) {
        appStream.rootElement.parentNode.removeChild(appStream.rootElement)
      }
      sdkMountRef.current.innerHTML = ''
      sdkMountRef.current.appendChild(appStream.rootElement)
    }

    setupEventListeners()
    isInitializedRef.current = true
    // Keep loading true - only onVideoInitialized will set it to false
  } catch (err: unknown) {
    setError(err instanceof Error? err.message : 'Failed to initialize stream')
    setIsLoading(false)
  } finally {
    isInitializingRef.current = false
  }
}
  const cleanup = () => {
   try {
  


    if (streamRef.current?.stream?.disconnect) streamRef.current.stream.disconnect()
    if (playerRef.current?.disconnect) playerRef.current.disconnect()

  
    if (sdkMountRef.current) sdkMountRef.current.innerHTML = ''
  } catch (err) {}
  // Nullify refs
  streamRef.current = null
  playerRef.current = null
  uiControlRef.current = null
  isInitializedRef.current = false
  isInitializingRef.current = false
  }

  // Iframe communication system
  const sendMessageToParent = (type: string, data: any = {}) => {
    if (enableIframeComms && typeof window !== 'undefined' && window.parent !== window) {
      try {
        window.parent.postMessage({
          type: `omnipixel-${type}`,
          projectId,
          buildId,
          timestamp: new Date().toISOString(),
          ...data
        }, '*')
      } catch (error) {
        console.warn('Failed to send message to parent:', error)
      }
    }
  }

  // Listen for messages from parent iframe
  useEffect(() => {
    if (!enableIframeComms || typeof window === 'undefined') return

    const handleMessage = (event: MessageEvent) => {
      if (!event.data?.type?.startsWith('omnipixel-')) return

      const { type, data } = event.data
      console.log('📨 Received message from parent:', type, data)

      switch (type) {
        case 'omnipixel-connect':
          if (!isPlaying) {
            handlePlay()
          }
          break
        case 'omnipixel-disconnect':
          if (isPlaying) {
            handlePause()
          }
          break
        case 'omnipixel-mute':
          setIsMuted(true)
          break
        case 'omnipixel-unmute':
          setIsMuted(false)
          break
        case 'omnipixel-fullscreen':
          setIsFullscreen(true)
          break
        case 'omnipixel-exit-fullscreen':
          setIsFullscreen(false)
          break
        case 'omnipixel-send-input':
          // Forward input to StreamPixel
          if (playerRef.current && data.input) {
            // Handle different input types
            if (data.input.type === 'keyboard') {
              // Send keyboard input to StreamPixel
            } else if (data.input.type === 'mouse') {
              // Send mouse input to StreamPixel
            }
          }
          break
      }
    }

    window.addEventListener('message', handleMessage)
    return () => window.removeEventListener('message', handleMessage)
  }, [enableIframeComms, isPlaying, projectId, buildId])

  // Send status updates to parent
  useEffect(() => {
    if (enableIframeComms) {
      sendMessageToParent('status-change', {
        isLoading,
        isPlaying,
        streamStatus,
        error,
        isMuted,
        isFullscreen
      })
    }
  }, [enableIframeComms, isLoading, isPlaying, streamStatus, error, isMuted, isFullscreen])

  // Prevent accidental keyboard triggers for buttons
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Prevent spacebar from triggering buttons when not focused on input elements
      if (e.code === 'Space' && e.target && !(e.target as HTMLElement).matches('input, textarea, [contenteditable]')) {
        e.preventDefault()
        e.stopPropagation()
      }

      // Prevent Enter key from triggering buttons when not focused on input elements
      if (e.code === 'Enter' && e.target && !(e.target as HTMLElement).matches('input, textarea, [contenteditable], button')) {
        e.preventDefault()
        e.stopPropagation()
      }
    }

    // Add event listener to the document
    document.addEventListener('keydown', handleKeyDown, true)

    return () => {
      document.removeEventListener('keydown', handleKeyDown, true)
    }
  }, [])

  // Event listeners (simplified for clarity, you can add more as needed)
  const setupEventListeners = () => {
    if (!streamRef.current) return

    streamRef.current.onConnectAction = () => {
      console.log('🔗 Stream: Connect action triggered')
      setStreamStatus('connecting')
      setIsLoading(true)
      setLoadingMessage(config?.connectingMessage ?? 'Connecting to stream...')
      sendMessageToParent('connect-started')
    }

    streamRef.current.onWebRtcConnecting = () => {
      console.log('🌐 Stream: WebRTC connecting')
      setStreamStatus('connecting')
      setLoadingMessage(config?.connectingMessage ?? 'Establishing connection...')
      sendMessageToParent('webrtc-connecting')
    }

    streamRef.current.onWebRtcConnected = () => {
      console.log('✅ Stream: WebRTC connected')
      setStreamStatus('connecting')
      setLoadingMessage('Initializing video stream...')
      // Don't hide loading yet - wait for video to be ready
      sendMessageToParent('webrtc-connected')
    }

    streamRef.current.onVideoInitialized = () => {
      console.log('🎥 Stream: Video initialized and ready')
      console.log('🔄 Setting loading state to false - video ready')
      setIsLoading(false) // Hide loading when video is actually ready
      setStreamStatus('connected')
      setIsPlaying(true)
      sendMessageToParent('video-initialized')
    }

    streamRef.current.onDisconnect = () => {
      console.log('🔌 Stream: Disconnected')
      setIsPlaying(false)
      setIsLoading(false)
      setStreamStatus('disconnected')
      setLoadingMessage(config?.disconnectedMessage ?? 'Stream disconnected')
      sendMessageToParent('disconnected')

      // Use requestAnimationFrame for React-safe cleanup
      requestAnimationFrame(() => {
        cleanup()
      })
    }

    // Additional Stream Service events for iframe communication
    if (streamRef.current.onDataChannelOpen) {
      streamRef.current.onDataChannelOpen = () => {
        console.log('📡 Stream: Data channel opened')
        sendMessageToParent('data-channel-open')
      }
    }

    if (streamRef.current.onDataChannelMessage) {
      streamRef.current.onDataChannelMessage = (message: any) => {
        console.log('📨 Stream: Data channel message', message)
        sendMessageToParent('data-channel-message', { message })
      }
    }

    // Unreal Engine specific events
    if (streamRef.current.onUnrealMessage) {
      streamRef.current.onUnrealMessage = (message: any) => {
        console.log('🎮 Unreal Engine message:', message)
        sendMessageToParent('unreal-message', { message })
      }
    }
  }

  // Control handlers
  const handlePlay = async () => {
    console.log('▶️ Play button clicked')
    sendMessageToParent('play-requested')

    console.log('🔄 Setting loading state to true')
    setIsLoading(true)
    setLoadingMessage(config?.loadingMessage ?? 'Initializing stream...')

    cleanup()
    await initializeStream()
    setStreamStatus('connecting')
    setIsPlaying(true)
  }

  const handlePause = () => {
    console.log('⏸️ Pause button clicked')
    sendMessageToParent('pause-requested')

    playerRef.current?.disconnect()
    streamRef.current?.stream?.disconnect()

    // Use requestAnimationFrame for React-safe state updates
    requestAnimationFrame(() => {
      setIsPlaying(false)
      setStreamStatus('disconnected')
    })

    cleanup()
    window.location.reload();
  }

  const handleMute = () => {
    console.log('🔇 Mute button clicked')
    if (uiControlRef.current) {
      uiControlRef.current.toggleAudio()
      setIsMuted(!isMuted)
      sendMessageToParent('mute-toggled', { isMuted: !isMuted })
    }
  }
  const handleFullscreen = () => {
    if (!containerRef.current) return
    if (!isFullscreen) {
      containerRef.current.requestFullscreen?.()
    } else {
      document.exitFullscreen?.()
    }
    setIsFullscreen(!isFullscreen)
  }

  // Password dialog handler
  const handlePasswordSubmit = () => {
    if (enteredPassword === config?.password) {
      setIsPasswordValid(true)
      setIsPasswordPromptOpen(false)
    } else {
      setError('Invalid password')
    }
  }

  // Embed code utilities
  const generateEmbedCode = () => {
    const origin = typeof window !== 'undefined' ? window.location.origin : 'https://your-domain.com'
    const params = new URLSearchParams()

    // Add control visibility parameters
    if (!showControls) params.set('hideControls', 'true')
    if (!showHeader) params.set('hideHeader', 'true')
    if (!showEmbedButton) params.set('hideEmbedButton', 'true')
    if (config?.autoConnect) params.set('autoConnect', 'true')

    // Add password parameter if stream is password protected
    if (config?.isPasswordProtected && config?.password) {
      params.set('password', config.password)
    }

    const queryString = params.toString()
    const embedUrl = `${origin}/embed/${projectId}${queryString ? `?${queryString}` : ''}`

    return `<!-- OmniPixel Interactive Stream Embed -->
<iframe
  src="${embedUrl}"
  width="${width}"
  height="${height}"
  frameborder="0"
  allowfullscreen
  allow="camera; microphone; fullscreen"
  style="border: none; border-radius: 8px;">
</iframe>

<!-- Optional: Listen for iframe events -->
<script>
window.addEventListener('message', function(event) {
  if (event.data?.type?.startsWith('omnipixel-')) {
    console.log('Stream event:', event.data.type, event.data);

    // Handle specific events
    switch(event.data.type) {
      case 'omnipixel-status-change':
        console.log('Stream status:', event.data.streamStatus);
        break;
      case 'omnipixel-webrtc-connected':
        console.log('Stream connected successfully');
        break;
      case 'omnipixel-unreal-message':
        console.log('Unreal Engine message:', event.data.message);
        break;
    }
  }
});

// Optional: Send commands to the stream
function connectStream() {
  document.querySelector('iframe').contentWindow.postMessage({
    type: 'omnipixel-connect'
  }, '*');
}

function disconnectStream() {
  document.querySelector('iframe').contentWindow.postMessage({
    type: 'omnipixel-disconnect'
  }, '*');
}
</script>`
  }
  const copyEmbedCode = async () => {
    try {
      await navigator.clipboard.writeText(generateEmbedCode())
      alert('Embed code copied to clipboard!')
    } catch { }
  }
  const openInNewTab = () => {
    if (typeof window !== 'undefined') {
      const url = `${window.location.origin}/projects/${projectId}`
      window.open(url, '_blank', 'noopener,noreferrer')
    }
  }

  const getStatusColor = () => {
    switch (streamStatus) {
      case 'connected': return 'bg-green-100 text-green-800'
      case 'connecting': return 'bg-yellow-100 text-yellow-800'
      case 'disconnected': return 'bg-gray-100 text-gray-800'
      case 'error': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }
  const getStatusText = () => {
    switch (streamStatus) {
      case 'connected': return 'Connected'
      case 'connecting': return 'Connecting...'
      case 'disconnected': return 'Disconnected'
      case 'error': return 'Error'
      default: return 'Unknown'
    }
  }

  // Password protection UI
  if (config?.isPasswordProtected && !isPasswordValid && isPasswordPromptOpen) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <Lock className="h-12 w-12 mx-auto text-gray-400" />
            <h3 className="text-lg font-medium">Password Protected</h3>
            <p className="text-gray-600">This stream requires a password to access.</p>
            <div className="max-w-sm mx-auto space-y-3">
              <Input
                type="password"
                placeholder="Enter password"
                value={enteredPassword}
                onChange={(e) => setEnteredPassword(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handlePasswordSubmit()}
              />
              <Button
                onClick={handlePasswordSubmit}
                onKeyDown={(e) => e.key === ' ' && e.preventDefault()}
                className="w-full"
              >
                <Unlock className="h-4 w-4 mr-2" />
                Access Stream
              </Button>
            </div>
            {error && <p className="text-sm text-red-600">{error}</p>}
          </div>
        </CardContent>
      </Card>
    )
  }

  // Full-screen embedded mode
  if (isEmbedded && enableIframeComms) {
    return (
      <div className={`relative w-full h-full ${className}`}>
        {/* Stream container */}
        <div
          ref={containerRef}
          className="absolute inset-0 bg-black"
        >
          {/* Stream Service SDK mount point - hidden during loading/connecting */}
          <div
            ref={sdkMountRef}
            className="absolute inset-0 w-full h-full"
            style={{
              visibility: (isLoading || streamStatus === 'connecting') ? 'hidden' : 'visible',
              zIndex: 1
            }}
          />
          {/* Loading overlay - visible only during active loading/connecting */}
          {(isLoading || streamStatus === 'connecting') && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-900/95" style={{ zIndex: 30 }}>
              <div className="text-center text-white">
                <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4" />
                <p className="text-lg font-medium mb-2">{loadingMessage}</p>
                <p className="text-sm opacity-75">Status: {getStatusText()}</p>
              </div>
            </div>
          )}

          {/* Error overlay */}
          {error && (
            <div className="absolute inset-0 flex items-center justify-center bg-red-900" style={{ zIndex: 40 }}>
              <div className="text-center text-white p-4">
                <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                <p>{error}</p>
              </div>
            </div>
          )}

          {/* Floating control buttons - no bar background */}
          {showControls && (
            <>
              {/* Status badges - top left */}
              <div className="absolute top-4 left-4 z-30 flex items-center space-x-2">
                <Badge className={getStatusColor()}>{getStatusText()}</Badge>
                {buildId && (
                  <Badge variant="outline" className="text-white border-white/50 bg-black/70 backdrop-blur-sm">
                    Build: {buildId.slice(0, 8)}...
                  </Badge>
                )}
              </div>

              {/* Control buttons - bottom right */}
              <div className="absolute bottom-4 right-4 z-30 flex items-center space-x-2">
                {!isPlaying ? (
                  <Button
                    onClick={handlePlay}
                    onKeyDown={(e) => e.key === ' ' && e.preventDefault()}
                    size="sm"
                    className="bg-green-600/90 hover:bg-green-700 text-white font-medium px-4 backdrop-blur-sm shadow-lg"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Connect
                  </Button>
                ) : (
                  <Button
                    onClick={handlePause}
                    onKeyDown={(e) => e.key === ' ' && e.preventDefault()}
                    variant="destructive"
                    size="sm"
                    className="bg-red-600/90 hover:bg-red-700 text-white font-medium px-4 backdrop-blur-sm shadow-lg"
                  >
                    <Square className="h-4 w-4 mr-2" />
                    Disconnect
                  </Button>
                )}

                <Button
                  onClick={handleMute}
                  onKeyDown={(e) => e.key === ' ' && e.preventDefault()}
                  variant="outline"
                  size="sm"
                  className="text-white border-white/50 bg-black/70 hover:bg-black/80 hover:text-white backdrop-blur-sm shadow-lg"
                >
                  {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                </Button>

                <Button
                  onClick={handleFullscreen}
                  onKeyDown={(e) => e.key === ' ' && e.preventDefault()}
                  variant="outline"
                  size="sm"
                  className="text-white border-white/50 bg-black/70 hover:bg-black/80 hover:text-white backdrop-blur-sm shadow-lg"
                >
                  <Maximize className="h-4 w-4" />
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    )
  }

  // Standard card mode
  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Interactive Stream</CardTitle>
              <CardDescription>Real-time interactive streaming experience</CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Badge className={getStatusColor()}>{getStatusText()}</Badge>
              {buildId && (
                <Badge variant="outline">
                  Build: {buildId.slice(0, 8)}...
                </Badge>
              )}
              {showEmbedButton && (
                <Dialog open={showEmbedDialog} onOpenChange={setShowEmbedDialog}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onKeyDown={(e) => e.key === ' ' && e.preventDefault()}
                    >
                      <Code className="h-4 w-4 mr-2" />
                      Embed
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Embed Stream</DialogTitle>
                      <DialogDescription>Copy this code to embed the stream in your website</DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium">Embed Code:</label>
                        <textarea
                          className="w-full mt-1 p-2 border rounded text-sm font-mono"
                          rows={6}
                          readOnly
                          value={generateEmbedCode()}
                        />
                      </div>
                      <Button
                        onClick={copyEmbedCode}
                        onKeyDown={(e) => e.key === ' ' && e.preventDefault()}
                        className="w-full"
                      >
                        Copy Embed Code
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>
        </CardHeader>
      )}

      <CardContent>
        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        )}

        <div
          ref={containerRef}
          className="relative bg-black rounded-lg overflow-hidden"
          style={{
            width: '100%',
            aspectRatio: `${width}/${height}`,
            minHeight: '400px'
          }}
        >
          {/* Loading overlay - visible only during active loading/connecting */}
          {(isLoading || streamStatus === 'connecting') && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-900/95" style={{ zIndex: 30 }}>
              <div className="text-center text-white">
                <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4" />
                <p className="text-lg font-medium mb-2">{loadingMessage}</p>
                <p className="text-sm opacity-75">Status: {getStatusText()}</p>
              </div>
            </div>
          )}
          {!isLoading && !isPlaying && !error && streamStatus === 'disconnected' && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-900" style={{ zIndex: 20 }}>
              <div className="text-center text-white space-y-4">
                <Play className="h-16 w-16 mx-auto opacity-50" />
                <div>
                  <p className="text-lg mb-2">Ready to Connect</p>
                  <p className="text-sm opacity-75 mb-4">{messages.disconnected}</p>
                  {/* Always show connect button for embed players, or when autoConnect is disabled */}
                  {(isEmbedded || !config?.autoConnect) && (
                    <Button
                      onClick={handlePlay}
                      onKeyDown={(e) => e.key === ' ' && e.preventDefault()}
                      size="lg"
                    >
                      <Play className="h-5 w-5 mr-2" />
                      {messages.connectButton}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}
          {streamStatus === 'error' && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-900" style={{ zIndex: 20 }}>
              <div className="text-center text-white space-y-4">
                <AlertCircle className="h-16 w-16 mx-auto text-red-500" />
                <div>
                  <p className="text-lg mb-2">Connection Error</p>
                  <p className="text-sm opacity-75 mb-4">{error || messages.error}</p>
                  {/* Always show retry button for embed players, or when autoConnect is disabled */}
                  {(isEmbedded || !config?.autoConnect) && (
                    <Button
                      onClick={handlePlay}
                      onKeyDown={(e) => e.key === ' ' && e.preventDefault()}
                      size="lg"
                      variant="outline"
                    >
                      <Play className="h-5 w-5 mr-2" />
                      Try Again
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* SDK DOM mounts ONLY here - hidden during loading/connecting */}
          <div
            ref={sdkMountRef}
            className="absolute inset-0 w-full h-full"
            style={{
              visibility: (isLoading || streamStatus === 'connecting') ? 'hidden' : 'visible',
              zIndex: 1,
              pointerEvents: (isLoading || streamStatus === 'connecting') ? 'none' : 'auto'
            }}
          />
        </div>

        {/* Controls */}
        {showControls && (
          <div className="mt-4 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {streamStatus === 'disconnected' || streamStatus === 'error' ? (
                <Button
                  onClick={handlePlay}
                  onKeyDown={(e) => e.key === ' ' && e.preventDefault()}
                  disabled={isLoading}
                >
                  <Play className="h-4 w-4" />
                  Connect
                </Button>
              ) : streamStatus === 'connecting' ? (
                <Button disabled>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  {messages.connecting}
                </Button>
              ) : (
                <Button
                  variant="outline"
                  onClick={handlePause}
                  onKeyDown={(e) => e.key === ' ' && e.preventDefault()}
                >
                  <Square className="h-4 w-4" />
                  Disconnect
                </Button>
              )}
              <Button
                variant="outline"
                onClick={handleMute}
                onKeyDown={(e) => e.key === ' ' && e.preventDefault()}
              >
                {isMuted ? (
                  <VolumeX className="h-4 w-4" />
                ) : (
                  <Volume2 className="h-4 w-4" />
                )}
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleFullscreen}
                onKeyDown={(e) => e.key === ' ' && e.preventDefault()}
              >
                <Maximize className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={openInNewTab}
                onKeyDown={(e) => e.key === ' ' && e.preventDefault()}
                title="Open in new tab"
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        <div className="mt-4 text-xs text-gray-500">
          <p>Stream ID: {streamProjectId}</p>
          {buildId && <p>Build ID: {buildId}</p>}
        </div>
      </CardContent>
    </Card>
  )
}

// Extend window interface for Stream Service SDK (optional if you want TS autocomplete)
declare global {
  interface Window {
    StreamPixelApplication: any
  }
}
