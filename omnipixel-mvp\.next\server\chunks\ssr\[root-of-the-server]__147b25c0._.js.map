{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/stream-player.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const StreamPlayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call StreamPlayer() from the server but StreamPlayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/stream-player.tsx <module evaluation>\",\n    \"StreamPlayer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8DACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/stream-player.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const StreamPlayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call StreamPlayer() from the server but StreamPlayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/stream-player.tsx\",\n    \"StreamPlayer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\r\nimport { cookies } from 'next/headers'\r\n\r\nexport async function createClient(url?:string, key?:string) {\r\n  const cookieStore = await cookies()\r\n  url = url ?? process.env.NEXT_PUBLIC_SUPABASE_URL!\r\n  key = key ?? process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!\r\n\r\n  return createServerClient(\r\n    url,\r\n    key,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return cookieStore.getAll()\r\n        },\r\n        setAll(cookiesToSet) {\r\n          try {\r\n            cookiesToSet.forEach(({ name, value, options }) =>\r\n              cookieStore.set(name, value, options)\r\n            )\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  )\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe,aAAa,GAAW,EAAE,GAAW;IACzD,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM;IACN,MAAM;IAEN,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,KACA,KACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/embed/%5BprojectId%5D/page.tsx"], "sourcesContent": ["import { notFound } from 'next/navigation'\r\nimport { StreamPlayer } from '@/components/stream-player'\r\nimport { createClient } from '@/utils/supabase/server'\r\nimport { Build } from '@/lib/supabase'\r\nimport Script from 'next/script'\r\n\r\ninterface EmbedPageProps {\r\n  params: Promise<{\r\n    projectId: string\r\n  }>\r\n  searchParams: Promise<{\r\n    hideControls?: string\r\n    hideHeader?: string\r\n    hideEmbedButton?: string\r\n    autoConnect?: string\r\n    password?: string\r\n  }>\r\n}\r\n\r\nasync function getProject(projectId: string) {\r\n  \r\n  const supabase = await createClient();\r\n\r\n  const { data: project, error } = await supabase\r\n    .from('projects')\r\n    .select(`\r\n      *,\r\n      builds!builds_project_id_fkey (\r\n        id,\r\n        filename,\r\n        version,\r\n        status,\r\n        is_current,\r\n        file_size,\r\n        streampixel_build_id,\r\n        streampixel_status,\r\n        created_at\r\n      )\r\n    `)\r\n    .eq('id', projectId)\r\n    .single()\r\n\r\n  if (error || !project) {\r\n    return null\r\n  }\r\n\r\n  return project\r\n}\r\n\r\nexport default async function EmbedPage({ params, searchParams }: EmbedPageProps) {\r\n  const projectId = (await params).projectId\r\n  const project = await getProject(projectId)\r\n\r\n  if (!project) {\r\n    notFound()\r\n  }\r\n\r\n  // Get the current active build\r\n  const currentBuild = project.builds?.find((build: Build) => build.is_current)\r\n\r\n  // Parse URL parameters for control visibility\r\n  const sParams = await searchParams\r\n  const hideControls = sParams.hideControls === 'true'\r\n  const hideHeader = sParams.hideHeader === 'true'\r\n  const hideEmbedButton = sParams.hideEmbedButton === 'true'\r\n  const autoConnect = sParams.autoConnect === 'true'\r\n  const urlPassword = sParams.password\r\n\r\n  // Server-side password protection check\r\n  const isPasswordProtected = project.config?.isPasswordProtected === true\r\n  const projectPassword = project.config?.password\r\n\r\n  if (isPasswordProtected && projectPassword) {\r\n    // If password protection is enabled but no password provided in URL\r\n    if (!urlPassword) {\r\n      return (\r\n        <>\r\n          <style dangerouslySetInnerHTML={{\r\n            __html: `\r\n              body {\r\n                margin: 0;\r\n                padding: 0;\r\n                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n                background: #f8fafc;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                min-height: 100vh;\r\n              }\r\n              .password-container {\r\n                background: white;\r\n                padding: 2rem;\r\n                border-radius: 8px;\r\n                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\r\n                max-width: 400px;\r\n                width: 100%;\r\n                text-align: center;\r\n              }\r\n              .lock-icon {\r\n                width: 48px;\r\n                height: 48px;\r\n                margin: 0 auto 1rem;\r\n                color: #9ca3af;\r\n              }\r\n              h1 { margin: 0 0 0.5rem; color: #1f2937; }\r\n              p { margin: 0 0 1.5rem; color: #6b7280; }\r\n              input {\r\n                width: 100%;\r\n                padding: 0.75rem;\r\n                border: 1px solid #d1d5db;\r\n                border-radius: 6px;\r\n                margin-bottom: 1rem;\r\n                font-size: 1rem;\r\n              }\r\n              button {\r\n                width: 100%;\r\n                padding: 0.75rem;\r\n                background: #3b82f6;\r\n                color: white;\r\n                border: none;\r\n                border-radius: 6px;\r\n                font-size: 1rem;\r\n                cursor: pointer;\r\n              }\r\n              button:hover { background: #2563eb; }\r\n            `\r\n          }} />\r\n          <div className=\"password-container\">\r\n            <svg className=\"lock-icon\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n            <h1>Password Protected</h1>\r\n            <p>This stream requires a password to access.</p>\r\n            <form method=\"GET\">\r\n              <input type=\"hidden\" name=\"hideControls\" value={hideControls.toString()} />\r\n              <input type=\"hidden\" name=\"hideHeader\" value={hideHeader.toString()} />\r\n              <input type=\"hidden\" name=\"hideEmbedButton\" value={hideEmbedButton.toString()} />\r\n              <input type=\"hidden\" name=\"autoConnect\" value={autoConnect.toString()} />\r\n              <input\r\n                type=\"password\"\r\n                name=\"password\"\r\n                placeholder=\"Enter password\"\r\n                required\r\n                autoFocus\r\n              />\r\n              <button type=\"submit\">Access Stream</button>\r\n            </form>\r\n          </div>\r\n        </>\r\n      )\r\n    }\r\n\r\n    // If password provided but incorrect\r\n    if (urlPassword !== projectPassword) {\r\n      return (\r\n        <>\r\n          <style dangerouslySetInnerHTML={{\r\n            __html: `\r\n              body {\r\n                margin: 0;\r\n                padding: 0;\r\n                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n                background: #f8fafc;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                min-height: 100vh;\r\n              }\r\n              .password-container {\r\n                background: white;\r\n                padding: 2rem;\r\n                border-radius: 8px;\r\n                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\r\n                max-width: 400px;\r\n                width: 100%;\r\n                text-align: center;\r\n              }\r\n              .error-icon {\r\n                width: 48px;\r\n                height: 48px;\r\n                margin: 0 auto 1rem;\r\n                color: #ef4444;\r\n              }\r\n              h1 { margin: 0 0 0.5rem; color: #1f2937; }\r\n              p { margin: 0 0 1.5rem; color: #6b7280; }\r\n              .error { color: #ef4444; margin-bottom: 1rem; }\r\n              input {\r\n                width: 100%;\r\n                padding: 0.75rem;\r\n                border: 1px solid #ef4444;\r\n                border-radius: 6px;\r\n                margin-bottom: 1rem;\r\n                font-size: 1rem;\r\n              }\r\n              button {\r\n                width: 100%;\r\n                padding: 0.75rem;\r\n                background: #3b82f6;\r\n                color: white;\r\n                border: none;\r\n                border-radius: 6px;\r\n                font-size: 1rem;\r\n                cursor: pointer;\r\n              }\r\n              button:hover { background: #2563eb; }\r\n            `\r\n          }} />\r\n          <div className=\"password-container\">\r\n            <svg className=\"error-icon\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n            <h1>Access Denied</h1>\r\n            <p>The password you entered is incorrect.</p>\r\n            <div className=\"error\">Invalid password. Please try again.</div>\r\n            <form method=\"GET\">\r\n              <input type=\"hidden\" name=\"hideControls\" value={hideControls.toString()} />\r\n              <input type=\"hidden\" name=\"hideHeader\" value={hideHeader.toString()} />\r\n              <input type=\"hidden\" name=\"hideEmbedButton\" value={hideEmbedButton.toString()} />\r\n              <input type=\"hidden\" name=\"autoConnect\" value={autoConnect.toString()} />\r\n              <input\r\n                type=\"password\"\r\n                name=\"password\"\r\n                placeholder=\"Enter password\"\r\n                required\r\n                autoFocus\r\n              />\r\n              <button type=\"submit\">Try Again</button>\r\n            </form>\r\n          </div>\r\n        </>\r\n      )\r\n    }\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {/* Tailwind CSS */}\r\n      <Script src=\"https://cdn.tailwindcss.com\" strategy=\"beforeInteractive\" />\r\n\r\n      {/* Stream Service SDK */}\r\n      {/* <Script src=\"https://cdn.streampixel.io/sdk/latest/streampixel.min.js\" strategy=\"lazyOnload\" /> */}\r\n\r\n      <style dangerouslySetInnerHTML={{\r\n        __html: `\r\n          * {\r\n            margin: 0;\r\n            padding: 0;\r\n            box-sizing: border-box;\r\n          }\r\n\r\n          html, body {\r\n            width: 100%;\r\n            height: 100%;\r\n            margin: 0;\r\n            padding: 0;\r\n            background: #000;\r\n            font-family: system-ui, -apple-system, sans-serif;\r\n            overflow: hidden;\r\n          }\r\n\r\n          .embed-container {\r\n            position: fixed;\r\n            top: 0;\r\n            left: 0;\r\n            width: 100vw;\r\n            height: 100vh;\r\n            background: #000;\r\n            z-index: 1;\r\n          }\r\n\r\n          .stream-wrapper {\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n\r\n          /* Hide all scrollbars */\r\n          * {\r\n            scrollbar-width: none;\r\n            -ms-overflow-style: none;\r\n          }\r\n\r\n          *::-webkit-scrollbar {\r\n            display: none;\r\n          }\r\n\r\n          /* Ensure full coverage */\r\n          #__next {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        `\r\n      }} />\r\n\r\n      <div className=\"embed-container\">\r\n        <div className=\"stream-wrapper\">\r\n          <StreamPlayer\r\n            projectId={project.id}\r\n            buildId={currentBuild?.id}\r\n            config={{\r\n              ...project.config,\r\n              autoConnect: autoConnect || project.config?.autoConnect,\r\n              // Password protection is handled at server level, so disable client-side protection\r\n              isPasswordProtected: false,\r\n              password: undefined\r\n            }}\r\n            showControls={!hideControls}\r\n            showHeader={!hideHeader}\r\n            showEmbedButton={!hideEmbedButton}\r\n            isEmbedded={true}\r\n            enableIframeComms={true}\r\n            projectData={project}\r\n            className=\"absolute inset-0 w-full h-full border-0\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Embedded player metadata */}\r\n      <Script\r\n        id=\"structured-data\"\r\n        type=\"application/ld+json\"\r\n        dangerouslySetInnerHTML={{\r\n          __html: JSON.stringify({\r\n            \"@context\": \"https://schema.org\",\r\n            \"@type\": \"VideoObject\",\r\n            \"name\": project.name,\r\n            \"description\": `Interactive streaming experience for ${project.name}`,\r\n            \"thumbnailUrl\": `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/thumbnails/${project.id}.jpg`,\r\n            \"embedUrl\": `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,\r\n            \"uploadDate\": project.created_at,\r\n            \"duration\": \"PT0S\",\r\n            \"interactionStatistic\": {\r\n              \"@type\": \"InteractionCounter\",\r\n              \"interactionType\": \"https://schema.org/WatchAction\",\r\n              \"userInteractionCount\": 0\r\n            }\r\n          })\r\n        }}\r\n      />\r\n\r\n      {/* Analytics and tracking */}\r\n      <Script\r\n        id=\"embed-analytics\"\r\n        strategy=\"afterInteractive\"\r\n        dangerouslySetInnerHTML={{\r\n          __html: `\r\n            // Track embed views\r\n            if (window.parent !== window) {\r\n              // This is embedded in an iframe\r\n              console.log('Stream embedded and loaded');\r\n\r\n              // Send message to parent frame if needed\r\n              try {\r\n                window.parent.postMessage({\r\n                  type: 'stream-loaded',\r\n                  projectId: '${project.id}',\r\n                  timestamp: new Date().toISOString()\r\n                }, '*');\r\n              } catch (e) {\r\n                // Cross-origin restrictions\r\n              }\r\n            }\r\n\r\n            // Prevent right-click context menu\r\n            document.addEventListener('contextmenu', function(e) {\r\n              e.preventDefault();\r\n            });\r\n\r\n            // Prevent text selection\r\n            document.addEventListener('selectstart', function(e) {\r\n              e.preventDefault();\r\n            });\r\n\r\n            // Prevent drag and drop\r\n            document.addEventListener('dragstart', function(e) {\r\n              e.preventDefault();\r\n            });\r\n          `\r\n        }}\r\n      />\r\n    </>\r\n  )\r\n}\r\n\r\n// Generate metadata for SEO\r\nexport async function generateMetadata({ params }: EmbedPageProps) {\r\n  const projectId = (await params).projectId\r\n  const project = await getProject(projectId)\r\n\r\n  if (!project) {\r\n    return {\r\n      title: 'Stream Not Found',\r\n      description: 'The requested stream could not be found.'\r\n    }\r\n  }\r\n\r\n  return {\r\n    title: `${project.name} - Interactive Stream`,\r\n    description: `Interactive streaming experience for ${project.name}`,\r\n    openGraph: {\r\n      title: `${project.name} - Interactive Stream`,\r\n      description: `Interactive streaming experience for ${project.name}`,\r\n      type: 'video.other',\r\n      url: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,\r\n    },\r\n    twitter: {\r\n      card: 'player',\r\n      title: `${project.name} - Interactive Stream`,\r\n      description: `Interactive streaming experience for ${project.name}`,\r\n      players: {\r\n        playerUrl: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,\r\n        streamUrl: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,\r\n        width: 1280,\r\n        height: 720,\r\n      },\r\n    },\r\n    robots: {\r\n      index: false, // Don't index embed pages\r\n      follow: false,\r\n    },\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;AACA;AAEA;;;;;;AAeA,eAAe,WAAW,SAAiB;IAEzC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;IAaT,CAAC,EACA,EAAE,CAAC,MAAM,WACT,MAAM;IAET,IAAI,SAAS,CAAC,SAAS;QACrB,OAAO;IACT;IAEA,OAAO;AACT;AAEe,eAAe,UAAU,EAAE,MAAM,EAAE,YAAY,EAAkB;IAC9E,MAAM,YAAY,CAAC,MAAM,MAAM,EAAE,SAAS;IAC1C,MAAM,UAAU,MAAM,WAAW;IAEjC,IAAI,CAAC,SAAS;QACZ,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,+BAA+B;IAC/B,MAAM,eAAe,QAAQ,MAAM,EAAE,KAAK,CAAC,QAAiB,MAAM,UAAU;IAE5E,8CAA8C;IAC9C,MAAM,UAAU,MAAM;IACtB,MAAM,eAAe,QAAQ,YAAY,KAAK;IAC9C,MAAM,aAAa,QAAQ,UAAU,KAAK;IAC1C,MAAM,kBAAkB,QAAQ,eAAe,KAAK;IACpD,MAAM,cAAc,QAAQ,WAAW,KAAK;IAC5C,MAAM,cAAc,QAAQ,QAAQ;IAEpC,wCAAwC;IACxC,MAAM,sBAAsB,QAAQ,MAAM,EAAE,wBAAwB;IACpE,MAAM,kBAAkB,QAAQ,MAAM,EAAE;IAExC,IAAI,uBAAuB,iBAAiB;QAC1C,oEAAoE;QACpE,IAAI,CAAC,aAAa;YAChB,qBACE;;kCACE,8OAAC;wBAAM,yBAAyB;4BAC9B,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YA+CT,CAAC;wBACH;;;;;;kCACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAY,MAAK;gCAAe,SAAQ;0CACrD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAyG,UAAS;;;;;;;;;;;0CAE/I,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;0CACH,8OAAC;gCAAK,QAAO;;kDACX,8OAAC;wCAAM,MAAK;wCAAS,MAAK;wCAAe,OAAO,aAAa,QAAQ;;;;;;kDACrE,8OAAC;wCAAM,MAAK;wCAAS,MAAK;wCAAa,OAAO,WAAW,QAAQ;;;;;;kDACjE,8OAAC;wCAAM,MAAK;wCAAS,MAAK;wCAAkB,OAAO,gBAAgB,QAAQ;;;;;;kDAC3E,8OAAC;wCAAM,MAAK;wCAAS,MAAK;wCAAc,OAAO,YAAY,QAAQ;;;;;;kDACnE,8OAAC;wCACC,MAAK;wCACL,MAAK;wCACL,aAAY;wCACZ,QAAQ;wCACR,SAAS;;;;;;kDAEX,8OAAC;wCAAO,MAAK;kDAAS;;;;;;;;;;;;;;;;;;;;QAKhC;QAEA,qCAAqC;QACrC,IAAI,gBAAgB,iBAAiB;YACnC,qBACE;;kCACE,8OAAC;wBAAM,yBAAyB;4BAC9B,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAgDT,CAAC;wBACH;;;;;;kCACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAa,MAAK;gCAAe,SAAQ;0CACtD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAoH,UAAS;;;;;;;;;;;0CAE1J,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;0CACH,8OAAC;gCAAI,WAAU;0CAAQ;;;;;;0CACvB,8OAAC;gCAAK,QAAO;;kDACX,8OAAC;wCAAM,MAAK;wCAAS,MAAK;wCAAe,OAAO,aAAa,QAAQ;;;;;;kDACrE,8OAAC;wCAAM,MAAK;wCAAS,MAAK;wCAAa,OAAO,WAAW,QAAQ;;;;;;kDACjE,8OAAC;wCAAM,MAAK;wCAAS,MAAK;wCAAkB,OAAO,gBAAgB,QAAQ;;;;;;kDAC3E,8OAAC;wCAAM,MAAK;wCAAS,MAAK;wCAAc,OAAO,YAAY,QAAQ;;;;;;kDACnE,8OAAC;wCACC,MAAK;wCACL,MAAK;wCACL,aAAY;wCACZ,QAAQ;wCACR,SAAS;;;;;;kDAEX,8OAAC;wCAAO,MAAK;kDAAS;;;;;;;;;;;;;;;;;;;;QAKhC;IACF;IAEA,qBACE;;0BAEE,8OAAC,8HAAA,CAAA,UAAM;gBAAC,KAAI;gBAA8B,UAAS;;;;;;0BAKnD,8OAAC;gBAAM,yBAAyB;oBAC9B,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAkDT,CAAC;gBACH;;;;;;0BAEA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,+HAAA,CAAA,eAAY;wBACX,WAAW,QAAQ,EAAE;wBACrB,SAAS,cAAc;wBACvB,QAAQ;4BACN,GAAG,QAAQ,MAAM;4BACjB,aAAa,eAAe,QAAQ,MAAM,EAAE;4BAC5C,oFAAoF;4BACpF,qBAAqB;4BACrB,UAAU;wBACZ;wBACA,cAAc,CAAC;wBACf,YAAY,CAAC;wBACb,iBAAiB,CAAC;wBAClB,YAAY;wBACZ,mBAAmB;wBACnB,aAAa;wBACb,WAAU;;;;;;;;;;;;;;;;0BAMhB,8OAAC,8HAAA,CAAA,UAAM;gBACL,IAAG;gBACH,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC;wBACrB,YAAY;wBACZ,SAAS;wBACT,QAAQ,QAAQ,IAAI;wBACpB,eAAe,CAAC,qCAAqC,EAAE,QAAQ,IAAI,EAAE;wBACrE,gBAAgB,gFAAwC,qCAAqC,EAAE,QAAQ,EAAE,CAAC,IAAI,CAAC;wBAC/G,YAAY,GAAG,QAAQ,GAAG,CAAC,YAAY,IAAI,wBAAwB,OAAO,EAAE,QAAQ,EAAE,EAAE;wBACxF,cAAc,QAAQ,UAAU;wBAChC,YAAY;wBACZ,wBAAwB;4BACtB,SAAS;4BACT,mBAAmB;4BACnB,wBAAwB;wBAC1B;oBACF;gBACF;;;;;;0BAIF,8OAAC,8HAAA,CAAA,UAAM;gBACL,IAAG;gBACH,UAAS;gBACT,yBAAyB;oBACvB,QAAQ,CAAC;;;;;;;;;;8BAUW,EAAE,QAAQ,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;UAsBjC,CAAC;gBACH;;;;;;;;AAIR;AAGO,eAAe,iBAAiB,EAAE,MAAM,EAAkB;IAC/D,MAAM,YAAY,CAAC,MAAM,MAAM,EAAE,SAAS;IAC1C,MAAM,UAAU,MAAM,WAAW;IAEjC,IAAI,CAAC,SAAS;QACZ,OAAO;YACL,OAAO;YACP,aAAa;QACf;IACF;IAEA,OAAO;QACL,OAAO,GAAG,QAAQ,IAAI,CAAC,qBAAqB,CAAC;QAC7C,aAAa,CAAC,qCAAqC,EAAE,QAAQ,IAAI,EAAE;QACnE,WAAW;YACT,OAAO,GAAG,QAAQ,IAAI,CAAC,qBAAqB,CAAC;YAC7C,aAAa,CAAC,qCAAqC,EAAE,QAAQ,IAAI,EAAE;YACnE,MAAM;YACN,KAAK,GAAG,QAAQ,GAAG,CAAC,YAAY,IAAI,wBAAwB,OAAO,EAAE,QAAQ,EAAE,EAAE;QACnF;QACA,SAAS;YACP,MAAM;YACN,OAAO,GAAG,QAAQ,IAAI,CAAC,qBAAqB,CAAC;YAC7C,aAAa,CAAC,qCAAqC,EAAE,QAAQ,IAAI,EAAE;YACnE,SAAS;gBACP,WAAW,GAAG,QAAQ,GAAG,CAAC,YAAY,IAAI,wBAAwB,OAAO,EAAE,QAAQ,EAAE,EAAE;gBACvF,WAAW,GAAG,QAAQ,GAAG,CAAC,YAAY,IAAI,wBAAwB,OAAO,EAAE,QAAQ,EAAE,EAAE;gBACvF,OAAO;gBACP,QAAQ;YACV;QACF;QACA,QAAQ;YACN,OAAO;YACP,QAAQ;QACV;IACF;AACF", "debugId": null}}]}