{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/stream-player.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const StreamPlayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call StreamPlayer() from the server but StreamPlayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/stream-player.tsx <module evaluation>\",\n    \"StreamPlayer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8DACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/stream-player.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const StreamPlayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call StreamPlayer() from the server but StreamPlayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/stream-player.tsx\",\n    \"StreamPlayer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\r\nimport { cookies } from 'next/headers'\r\n\r\nexport async function createClient(url?:string, key?:string) {\r\n  const cookieStore = await cookies()\r\n  url = url ?? process.env.NEXT_PUBLIC_SUPABASE_URL!\r\n  key = key ?? process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!\r\n\r\n  return createServerClient(\r\n    url,\r\n    key,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return cookieStore.getAll()\r\n        },\r\n        setAll(cookiesToSet) {\r\n          try {\r\n            cookiesToSet.forEach(({ name, value, options }) =>\r\n              cookieStore.set(name, value, options)\r\n            )\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  )\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe,aAAa,GAAW,EAAE,GAAW;IACzD,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM;IACN,MAAM;IAEN,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,KACA,KACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/embed/%5BprojectId%5D/page.tsx"], "sourcesContent": ["import { notFound } from 'next/navigation'\r\nimport { StreamPlayer } from '@/components/stream-player'\r\nimport { createClient } from '@/utils/supabase/server'\r\nimport { Build } from '@/lib/supabase'\r\nimport Script from 'next/script'\r\n\r\ninterface EmbedPageProps {\r\n  params: Promise<{\r\n    projectId: string\r\n  }>\r\n  searchParams: Promise<{\r\n    hideControls?: string\r\n    hideHeader?: string\r\n    hideEmbedButton?: string\r\n    autoConnect?: string\r\n    password?: string\r\n  }>\r\n}\r\n\r\nasync function getProject(projectId: string) {\r\n  \r\n  const supabase = await createClient();\r\n\r\n  const { data: project, error } = await supabase\r\n    .from('projects')\r\n    .select(`\r\n      *,\r\n      builds!builds_project_id_fkey (\r\n        id,\r\n        filename,\r\n        version,\r\n        status,\r\n        is_current,\r\n        file_size,\r\n        streampixel_build_id,\r\n        streampixel_status,\r\n        created_at\r\n      )\r\n    `)\r\n    .eq('id', projectId)\r\n    .single()\r\n\r\n  if (error || !project) {\r\n    return null\r\n  }\r\n\r\n  return project\r\n}\r\n\r\nexport default async function EmbedPage({ params, searchParams }: EmbedPageProps) {\r\n  const projectId = (await params).projectId\r\n  const project = await getProject(projectId)\r\n\r\n  if (!project) {\r\n    notFound()\r\n  }\r\n\r\n  // Get the current active build\r\n  const currentBuild = project.builds?.find((build: Build) => build.is_current)\r\n\r\n  // Parse URL parameters for control visibility\r\n  const sParams = await searchParams\r\n  const hideControls = sParams.hideControls === 'true'\r\n  const hideHeader = sParams.hideHeader === 'true'\r\n  const hideEmbedButton = sParams.hideEmbedButton === 'true'\r\n  const autoConnect = sParams.autoConnect === 'true'\r\n  const urlPassword = sParams.password\r\n\r\n  return (\r\n    <>\r\n      {/* Tailwind CSS */}\r\n      <Script src=\"https://cdn.tailwindcss.com\" strategy=\"beforeInteractive\" />\r\n\r\n      {/* Stream Service SDK */}\r\n      {/* <Script src=\"https://cdn.streampixel.io/sdk/latest/streampixel.min.js\" strategy=\"lazyOnload\" /> */}\r\n\r\n      <style dangerouslySetInnerHTML={{\r\n        __html: `\r\n          * {\r\n            margin: 0;\r\n            padding: 0;\r\n            box-sizing: border-box;\r\n          }\r\n\r\n          html, body {\r\n            width: 100%;\r\n            height: 100%;\r\n            margin: 0;\r\n            padding: 0;\r\n            background: #000;\r\n            font-family: system-ui, -apple-system, sans-serif;\r\n            overflow: hidden;\r\n          }\r\n\r\n          .embed-container {\r\n            position: fixed;\r\n            top: 0;\r\n            left: 0;\r\n            width: 100vw;\r\n            height: 100vh;\r\n            background: #000;\r\n            z-index: 1;\r\n          }\r\n\r\n          .stream-wrapper {\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n\r\n          /* Hide all scrollbars */\r\n          * {\r\n            scrollbar-width: none;\r\n            -ms-overflow-style: none;\r\n          }\r\n\r\n          *::-webkit-scrollbar {\r\n            display: none;\r\n          }\r\n\r\n          /* Ensure full coverage */\r\n          #__next {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        `\r\n      }} />\r\n\r\n      <div className=\"embed-container\">\r\n        <div className=\"stream-wrapper\">\r\n          <StreamPlayer\r\n            projectId={project.id}\r\n            buildId={currentBuild?.id}\r\n            config={{\r\n              ...project.config,\r\n              autoConnect: autoConnect || project.config?.autoConnect,\r\n              password: urlPassword || project.config?.password\r\n            }}\r\n            showControls={!hideControls}\r\n            showHeader={!hideHeader}\r\n            showEmbedButton={!hideEmbedButton}\r\n            isEmbedded={true}\r\n            enableIframeComms={true}\r\n            projectData={project}\r\n            className=\"absolute inset-0 w-full h-full border-0\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Embedded player metadata */}\r\n      <Script\r\n        id=\"structured-data\"\r\n        type=\"application/ld+json\"\r\n        dangerouslySetInnerHTML={{\r\n          __html: JSON.stringify({\r\n            \"@context\": \"https://schema.org\",\r\n            \"@type\": \"VideoObject\",\r\n            \"name\": project.name,\r\n            \"description\": `Interactive streaming experience for ${project.name}`,\r\n            \"thumbnailUrl\": `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/thumbnails/${project.id}.jpg`,\r\n            \"embedUrl\": `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,\r\n            \"uploadDate\": project.created_at,\r\n            \"duration\": \"PT0S\",\r\n            \"interactionStatistic\": {\r\n              \"@type\": \"InteractionCounter\",\r\n              \"interactionType\": \"https://schema.org/WatchAction\",\r\n              \"userInteractionCount\": 0\r\n            }\r\n          })\r\n        }}\r\n      />\r\n\r\n      {/* Analytics and tracking */}\r\n      <Script\r\n        id=\"embed-analytics\"\r\n        strategy=\"afterInteractive\"\r\n        dangerouslySetInnerHTML={{\r\n          __html: `\r\n            // Track embed views\r\n            if (window.parent !== window) {\r\n              // This is embedded in an iframe\r\n              console.log('Stream embedded and loaded');\r\n\r\n              // Send message to parent frame if needed\r\n              try {\r\n                window.parent.postMessage({\r\n                  type: 'stream-loaded',\r\n                  projectId: '${project.id}',\r\n                  timestamp: new Date().toISOString()\r\n                }, '*');\r\n              } catch (e) {\r\n                // Cross-origin restrictions\r\n              }\r\n            }\r\n\r\n            // Prevent right-click context menu\r\n            document.addEventListener('contextmenu', function(e) {\r\n              e.preventDefault();\r\n            });\r\n\r\n            // Prevent text selection\r\n            document.addEventListener('selectstart', function(e) {\r\n              e.preventDefault();\r\n            });\r\n\r\n            // Prevent drag and drop\r\n            document.addEventListener('dragstart', function(e) {\r\n              e.preventDefault();\r\n            });\r\n          `\r\n        }}\r\n      />\r\n    </>\r\n  )\r\n}\r\n\r\n// Generate metadata for SEO\r\nexport async function generateMetadata({ params }: EmbedPageProps) {\r\n  const projectId = (await params).projectId\r\n  const project = await getProject(projectId)\r\n\r\n  if (!project) {\r\n    return {\r\n      title: 'Stream Not Found',\r\n      description: 'The requested stream could not be found.'\r\n    }\r\n  }\r\n\r\n  return {\r\n    title: `${project.name} - Interactive Stream`,\r\n    description: `Interactive streaming experience for ${project.name}`,\r\n    openGraph: {\r\n      title: `${project.name} - Interactive Stream`,\r\n      description: `Interactive streaming experience for ${project.name}`,\r\n      type: 'video.other',\r\n      url: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,\r\n    },\r\n    twitter: {\r\n      card: 'player',\r\n      title: `${project.name} - Interactive Stream`,\r\n      description: `Interactive streaming experience for ${project.name}`,\r\n      players: {\r\n        playerUrl: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,\r\n        streamUrl: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,\r\n        width: 1280,\r\n        height: 720,\r\n      },\r\n    },\r\n    robots: {\r\n      index: false, // Don't index embed pages\r\n      follow: false,\r\n    },\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;AACA;AAEA;;;;;;AAeA,eAAe,WAAW,SAAiB;IAEzC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;IAaT,CAAC,EACA,EAAE,CAAC,MAAM,WACT,MAAM;IAET,IAAI,SAAS,CAAC,SAAS;QACrB,OAAO;IACT;IAEA,OAAO;AACT;AAEe,eAAe,UAAU,EAAE,MAAM,EAAE,YAAY,EAAkB;IAC9E,MAAM,YAAY,CAAC,MAAM,MAAM,EAAE,SAAS;IAC1C,MAAM,UAAU,MAAM,WAAW;IAEjC,IAAI,CAAC,SAAS;QACZ,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,+BAA+B;IAC/B,MAAM,eAAe,QAAQ,MAAM,EAAE,KAAK,CAAC,QAAiB,MAAM,UAAU;IAE5E,8CAA8C;IAC9C,MAAM,UAAU,MAAM;IACtB,MAAM,eAAe,QAAQ,YAAY,KAAK;IAC9C,MAAM,aAAa,QAAQ,UAAU,KAAK;IAC1C,MAAM,kBAAkB,QAAQ,eAAe,KAAK;IACpD,MAAM,cAAc,QAAQ,WAAW,KAAK;IAC5C,MAAM,cAAc,QAAQ,QAAQ;IAEpC,qBACE;;0BAEE,8OAAC,8HAAA,CAAA,UAAM;gBAAC,KAAI;gBAA8B,UAAS;;;;;;0BAKnD,8OAAC;gBAAM,yBAAyB;oBAC9B,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAkDT,CAAC;gBACH;;;;;;0BAEA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,+HAAA,CAAA,eAAY;wBACX,WAAW,QAAQ,EAAE;wBACrB,SAAS,cAAc;wBACvB,QAAQ;4BACN,GAAG,QAAQ,MAAM;4BACjB,aAAa,eAAe,QAAQ,MAAM,EAAE;4BAC5C,UAAU,eAAe,QAAQ,MAAM,EAAE;wBAC3C;wBACA,cAAc,CAAC;wBACf,YAAY,CAAC;wBACb,iBAAiB,CAAC;wBAClB,YAAY;wBACZ,mBAAmB;wBACnB,aAAa;wBACb,WAAU;;;;;;;;;;;;;;;;0BAMhB,8OAAC,8HAAA,CAAA,UAAM;gBACL,IAAG;gBACH,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC;wBACrB,YAAY;wBACZ,SAAS;wBACT,QAAQ,QAAQ,IAAI;wBACpB,eAAe,CAAC,qCAAqC,EAAE,QAAQ,IAAI,EAAE;wBACrE,gBAAgB,gFAAwC,qCAAqC,EAAE,QAAQ,EAAE,CAAC,IAAI,CAAC;wBAC/G,YAAY,GAAG,QAAQ,GAAG,CAAC,YAAY,IAAI,wBAAwB,OAAO,EAAE,QAAQ,EAAE,EAAE;wBACxF,cAAc,QAAQ,UAAU;wBAChC,YAAY;wBACZ,wBAAwB;4BACtB,SAAS;4BACT,mBAAmB;4BACnB,wBAAwB;wBAC1B;oBACF;gBACF;;;;;;0BAIF,8OAAC,8HAAA,CAAA,UAAM;gBACL,IAAG;gBACH,UAAS;gBACT,yBAAyB;oBACvB,QAAQ,CAAC;;;;;;;;;;8BAUW,EAAE,QAAQ,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;UAsBjC,CAAC;gBACH;;;;;;;;AAIR;AAGO,eAAe,iBAAiB,EAAE,MAAM,EAAkB;IAC/D,MAAM,YAAY,CAAC,MAAM,MAAM,EAAE,SAAS;IAC1C,MAAM,UAAU,MAAM,WAAW;IAEjC,IAAI,CAAC,SAAS;QACZ,OAAO;YACL,OAAO;YACP,aAAa;QACf;IACF;IAEA,OAAO;QACL,OAAO,GAAG,QAAQ,IAAI,CAAC,qBAAqB,CAAC;QAC7C,aAAa,CAAC,qCAAqC,EAAE,QAAQ,IAAI,EAAE;QACnE,WAAW;YACT,OAAO,GAAG,QAAQ,IAAI,CAAC,qBAAqB,CAAC;YAC7C,aAAa,CAAC,qCAAqC,EAAE,QAAQ,IAAI,EAAE;YACnE,MAAM;YACN,KAAK,GAAG,QAAQ,GAAG,CAAC,YAAY,IAAI,wBAAwB,OAAO,EAAE,QAAQ,EAAE,EAAE;QACnF;QACA,SAAS;YACP,MAAM;YACN,OAAO,GAAG,QAAQ,IAAI,CAAC,qBAAqB,CAAC;YAC7C,aAAa,CAAC,qCAAqC,EAAE,QAAQ,IAAI,EAAE;YACnE,SAAS;gBACP,WAAW,GAAG,QAAQ,GAAG,CAAC,YAAY,IAAI,wBAAwB,OAAO,EAAE,QAAQ,EAAE,EAAE;gBACvF,WAAW,GAAG,QAAQ,GAAG,CAAC,YAAY,IAAI,wBAAwB,OAAO,EAAE,QAAQ,EAAE,EAAE;gBACvF,OAAO;gBACP,QAAQ;YACV;QACF;QACA,QAAQ;YACN,OAAO;YACP,QAAQ;QACV;IACF;AACF", "debugId": null}}]}