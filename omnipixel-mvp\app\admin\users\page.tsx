// app/admin/users/page.tsx
import { redirect } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'
import AdminUsersClient from './client'
import { User } from '@/types/supabase'

export default async function AdminUsersPage() {
  const supabase = await createClient()
  // Auth
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login')

  // Profile
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()
  if (!profile || profile.role !== 'platform_admin') redirect('/admin')

  // Fetch users with projects (first page, limit 20)
  const { data: usersRaw, count } = await supabase
    .from('profiles')
    .select(`
      id,
      email,
      role,
      created_at,
      updated_at,
      projects:projects (
        id, name, created_at
      )
    `, { count: 'exact' })
    .order('created_at', { ascending: false })
    .range(0, 19)

  const users = (usersRaw ?? []).map((u: User) => ({
    id: u.id,
    email: u.email,
    role: u.role,
    created_at: u.created_at,
    updated_at: u.updated_at,
    projects: u.projects || [],
  }))

  // Pagination
  const pagination = {
    page: 1,
    limit: 20,
    total: count || users.length,
    totalPages: Math.max(1, Math.ceil((count || users.length) / 20))
  }

  return (
    <AdminUsersClient
      user={user}
      profile={profile}
      initialUsers={users}
      initialPagination={pagination}
    />
  )
}
