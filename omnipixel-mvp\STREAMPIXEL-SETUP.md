# StreamPixel API Configuration Setup

## 🚨 **Current Issue**
StreamPixel upload is failing with: `StreamPixel API configuration missing`

## 🔧 **Missing Configuration**
You need to set your **StreamPixel User ID** in the environment variables.

## 📋 **Required Environment Variables**

### **Current Status:**
- ✅ `STREAMPIXEL_API_KEY` - Present
- ❌ `STREAMPIXEL_USER_ID` - **Missing**

## 🎯 **How to Get Your StreamPixel User ID**

### **Method 1: StreamPixel Dashboard**
1. **Log in** to your StreamPixel account at https://streampixel.io
2. **Go to Account Settings** or **Profile**
3. **Look for User ID** or **Account ID**
4. **Copy the User ID** (usually a string or number)

### **Method 2: StreamPixel API Documentation**
1. **Check your StreamPixel API documentation**
2. **Look for account/user endpoints**
3. **Your User ID** should be mentioned in API examples

### **Method 3: Contact StreamPixel Support**
If you can't find your User ID:
1. **Contact StreamPixel support**
2. **Ask for your User ID** for API integration
3. **Mention you're integrating with their upload API**

## 🔧 **Update Environment Variables**

### **1. Update `.env.local`:**
```bash
# Current (incomplete)
STREAMPIXEL_API_KEY="00000000000000000000000000000000:74698d4eccc1c05f06e850d09a8b2f202e132da70d36546198db244518d232c23013e9cfb26a7dccd8e164bde3105260"
STREAMPIXEL_USER_ID=your_streampixel_user_id_here

# Replace with your actual User ID
STREAMPIXEL_USER_ID=your_actual_user_id
```

### **2. Example User ID formats:**
```bash
# Could be a number
STREAMPIXEL_USER_ID=12345

# Could be a string
STREAMPIXEL_USER_ID=user_abc123

# Could be a UUID
STREAMPIXEL_USER_ID=550e8400-e29b-41d4-a716-************
```

## 🧪 **Test the Configuration**

### **1. Restart the development server:**
```bash
npm run dev
```

### **2. Try uploading a build:**
1. **Upload a ZIP file**
2. **Activate the build**
3. **Check console logs** for StreamPixel API calls

### **3. Check the API response:**
The error should now show specific details:
```json
{
  "error": "StreamPixel API configuration missing",
  "details": {
    "apiKey": "Present",
    "userId": "Missing"  // Should show "Present" after fix
  }
}
```

## 🔍 **Debugging Steps**

### **1. Check Environment Variables:**
Add this to your API route temporarily:
```typescript
console.log('StreamPixel Config Check:', {
  apiKey: process.env.STREAMPIXEL_API_KEY ? 'Present' : 'Missing',
  userId: process.env.STREAMPIXEL_USER_ID ? 'Present' : 'Missing'
})
```

### **2. Verify API Key Format:**
Your API key should be in format:
```
"project_id:api_key_hash"
```

### **3. Test API Connection:**
You can test the StreamPixel API directly:
```bash
curl -X POST https://api.streampixel.io/pixelStripeApi/projects/upload-file \
  -H "Content-Type: application/json" \
  -d '{
    "apiKey": "your_api_key",
    "userId": "your_user_id",
    "fileUrl": "test_url",
    "projectId": "test_project",
    "autoRelease": true
  }'
```

## 📞 **Next Steps**

1. **Get your StreamPixel User ID** from your account
2. **Update `.env.local`** with the correct User ID
3. **Restart the development server**
4. **Test the upload functionality**

## ⚠️ **Security Note**
- Keep your API keys secure
- Don't commit `.env.local` to version control
- Use different keys for development and production

## 🆘 **Still Having Issues?**

If you continue to have problems:
1. **Check StreamPixel documentation** for API requirements
2. **Verify your account** has API access enabled
3. **Contact StreamPixel support** for assistance
4. **Check network connectivity** to StreamPixel API

Once you have your User ID, update the `.env.local` file and restart the server!
