-- Build Flow Improvements Migration - Part 2
-- Functions and indexes that use the new 'inactive' enum value

-- 1. Update the build limit enforcement function to handle inactive builds
CREATE OR REPLACE FUNCTION enforce_build_limit()
RETURNS TRIGGER AS $$
DECLARE
    build_count INTEGER;
    oldest_build_id UUID;
BEGIN
    -- Count current builds for this project (excluding failed builds)
    SELECT COUNT(*) INTO build_count
    FROM builds 
    WHERE project_id = NEW.project_id 
    AND status NOT IN ('failed', 'archived');
    
    -- If we're at the limit (2 builds), archive the oldest non-current build
    IF build_count >= 2 THEN
        -- Find the oldest build that is not current
        SELECT id INTO oldest_build_id
        FROM builds 
        WHERE project_id = NEW.project_id 
        AND status NOT IN ('failed', 'archived')
        AND is_current = false
        ORDER BY created_at ASC
        LIMIT 1;
        
        -- If we found an old build, archive it
        IF oldest_build_id IS NOT NULL THEN
            UPDATE builds 
            SET status = 'archived', updated_at = NOW()
            WHERE id = oldest_build_id;
        ELSE
            -- If no non-current build found, prevent insertion
            RAISE EXCEPTION 'Cannot upload new build: maximum of 2 builds allowed per project. Please delete an existing build first.';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 2. Update the first build auto-activation function to respect auto_release setting
CREATE OR REPLACE FUNCTION set_first_build_current()
RETURNS TRIGGER AS $$
DECLARE
    current_build_count INTEGER;
    project_auto_release BOOLEAN;
BEGIN
    -- Count current builds for this project
    SELECT COUNT(*) INTO current_build_count
    FROM builds 
    WHERE project_id = NEW.project_id 
    AND is_current = true
    AND status NOT IN ('failed', 'archived');
    
    -- Get project's auto_release setting
    SELECT auto_release INTO project_auto_release
    FROM projects 
    WHERE id = NEW.project_id;
    
    -- If no current build exists and auto_release is enabled, make this one current
    -- OR if this is the very first build for the project, always make it current
    IF current_build_count = 0 THEN
        -- Check if this is the first build ever for this project
        DECLARE
            total_builds INTEGER;
        BEGIN
            SELECT COUNT(*) INTO total_builds
            FROM builds 
            WHERE project_id = NEW.project_id;
            
            -- If first build ever OR auto_release is enabled, set to processing
            -- DON'T set as current until StreamPixel webhook confirms
            IF total_builds = 0 OR project_auto_release = true THEN
                NEW.is_current = false; -- Wait for StreamPixel webhook
                NEW.status = 'processing'; -- Will be sent to StreamPixel
            ELSE
                -- Otherwise, keep it inactive
                NEW.status = 'inactive';
                NEW.is_current = false;
            END IF;
        END;
    ELSE
        -- If there's already a current build, this one should be inactive by default
        -- unless auto_release is enabled
        IF project_auto_release = true THEN
            -- Auto-release: DON'T deactivate current build yet, wait for StreamPixel
            -- Set new build to processing, but not current until webhook confirms
            NEW.is_current = false; -- Wait for StreamPixel webhook
            NEW.status = 'processing'; -- Will be sent to StreamPixel
        ELSE
            -- Manual activation: keep this build inactive
            NEW.status = 'inactive';
            NEW.is_current = false;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 3. Create function to prevent deletion of current/active builds
CREATE OR REPLACE FUNCTION prevent_active_build_deletion()
RETURNS TRIGGER AS $$
BEGIN
    -- Prevent deletion of current/active builds
    IF OLD.is_current = true OR OLD.status = 'active' THEN
        RAISE EXCEPTION 'Cannot delete the currently active build. Please activate another build first.';
    END IF;
    
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to prevent active build deletion
DROP TRIGGER IF EXISTS trigger_prevent_active_build_deletion ON builds;
CREATE TRIGGER trigger_prevent_active_build_deletion
    BEFORE DELETE ON builds
    FOR EACH ROW
    EXECUTE FUNCTION prevent_active_build_deletion();

-- 4. Create function to count non-archived builds for UI logic
CREATE OR REPLACE FUNCTION get_active_build_count(project_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    build_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO build_count
    FROM builds 
    WHERE project_id = project_uuid 
    AND status NOT IN ('failed', 'archived');
    
    RETURN build_count;
END;
$$ LANGUAGE plpgsql;

-- 5. Add index for auto_release queries
CREATE INDEX IF NOT EXISTS idx_projects_auto_release ON projects(auto_release);

-- 6. Add index for inactive builds
CREATE INDEX IF NOT EXISTS idx_builds_inactive ON builds(project_id, status) WHERE status = 'inactive';

-- 7. Update existing projects to have auto_release = false (explicit default)
UPDATE projects 
SET auto_release = false 
WHERE auto_release IS NULL;
