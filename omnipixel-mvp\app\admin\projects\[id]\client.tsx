'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { Navigation } from '@/components/navigation'
import { UppyFileUpload } from '@/components/uppy-file-upload'
import { EnhancedConfigEditor } from '@/components/enhanced-config-editor'
import { BuildHistory } from '@/components/build-history'
import { StreamPlayer } from '@/components/stream-player'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Download, Loader2, Edit2, Save, X, Trash2, Settings, Package, Calendar, UserIcon } from 'lucide-react'
import type { Build } from '@/lib/supabase'
import { Body, Meta, UploadResult } from '@uppy/core'

type AdminProject = {
  id: string
  name: string
  stream_project_id: string
  user_id: string
  config: any
  auto_release: boolean
  created_at: string
  updated_at: string
  profiles: {
    email: string
    role: string
  }
  builds: Build[]
}

type EditForm = {
  name: string
  stream_project_id: string
  user_email: string
  config: string
}

export default function AdminProjectDetailClient({ initialProject }: { initialProject: AdminProject }) {
  const router = useRouter()
  const [project, setProject] = useState<AdminProject>(initialProject)
  const [builds, setBuilds] = useState<Build[]>(initialProject.builds || [])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setSaving] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [editedName, setEditedName] = useState(project.name)
  const [autoRelease, setAutoRelease] = useState(project.auto_release)
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const [editForm, setEditForm] = useState<EditForm>({
    name: initialProject.name,
    stream_project_id: initialProject.stream_project_id,
    user_email: initialProject.profiles.email,
    config: JSON.stringify(initialProject.config || {}, null, 2),
  })

  // ------ HANDLERS ---------
  const fetchBuilds = useCallback(async (isPolling = false) => {
    try {
      if (!isPolling) {
        setLoading(true)
      }
      setError(null)
      const response = await fetch(`/api/projects/${project.id}/builds`)
      if (!response.ok) throw new Error('Could not fetch builds')
      const data = await response.json()
      setBuilds(data.builds)

      // Update project builds as well
      setProject(prev => ({ ...prev, builds: data.builds }))
    } catch (err) {
      if (!isPolling) {
        setError(err instanceof Error ? err.message : 'Failed to fetch builds')
      }
    } finally {
      if (!isPolling) {
        setLoading(false)
      }
    }
  }, [project.id])

  const refreshProject = async () => {
    setLoading(true)
    setError(null)
    try {
      const res = await fetch(`/api/admin/projects/${project.id}`)
      if (!res.ok) throw new Error('Failed to refresh project')
      const data = await res.json()
      setProject(data.project)
      setBuilds(data.project.builds || [])
      setEditForm({
        name: data.project.name,
        stream_project_id: data.project.stream_project_id,
        user_email: data.project.profiles.email,
        config: JSON.stringify(data.project.config || {}, null, 2),
      })
    } catch (e: unknown) {
      setError(e instanceof Error ? e.message : 'An unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  // Auto-polling for processing builds
  useEffect(() => {
    const hasProcessingBuilds = builds.some(build =>
      build.status === 'processing' || build.streampixel_status === 'processing'
    )

    if (hasProcessingBuilds) {
      // Start polling every 10 seconds
      pollingIntervalRef.current = setInterval(() => {
        fetchBuilds(true) // isPolling = true to avoid loading states
      }, 10000)
    } else {
      // Stop polling if no processing builds
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
        pollingIntervalRef.current = null
      }
    }

    // Cleanup on unmount
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
        pollingIntervalRef.current = null
      }
    }
  }, [builds, fetchBuilds])

  const handleUploadComplete = (result: UploadResult<Meta, Body>) => {
    if (result.build) {
      setBuilds((prev) => {
        const newBuild = result.build as Build;
        return [newBuild, ...prev].sort((a, b) => b.version - a.version);
      });
    } else {
      fetchBuilds()
    }
  }

  const handleUploadError = (err: string) => setError(err)

  const handleSaveProjectName = async () => {
    if (!editedName.trim()) return
    try {
      setLoading(true)
      const response = await fetch(`/api/projects/${project.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: editedName.trim() }),
      })
      if (!response.ok) throw new Error('Failed to update project name')
      setProject(prev => ({ ...prev, name: editedName.trim() }))
      setIsEditing(false)
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleCancelEdit = () => {
    setEditedName(project.name)
    setIsEditing(false)
  }

  const handleAutoReleaseToggle = async (enabled: boolean) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/projects/${project.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ auto_release: enabled }),
      })
      if (!response.ok) throw new Error('Failed to update auto-release setting')
      setAutoRelease(enabled)
      setProject(prev => ({ ...prev, auto_release: enabled }))
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const handleSave = async () => {
    if (!project) return
    setSaving(true)
    setError(null)
    try {
      let parsedConfig
      try { parsedConfig = JSON.parse(editForm.config) }
      catch { throw new Error('Invalid JSON in config field') }
      const res = await fetch('/api/admin/projects', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          project_id: project.id,
          name: editForm.name,
          stream_project_id: editForm.stream_project_id,
          user_email: editForm.user_email,
          config: parsedConfig,
        }),
      })
      if (!res.ok) {
        const data = await res.json()
        throw new Error(data.error || 'Failed to update project')
      }
      const data = await res.json()
      setProject(data.project)
      setIsEditing(false)
      alert('Project updated successfully!')
    } catch (e: unknown) {
      setError(e instanceof Error ? e.message : 'An unknown error occurred')
      alert('Failed to update project: ' + e)
    } finally {
      setSaving(false)
    }
  }

  const handleDelete = async () => {
    if (!project) return
    if (!confirm(`Are you sure you want to delete "${project.name}"? This action cannot be undone.`)) return
    setIsDeleting(true)
    setError(null)
    try {
      const res = await fetch('/api/admin/projects', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ project_id: project.id }),
      })
      if (!res.ok) {
        const data = await res.json()
        throw new Error(data.error || 'Failed to delete project')
      }
      alert('Project deleted successfully!')
      router.push('/admin')
    } catch (e: unknown) {
      setError(e instanceof Error ? e.message : 'An unknown error occurred')
      alert('Failed to delete project: ' + e)
    } finally {
      setIsDeleting(false)
    }
  }



  const handleBuildRevert = async (buildId: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/projects/${project.id}/builds/revert`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ build_id: buildId }),
      })
      if (!response.ok) throw new Error('Failed to revert build')
      await fetchBuilds()
      alert('Build reverted successfully!')
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleBuildDelete = async (buildId: string) => {
    try {
      setLoading(true)
      console.log('🗑️ [ADMIN] Deleting build:', buildId, 'from project:', project.id)

      const response = await fetch(`/api/projects/${project.id}/builds/${buildId}`, { method: 'DELETE' })
      console.log('[ADMIN] Delete response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('[ADMIN] Delete failed:', errorData)
        throw new Error(errorData.error || 'Failed to delete build')
      }

      const result = await response.json()
      console.log('[ADMIN] Delete result:', result)

      await fetchBuilds()
      alert('Build deleted successfully!')
    } catch (err: unknown) {
      console.error('[ADMIN] Build deletion error:', err)
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred'
      setError(errorMessage)
      alert('Failed to delete build: ' + errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleBuildActivate = async (buildId: string) => {
    try {
      setLoading(true)
      setError(null)
      console.log('Activating build:', buildId, 'for project:', project.id)

      const response = await fetch(`/api/projects/${project.id}/builds/${buildId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      console.log('Activation response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Activation failed:', errorData)
        throw new Error(errorData.error || 'Failed to activate build')
      }

      const result = await response.json()
      console.log('Activation result:', result)

      await fetchBuilds()
      alert('Build activated successfully!')
    } catch (err: unknown) {
      console.error('Build activation error:', err)
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred'
      setError(errorMessage)
      alert('Failed to activate build: ' + errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    if (!project) return
    setEditForm({
      name: project.name,
      stream_project_id: project.stream_project_id,
      user_email: project.profiles.email,
      config: JSON.stringify(project.config || {}, null, 2),
    })
    setIsEditing(false)
  }

  // ----------- RENDER LOGIC -----------

  if (loading) return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      <div className="flex items-center justify-center py-16"><Loader2 className="h-8 w-8 animate-spin" /> Loading...</div>
    </div>
  )
  if (error || !project) return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      <div className="flex items-center justify-center py-16">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Error</h1>
          <p className="text-red-600 mt-2">{error || 'Project not found'}</p>
          <Button onClick={() => router.push('/admin')} className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Admin Panel
          </Button>
        </div>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <Button variant="ghost" onClick={() => router.push('/admin')} className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" /> Back to Admin Panel
          </Button>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
              <p className="text-gray-600 mt-1">
                Owned by {project.profiles.email} • Stream ID: {project.stream_project_id}
              </p>
            </div>
            <div className="flex space-x-2">
              {!isEditing ? (
                <>
                  <Button onClick={() => setIsEditing(true)}>
                    <Edit2 className="h-4 w-4 mr-2" /> Edit Project
                  </Button>
                  <Button variant="destructive" onClick={handleDelete} disabled={isDeleting}>
                    {isDeleting ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4 mr-2" />
                    )}
                    Delete Project
                  </Button>
                </>
              ) : (
                <>
                  <Button onClick={handleSave} disabled={isSaving}>
                    {isSaving ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    Save Changes
                  </Button>
                  <Button variant="outline" onClick={handleCancel}>
                    <X className="h-4 w-4 mr-2" /> Cancel
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {!isEditing && project.stream_project_id && (
              <StreamPlayer
                projectId={project.id}
                buildId={project.builds?.find(b => b.is_current)?.id}
                config={project.config}
                showControls={true}
                showHeader={true}
                showEmbedButton={true}
              />
            )}
            {isEditing && (
              <Card>
                <CardHeader>
                  <CardTitle>Edit Project Details</CardTitle>
                  <CardDescription>Update project information and configuration</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Project Name</label>
                    <Input value={editForm.name} onChange={e => setEditForm({ ...editForm, name: e.target.value })} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Stream Project ID</label>
                    <Input value={editForm.stream_project_id} onChange={e => setEditForm({ ...editForm, stream_project_id: e.target.value })} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Owner Email</label>
                    <Input value={editForm.user_email} onChange={e => setEditForm({ ...editForm, user_email: e.target.value })} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Project Configuration (JSON)</label>
                    <Textarea value={editForm.config} onChange={e => setEditForm({ ...editForm, config: e.target.value })} rows={8} className="font-mono text-sm" />
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Upload Section - Admin can always upload */}
            {!isEditing && (
              <Card>
                <CardHeader>
                  <CardTitle>Upload New Build</CardTitle>
                  <CardDescription>Upload a new build file for this project as an admin.</CardDescription>
                </CardHeader>
                <CardContent>
                  <UppyFileUpload
                    projectId={project.id}
                    onUploadComplete={handleUploadComplete}
                    onUploadError={handleUploadError}
                  />
                </CardContent>
              </Card>
            )}

            {/* Project Configuration */}
            <EnhancedConfigEditor
              projectId={project.id}
              currentConfig={project.config || {}}
              onConfigUpdate={newConfig => setProject({ ...project, config: { ...newConfig, primaryCodec: newConfig.primaryCodec as "AV1" | "H264" | "VP9" | "VP8" | undefined, fallBackCodec: newConfig.fallBackCodec as "AV1" | "H264" | "VP9" | "VP8" | undefined, resolutionMode: newConfig.resolutionMode as "Fixed Resolution Mode" | "Crop on Resize Mode" | "Dynamic Resolution Mode" | undefined, appId: project.id } })}
              isAdmin={true}
            />

            {/* Build History */}
            <BuildHistory
              projectId={project.id}
              builds={builds}
              onBuildRevert={handleBuildRevert}
              onBuildDelete={handleBuildDelete}
              onBuildActivate={handleBuildActivate}
              onRefresh={fetchBuilds}
              isAdmin={true}
            />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Project Settings */}
            <Card>
              <CardHeader><CardTitle>Project Settings</CardTitle></CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="auto-release" className="text-sm font-medium text-gray-700">
                      Auto-Release
                    </Label>
                    <p className="text-xs text-gray-500">
                      Automatically activate new builds when uploaded
                    </p>
                  </div>
                  <Switch
                    id="auto-release"
                    checked={autoRelease}
                    onCheckedChange={handleAutoReleaseToggle}
                    disabled={loading}
                  />
                </div>
                <div><p className="text-sm font-medium text-gray-500">Created</p>
                <p className="text-sm text-gray-900">{formatDate(project.created_at)}</p></div>
                <div><p className="text-sm font-medium text-gray-500">Last Updated</p>
                <p className="text-sm text-gray-900">{formatDate(project.updated_at)}</p></div>
                <div><p className="text-sm font-medium text-gray-500">Total Builds</p>
                <p className="text-sm text-gray-900">{builds.length}</p></div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Package className="h-5 w-5 mr-2" />
                  Admin Statistics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Builds:</span>
                  <Badge variant="secondary">{builds.length}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Latest Version:</span>
                  <Badge variant="outline">{builds[0]?.version || 'None'}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Owner Role:</span>
                  <Badge variant={project.profiles.role === 'platform_admin' ? 'default' : 'secondary'}>
                    {project.profiles.role}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Owner Email:</span>
                  <span className="text-sm text-gray-500">
                    {project.profiles.email}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Stream ID:</span>
                  <span className="text-sm text-gray-500 font-mono">
                    {project.stream_project_id}
                  </span>
                </div>
              </CardContent>
            </Card>
            {/* <Card>
              <CardHeader><CardTitle>Recent Builds</CardTitle></CardHeader>
              <CardContent>
                {project.builds && project.builds.length > 0 ? (
                  <div className="space-y-3">
                    {project.builds.slice(0, 5).map(build => (
                      <div key={build.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium text-sm">{build.original_filename || build.filename}</p>
                          <p className="text-xs text-gray-500">
                            Version {build.version} • {new Date(build.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <Button size="sm" variant="outline"><Download className="h-4 w-4" /></Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">No builds uploaded yet</p>
                )}
              </CardContent>
            </Card> */}
            {!isEditing && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Settings className="h-5 w-5 mr-2" /> Current Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="text-xs bg-gray-50 p-3 rounded-lg overflow-auto">
                    {JSON.stringify(project.config || {}, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
