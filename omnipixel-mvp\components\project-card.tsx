import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Project } from '@/lib/supabase'
import { Play, Upload, Settings } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface ProjectCardProps {
  project: Project & {
    builds?: Array<{
      id: string
      filename: string
      original_filename?: string
      version: number
      created_at: string
      status?: string
      is_current?: boolean
    }>
  }
}

export function ProjectCard({ project }: ProjectCardProps) {
  // Filter out failed and archived builds
  const activeBuilds = project.builds?.filter(build =>
    !['failed', 'archived'].includes(build.status || '')
  ) || []

  const latestBuild = activeBuilds[0]
  const buildCount = activeBuilds.length

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-lg">{project.name}</CardTitle>
            {/* <CardDescription>
              Stream ID: {project.stream_project_id}
            </CardDescription> */}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/projects/${project.id}`}>
                <Settings className="h-4 w-4 mr-1" />
                Manage
              </Link>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>{buildCount} build{buildCount !== 1 ? 's' : ''}</span>
            {latestBuild && (
              <span>
                Last updated {formatDistanceToNow(new Date(latestBuild.created_at), { addSuffix: true })}
              </span>
            )}
          </div>
          
          {/* {latestBuild && (
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-sm">Latest Build</p>
                  <p className="text-xs text-gray-600">{latestBuild.original_filename || latestBuild.filename}</p>
                  <p className="text-xs text-gray-500">Version {latestBuild.version}</p>
                </div>
                <Button size="sm" asChild>
                  <Link href={`/projects/${project.id}/stream`}>
                    <Play className="h-4 w-4 mr-1" />
                    Stream
                  </Link>
                </Button>
              </div>
            </div>
          )} */}
          
          <div className="flex gap-2">
            <Button variant="outline" className="flex-1" asChild>
              <Link href={`/projects/${project.id}`}>
                <Upload className="h-4 w-4 mr-1" />
                Upload Build
              </Link>
            </Button>
            {/* {latestBuild && (
              <Button className="flex-1" asChild>
                <Link href={`/projects/${project.id}/stream`}>
                  <Play className="h-4 w-4 mr-1" />
                  Stream
                </Link>
              </Button>
            )} */}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
