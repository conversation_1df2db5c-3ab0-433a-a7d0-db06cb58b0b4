import { S3Client } from '@aws-sdk/client-s3'

// Backblaze B2 S3-Compatible Configuration
// See: https://www.backblaze.com/docs/cloud-storage-use-the-aws-sdk-for-javascript-v3-with-backblaze-b2

// Create S3-compatible client for Backblaze B2
export function createBackblazeS3Client(): S3Client {
  console.log('🔧 Creating Backblaze S3 Client...')

  // Get Backblaze credentials from environment
  const BACKBLAZE_APP_KEY_ID = process.env.BACKBLAZE_APP_KEY_ID
  const BACKBLAZE_APP_KEY = process.env.BACKBLAZE_APP_KEY

  console.log('Credentials check:')
  console.log('- BACKBLAZE_APP_KEY_ID:', BACKBLAZE_APP_KEY_ID ? `${BACKBLAZE_APP_KEY_ID.substring(0, 15)}...` : 'MISSING')
  console.log('- BACKBLAZE_APP_KEY:', BACKBLAZE_APP_KEY ? `${BACKBLAZE_APP_KEY.substring(0, 15)}...` : 'MISSING')

  // Backblaze endpoint - use the correct region for your bucket
  // See: https://www.backblaze.com/docs/cloud-storage-s3-compatible-api-endpoint-regions
  const BACKBLAZE_ENDPOINT = 'https://s3.us-east-005.backblazeb2.com'

  console.log('Configuration:')
  console.log('- Endpoint:', BACKBLAZE_ENDPOINT)
  console.log('- Region: us-east-005')
  console.log('- Force Path Style: true')

  if (!BACKBLAZE_APP_KEY_ID || !BACKBLAZE_APP_KEY) {
    console.error('❌ Backblaze credentials missing!')
    throw new Error('Backblaze credentials not configured')
  }

  // Create S3 client with Backblaze configuration
  const client = new S3Client({
    region: 'us-east-005', // Backblaze region (updated to match your endpoint)
    endpoint: BACKBLAZE_ENDPOINT,
    credentials: {
      accessKeyId: BACKBLAZE_APP_KEY_ID,
      secretAccessKey: BACKBLAZE_APP_KEY,
    },
    forcePathStyle: true, // Required for Backblaze B2
  })

  console.log('✅ Backblaze S3 Client created successfully')
  return client
}

// Get Backblaze bucket name
export function getBackblazeBucketName(): string {
  return process.env.BACKBLAZE_BUCKET_NAME || 'omnipixel'
}

// Check if Backblaze is configured
export function isBackblazeConfigured(): boolean {
  return !!(
    process.env.BACKBLAZE_APP_KEY_ID && 
    process.env.BACKBLAZE_APP_KEY && 
    process.env.BACKBLAZE_BUCKET_NAME
  )
}

// Get public URL for Backblaze file
export function getBackblazePublicUrl(key: string): string {
  const bucketName = getBackblazeBucketName()
  // For us-east-005 region, use the correct download URL format
  return `https://f005.backblazeb2.com/file/${bucketName}/${key}`
}

// Export adapter functions
const backblazeAdapter = {
  createBackblazeS3Client,
  getBackblazeBucketName,
  isBackblazeConfigured,
  getBackblazePublicUrl,
}

export default backblazeAdapter
