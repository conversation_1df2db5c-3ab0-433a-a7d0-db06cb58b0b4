import { NextRequest, NextResponse } from 'next/server'
import { 
  completeMultipartUpload,
  isAWSConfigured 
} from '@/lib/aws'
import { createClient } from '@/utils/supabase/server'

export async function POST(request: NextRequest) {
  try {
    // Check if AWS is configured
    if (!isAWSConfigured()) {
      return NextResponse.json(
        { error: 'File upload is not configured. Please contact the administrator.' },
        { status: 503 }
      )
    }

    // Create Supabase client for server-side auth
    const supabase = await createClient()

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { key, uploadId, parts } = body

    // Validate required fields
    if (!key || !uploadId || !parts || !Array.isArray(parts)) {
      return NextResponse.json(
        { error: 'Missing required fields: key, uploadId, parts' },
        { status: 400 }
      )
    }

    // Complete the multipart upload
    const location = await completeMultipartUpload(key, uploadId, parts)

    return NextResponse.json({
      location
    })

  } catch (error) {
    console.error('Error in complete-multipart:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
