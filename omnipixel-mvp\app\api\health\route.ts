import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { S3Client, HeadBucketCommand } from '@aws-sdk/client-s3'

// Health check endpoint for monitoring application status
export async function GET(request: NextRequest) {
  const startTime = Date.now()
  const checks: Record<string, any> = {}

  try {
    // Check database connectivity
    checks.database = await checkDatabase()
    
    // Check AWS S3 connectivity
    checks.aws = await checkAWS()
    
    // Check environment variables
    checks.environment = checkEnvironment()
    
    // Calculate response time
    const responseTime = Date.now() - startTime
    
    // Determine overall health
    const isHealthy = Object.values(checks).every(check => check.status === 'healthy')
    
    const healthStatus = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime}ms`,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks
    }

    return NextResponse.json(healthStatus, {
      status: isHealthy ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })

  } catch (error: any) {
    console.error('Health check failed:', error)
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      responseTime: `${Date.now() - startTime}ms`,
      error: error.message,
      checks
    }, {
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  }
}

async function checkDatabase() {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY

    if (!supabaseUrl || !supabaseKey) {
      return {
        status: 'unhealthy',
        error: 'Missing Supabase configuration'
      }
    }

    const supabase = createClient(supabaseUrl, supabaseKey)
    
    // Simple query to test connectivity
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
      throw error
    }

    return {
      status: 'healthy',
      message: 'Database connection successful',
      url: supabaseUrl
    }

  } catch (error: any) {
    return {
      status: 'unhealthy',
      error: error.message || 'Database connection failed'
    }
  }
}

async function checkAWS() {
  try {
    const region = process.env.AWS_REGION
    const bucketName = process.env.AWS_S3_BUCKET_NAME
    const accessKeyId = process.env.AWS_ACCESS_KEY_ID
    const secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY

    if (!region || !bucketName || !accessKeyId || !secretAccessKey) {
      return {
        status: 'unhealthy',
        error: 'Missing AWS configuration'
      }
    }

    const s3Client = new S3Client({
      region,
      credentials: {
        accessKeyId,
        secretAccessKey
      }
    })

    // Test S3 bucket access
    const command = new HeadBucketCommand({ Bucket: bucketName })
    await s3Client.send(command)

    return {
      status: 'healthy',
      message: 'AWS S3 connection successful',
      region,
      bucket: bucketName
    }

  } catch (error: any) {
    return {
      status: 'unhealthy',
      error: error.message || 'AWS connection failed'
    }
  }
}

function checkEnvironment() {
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'AWS_REGION',
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'AWS_S3_BUCKET_NAME',
    'STREAMPIXEL_API_KEY'
  ]

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName])
  
  if (missingVars.length > 0) {
    return {
      status: 'unhealthy',
      error: `Missing environment variables: ${missingVars.join(', ')}`
    }
  }

  return {
    status: 'healthy',
    message: 'All required environment variables are set',
    nodeVersion: process.version,
    platform: process.platform
  }
}
