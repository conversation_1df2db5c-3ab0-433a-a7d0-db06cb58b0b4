'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { Save, RotateCcw, Settings, MessageSquare, Lock, Loader2 } from 'lucide-react'

interface StreamPlayerConfig {
  // Stream Settings
  autoConnect?: boolean
  touchInput?: boolean
  keyBoardInput?: boolean
  resolutionMode?: string
  maxStreamQuality?: string
  primaryCodec?: string
  fallBackCodec?: string
  
  // Security Settings
  isPasswordProtected?: boolean
  password?: string
  
  // UI Messages
  loadingMessage?: string
  connectingMessage?: string
  disconnectedMessage?: string
  reconnectingMessage?: string
  errorMessage?: string
  connectButtonText?: string
}

interface EnhancedConfigEditorProps {
  projectId: string
  currentConfig: StreamPlayerConfig
  onConfigUpdate?: (newConfig: StreamPlayerConfig) => void
  isAdmin?: boolean
  className?: string
}

export function EnhancedConfigEditor({
  projectId,
  currentConfig,
  onConfigUpdate,
  isAdmin = false,
  className = ''
}: EnhancedConfigEditorProps) {
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [config, setConfig] = useState<StreamPlayerConfig>({
    // Default values
    autoConnect: false,
    touchInput: true,
    keyBoardInput: true,
    resolutionMode: 'Dynamic Resolution Mode',
    maxStreamQuality: '1080p (1920x1080)',
    primaryCodec: 'H264',
    fallBackCodec: 'VP8',
    isPasswordProtected: false,
    password: '',
    loadingMessage: 'Loading stream...',
    connectingMessage: 'Connecting to stream...',
    disconnectedMessage: 'Stream disconnected',
    reconnectingMessage: 'Reconnecting...',
    errorMessage: 'Stream error occurred',
    connectButtonText: 'Connect to Stream',
    ...currentConfig
  })

  useEffect(() => {
    setConfig({
      autoConnect: false,
      touchInput: true,
      keyBoardInput: true,
      resolutionMode: 'Dynamic Resolution Mode',
      maxStreamQuality: '1080p (1920x1080)',
      primaryCodec: 'H264',
      fallBackCodec: 'VP8',
      isPasswordProtected: false,
      password: '',
      loadingMessage: 'Loading stream...',
      connectingMessage: 'Connecting to stream...',
      disconnectedMessage: 'Stream disconnected',
      reconnectingMessage: 'Reconnecting...',
      errorMessage: 'Stream error occurred',
      connectButtonText: 'Connect to Stream',
      ...currentConfig
    })
  }, [currentConfig])

  const handleSave = async () => {
    try {
      setIsSaving(true)
      setError(null)

      // Determine which API endpoint to use
      const endpoint = isAdmin ? '/api/admin/projects' : `/api/projects/${projectId}`
      
      const body = isAdmin 
        ? { project_id: projectId, config }
        : { config }

      const response = await fetch(endpoint, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save configuration')
      }

      onConfigUpdate?.(config)
      alert('Configuration saved successfully!')

    } catch (err: any) {
      setError(err.message)
      console.error('Error saving config:', err)
    } finally {
      setIsSaving(false)
    }
  }

  const handleReset = () => {
    setConfig({
      autoConnect: false,
      touchInput: true,
      keyBoardInput: true,
      resolutionMode: 'Dynamic Resolution Mode',
      maxStreamQuality: '1080p (1920x1080)',
      primaryCodec: 'H264',
      fallBackCodec: 'VP8',
      isPasswordProtected: false,
      password: '',
      loadingMessage: 'Loading stream...',
      connectingMessage: 'Connecting to stream...',
      disconnectedMessage: 'Stream disconnected',
      reconnectingMessage: 'Reconnecting...',
      errorMessage: 'Stream error occurred',
      connectButtonText: 'Connect to Stream',
      ...currentConfig
    })
    setError(null)
  }

  const updateConfig = (key: keyof StreamPlayerConfig, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }))
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Settings className="h-5 w-5 mr-2" />
          Stream Configuration
        </CardTitle>
        <CardDescription>
          Configure stream settings, security, and user interface messages
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <Tabs defaultValue="stream" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="stream">Stream Settings</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="messages">Messages</TabsTrigger>
          </TabsList>

          <TabsContent value="stream" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="autoConnect">Auto Connect</Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="autoConnect"
                    checked={config.autoConnect}
                    onCheckedChange={(checked) => updateConfig('autoConnect', checked)}
                  />
                  <span className="text-sm text-gray-600">
                    {config.autoConnect ? 'Automatically connect on load' : 'Show connect button'}
                  </span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="touchInput">Touch Input</Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="touchInput"
                    checked={config.touchInput}
                    onCheckedChange={(checked) => updateConfig('touchInput', checked)}
                  />
                  <span className="text-sm text-gray-600">Enable touch controls</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="keyBoardInput">Keyboard Input</Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="keyBoardInput"
                    checked={config.keyBoardInput}
                    onCheckedChange={(checked) => updateConfig('keyBoardInput', checked)}
                  />
                  <span className="text-sm text-gray-600">Enable keyboard controls</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="resolutionMode">Resolution Mode</Label>
                <Select
                  value={config.resolutionMode}
                  onValueChange={(value) => updateConfig('resolutionMode', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Dynamic Resolution Mode">Dynamic Resolution</SelectItem>
                    <SelectItem value="Fixed Resolution Mode">Fixed Resolution</SelectItem>
                    <SelectItem value="Adaptive Resolution Mode">Adaptive Resolution</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxStreamQuality">Max Stream Quality</Label>
                <Select
                  value={config.maxStreamQuality}
                  onValueChange={(value) => updateConfig('maxStreamQuality', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="4K (3840x2160)">4K (3840x2160)</SelectItem>
                    <SelectItem value="1440p (2560x1440)">1440p (2560x1440)</SelectItem>
                    <SelectItem value="1080p (1920x1080)">1080p (1920x1080)</SelectItem>
                    <SelectItem value="720p (1280x720)">720p (1280x720)</SelectItem>
                    <SelectItem value="480p (854x480)">480p (854x480)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="primaryCodec">Primary Codec</Label>
                <Select
                  value={config.primaryCodec}
                  onValueChange={(value) => updateConfig('primaryCodec', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="H264">H264</SelectItem>
                    <SelectItem value="H265">H265</SelectItem>
                    <SelectItem value="VP8">VP8</SelectItem>
                    <SelectItem value="VP9">VP9</SelectItem>
                    <SelectItem value="AV1">AV1</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="fallBackCodec">Fallback Codec</Label>
                <Select
                  value={config.fallBackCodec}
                  onValueChange={(value) => updateConfig('fallBackCodec', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="H264">H264</SelectItem>
                    <SelectItem value="VP8">VP8</SelectItem>
                    <SelectItem value="VP9">VP9</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="security" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="isPasswordProtected">Password Protection</Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isPasswordProtected"
                    checked={config.isPasswordProtected}
                    onCheckedChange={(checked) => updateConfig('isPasswordProtected', checked)}
                  />
                  <span className="text-sm text-gray-600">
                    {config.isPasswordProtected ? 'Stream is password protected' : 'Stream is public'}
                  </span>
                </div>
              </div>

              {config.isPasswordProtected && (
                <div className="space-y-2">
                  <Label htmlFor="password">Stream Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={config.password}
                    onChange={(e) => updateConfig('password', e.target.value)}
                    placeholder="Enter stream password"
                  />
                  <p className="text-xs text-gray-500">
                    Users will need this password to access the stream
                  </p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="messages" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="loadingMessage">Loading Message</Label>
                <Input
                  id="loadingMessage"
                  value={config.loadingMessage}
                  onChange={(e) => updateConfig('loadingMessage', e.target.value)}
                  placeholder="Loading stream..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="connectingMessage">Connecting Message</Label>
                <Input
                  id="connectingMessage"
                  value={config.connectingMessage}
                  onChange={(e) => updateConfig('connectingMessage', e.target.value)}
                  placeholder="Connecting to stream..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="disconnectedMessage">Disconnected Message</Label>
                <Input
                  id="disconnectedMessage"
                  value={config.disconnectedMessage}
                  onChange={(e) => updateConfig('disconnectedMessage', e.target.value)}
                  placeholder="Stream disconnected"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="reconnectingMessage">Reconnecting Message</Label>
                <Input
                  id="reconnectingMessage"
                  value={config.reconnectingMessage}
                  onChange={(e) => updateConfig('reconnectingMessage', e.target.value)}
                  placeholder="Reconnecting..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="errorMessage">Error Message</Label>
                <Input
                  id="errorMessage"
                  value={config.errorMessage}
                  onChange={(e) => updateConfig('errorMessage', e.target.value)}
                  placeholder="Stream error occurred"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="connectButtonText">Connect Button Text</Label>
                <Input
                  id="connectButtonText"
                  value={config.connectButtonText}
                  onChange={(e) => updateConfig('connectButtonText', e.target.value)}
                  placeholder="Connect to Stream"
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex items-center justify-between pt-6 border-t">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={isSaving}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          
          <Button
            onClick={handleSave}
            disabled={isSaving}
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Configuration
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
