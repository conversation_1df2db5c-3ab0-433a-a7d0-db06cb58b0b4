# Troubleshooting Guide

Common issues and solutions for OmniPixel MVP.

## 🚨 Common Issues

### Development Environment

#### Node.js Version Issues
**Problem**: Build fails with Node.js version errors
```bash
Error: The engine "node" is incompatible with this module
```

**Solution**:
```bash
# Check Node.js version
node --version

# Install Node.js 18+ if needed
# Download from: https://nodejs.org/
# Or use nvm:
nvm install 18
nvm use 18
```

#### Package Installation Failures
**Problem**: npm install fails with dependency conflicts
```bash
npm ERR! peer dep missing
```

**Solution**:
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and package-lock.json
rm -rf node_modules package-lock.json

# Reinstall dependencies
npm install

# Or use legacy peer deps
npm install --legacy-peer-deps
```

#### Environment Variables Not Loading
**Problem**: Environment variables are undefined
```javascript
console.log(process.env.NEXT_PUBLIC_SUPABASE_URL) // undefined
```

**Solution**:
```bash
# Check .env.local exists
ls -la .env.local

# Copy from template if missing
cp .env.local.example .env.local

# Restart development server
npm run dev
```

### Database Issues

#### Supabase Connection Errors
**Problem**: Cannot connect to Supabase
```javascript
Error: Invalid API key
```

**Solution**:
1. Verify Supabase URL and keys in `.env.local`
2. Check Supabase project status
3. Regenerate API keys if needed
4. Ensure RLS policies are configured

#### Database Migration Failures
**Problem**: Migrations fail to apply
```sql
ERROR: relation "projects" does not exist
```

**Solution**:
```bash
# Check migration files
ls supabase/migrations/

# Apply migrations manually in Supabase dashboard
# Or reset database and reapply
```

#### Row Level Security Issues
**Problem**: Users cannot access their data
```javascript
Error: new row violates row-level security policy
```

**Solution**:
1. Check RLS policies in Supabase dashboard
2. Verify user authentication
3. Update policies if needed:
```sql
-- Example: Allow users to read their own projects
CREATE POLICY "Users can view own projects" ON projects
FOR SELECT USING (auth.uid() = user_id);
```

### Authentication Issues

#### Login/Signup Failures
**Problem**: Authentication doesn't work
```javascript
Error: Invalid login credentials
```

**Solution**:
1. Check Supabase Auth configuration
2. Verify email confirmation settings
3. Check redirect URLs
4. Test with different browsers

#### Session Management Issues
**Problem**: Users get logged out frequently
```javascript
Error: JWT expired
```

**Solution**:
1. Check session timeout settings
2. Implement proper token refresh
3. Handle auth state changes:
```javascript
supabase.auth.onAuthStateChange((event, session) => {
  if (event === 'SIGNED_OUT') {
    // Handle logout
  }
})
```

### File Upload Issues

#### Large File Upload Failures
**Problem**: Files over certain size fail to upload
```javascript
Error: Request entity too large
```

**Solution**:
1. Check AWS S3 configuration
2. Verify multipart upload settings
3. Increase timeout values
4. Check network stability

#### Presigned URL Errors
**Problem**: Cannot generate presigned URLs
```javascript
Error: Access denied
```

**Solution**:
1. Check AWS credentials
2. Verify S3 bucket permissions
3. Check CORS configuration
4. Validate IAM policies

### Streaming Issues

#### StreamPixel Connection Failures
**Problem**: Cannot connect to StreamPixel
```javascript
Error: Failed to establish streaming connection
```

**Solution**:
1. Verify StreamPixel API key
2. Check project configuration
3. Test network connectivity
4. Review StreamPixel dashboard

#### Stream Quality Issues
**Problem**: Poor streaming quality or lag
```javascript
Warning: High latency detected
```

**Solution**:
1. Check internet bandwidth
2. Adjust stream quality settings
3. Use different codec
4. Test from different locations

### Deployment Issues

#### AWS Deployment Failures
**Problem**: SAM deployment fails
```bash
Error: Stack creation failed
```

**Solution**:
```bash
# Check AWS credentials
aws sts get-caller-identity

# Verify SAM CLI installation
sam --version

# Check CloudFormation logs
aws cloudformation describe-stack-events --stack-name omnipixel-dev

# Validate template
sam validate --template-file template.yaml
```

#### Vercel Deployment Issues
**Problem**: Vercel build fails
```bash
Error: Build failed
```

**Solution**:
1. Check build logs in Vercel dashboard
2. Verify environment variables
3. Test build locally:
```bash
npm run build
```
4. Check Node.js version compatibility

### Performance Issues

#### Slow Page Load Times
**Problem**: Pages take too long to load
```javascript
// Lighthouse score < 50
```

**Solution**:
1. Optimize images and assets
2. Implement code splitting
3. Use Next.js Image component
4. Enable compression
5. Check database query performance

#### Memory Leaks
**Problem**: Application uses too much memory
```javascript
Error: JavaScript heap out of memory
```

**Solution**:
1. Check for memory leaks in components
2. Properly cleanup event listeners
3. Use React.memo for expensive components
4. Optimize large data sets

## 🔧 Debug Tools

### Development Tools
```bash
# Enable debug mode
DEBUG=* npm run dev

# Check bundle size
npm run build
npm run analyze

# Profile performance
npm run dev -- --profile
```

### Browser DevTools
1. **Network Tab**: Check API requests and responses
2. **Console Tab**: View error messages and logs
3. **Application Tab**: Check localStorage and cookies
4. **Performance Tab**: Profile rendering performance

### Database Debugging
```sql
-- Check table structure
\d projects

-- View recent logs
SELECT * FROM auth.audit_log_entries 
ORDER BY created_at DESC 
LIMIT 10;

-- Check RLS policies
SELECT * FROM pg_policies 
WHERE tablename = 'projects';
```

### AWS Debugging
```bash
# Check CloudFormation stack
aws cloudformation describe-stacks --stack-name omnipixel-dev

# View Lambda logs
aws logs tail /aws/lambda/omnipixel-presigned-url-dev --follow

# Check S3 bucket
aws s3 ls s3://your-bucket-name

# Test API Gateway
curl -X GET https://your-api-id.execute-api.region.amazonaws.com/dev/health
```

## 📊 Monitoring

### Application Monitoring
```javascript
// Add error boundary
class ErrorBoundary extends React.Component {
  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo)
    // Send to monitoring service
  }
}

// Monitor API responses
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
})

api.interceptors.response.use(
  response => response,
  error => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)
```

### Performance Monitoring
```javascript
// Monitor Core Web Vitals
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

getCLS(console.log)
getFID(console.log)
getFCP(console.log)
getLCP(console.log)
getTTFB(console.log)
```

## 🆘 Getting Help

### Before Asking for Help
1. **Check this troubleshooting guide**
2. **Search existing issues** on GitHub
3. **Check the documentation**
4. **Try reproducing** the issue in a clean environment
5. **Gather relevant information**:
   - Error messages
   - Browser/OS version
   - Steps to reproduce
   - Expected vs actual behavior

### Information to Include
```markdown
## Bug Report

**Environment:**
- OS: [e.g., Windows 10, macOS 12.0]
- Browser: [e.g., Chrome 96, Firefox 95]
- Node.js: [e.g., 18.12.0]
- npm: [e.g., 8.19.2]

**Steps to Reproduce:**
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected Behavior:**
A clear description of what you expected to happen.

**Actual Behavior:**
A clear description of what actually happened.

**Screenshots:**
If applicable, add screenshots to help explain your problem.

**Additional Context:**
Add any other context about the problem here.
```

### Support Channels
1. **GitHub Issues**: For bugs and feature requests
2. **Documentation**: Check docs/ directory
3. **Community**: Discord/Slack channels (if available)
4. **Email**: Direct support contact

## 🔄 Recovery Procedures

### Database Recovery
```bash
# Backup current state
pg_dump your_database > backup.sql

# Reset to known good state
# Apply migrations from scratch
# Restore data from backup if needed
```

### File System Recovery
```bash
# Reset to clean state
git clean -fdx
git reset --hard HEAD

# Reinstall dependencies
npm install

# Restart development
npm run dev
```

### AWS Resource Recovery
```bash
# Delete failed stack
aws cloudformation delete-stack --stack-name omnipixel-dev

# Wait for deletion
aws cloudformation wait stack-delete-complete --stack-name omnipixel-dev

# Redeploy
npm run deploy:aws
```

## 📋 Preventive Measures

### Regular Maintenance
1. **Update dependencies** regularly
2. **Monitor error logs** daily
3. **Run tests** before deployments
4. **Backup databases** regularly
5. **Review performance** metrics weekly

### Best Practices
1. **Use TypeScript** for type safety
2. **Implement error boundaries** in React
3. **Add comprehensive logging**
4. **Monitor application metrics**
5. **Test in multiple environments**

### Health Checks
```javascript
// API health check endpoint
export default function handler(req, res) {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    database: 'connected', // Check database connection
    storage: 'available',  // Check S3 connection
  }
  
  res.status(200).json(health)
}
```
