import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

export async function GET(request: NextRequest) {
  try {
    console.log('Debug API - Environment check:')
    console.log('SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)
    console.log('SUPABASE_KEY:', process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY?.substring(0, 20) + '...')
    console.log('Debug API - All cookies:', request.cookies.getAll().map(c => `${c.name}=${c.value.substring(0, 20)}...`))

    // Create Supabase client for server-side auth
    const supabase = await createClient();

    // Get the current user (secure method)
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError) {
      return NextResponse.json({
        error: 'Auth error',
        details: authError.message,
        user: null,
        profile: null,
      })
    }

    if (!user) {
      return NextResponse.json({
        error: 'No user',
        user: null,
        profile: null,
      })
    }

    // Try to fetch the profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError) {
      return NextResponse.json({
        error: 'Profile error',
        details: profileError.message,
        user: {
          user_id: user.id,
          email: user.email,
        },
        profile: null,
      })
    }

    // Try to fetch projects
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('id, name, stream_project_id, user_id')
      .limit(5)

    return NextResponse.json({
      success: true,
      user: {
        user_id: user.id,
        email: user.email,
      },
      profile,
      projects: projects || [],
      projectsError: projectsError?.message || null,
    })

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "unknown error";
    return NextResponse.json({
      error: 'Server error',
      details: errorMessage,
      user: null,
      profile: null,
    })
  }
}
