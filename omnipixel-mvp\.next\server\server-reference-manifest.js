self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"402951f6646b7bd60a67f4d66bdcdd1178e9a6401b\": {\n      \"workers\": {\n        \"app/login/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/login/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/login/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/login/page\": \"action-browser\"\n      }\n    },\n    \"4093c8445d5d287d1ecab4044e59413bd0e509a8a5\": {\n      \"workers\": {\n        \"app/login/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/login/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/login/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/login/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"3QuKliZi9urtbizTiSNs8TuZabyDOYtXickNI45x4xs=\"\n}"