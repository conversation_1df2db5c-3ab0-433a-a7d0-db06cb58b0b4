{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/components/stream-player.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect, useRef } from 'react'\r\nimport { StreamPixelApplication } from 'streampixelsdk'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\r\nimport {\r\n  Play,\r\n  Square,\r\n  Volume2,\r\n  VolumeX,\r\n  Maximize,\r\n  Loader2,\r\n  AlertCircle,\r\n  ExternalLink,\r\n  Code,\r\n  Lock,\r\n  Unlock\r\n} from 'lucide-react'\r\nimport { Project } from '@/lib/supabase'\r\n\r\ninterface StreamPlayerProps {\r\n  projectId: string\r\n  buildId?: string\r\n  className?: string\r\n  config?: StreamPlayerConfig\r\n  showControls?: boolean\r\n  showHeader?: boolean\r\n  showEmbedButton?: boolean\r\n  width?: number\r\n  height?: number\r\n  isEmbedded?: boolean\r\n  enableIframeComms?: boolean\r\n  projectData?: Project // Pre-fetched project data for embed contexts\r\n}\r\n\r\ninterface StreamPlayerConfig {\r\n  autoConnect?: boolean\r\n  touchInput?: boolean\r\n  keyBoardInput?: boolean\r\n  resolutionMode?: string\r\n  maxStreamQuality?: string\r\n  primaryCodec?: string\r\n  fallBackCodec?: string\r\n  isPasswordProtected?: boolean\r\n  password?: string\r\n  loadingMessage?: string\r\n  connectingMessage?: string\r\n  disconnectedMessage?: string\r\n  reconnectingMessage?: string\r\n  errorMessage?: string\r\n  connectButtonText?: string\r\n}\r\n\r\nexport function StreamPlayer({\r\n  projectId,\r\n  buildId,\r\n  className = '',\r\n  config: propConfig,\r\n  showControls = true,\r\n  showHeader = true,\r\n  showEmbedButton = true,\r\n  width = 1280,\r\n  height = 720,\r\n  isEmbedded = false,\r\n  enableIframeComms = false,\r\n  projectData: preloadedProjectData\r\n}: StreamPlayerProps) {\r\n  const [isLoading, setIsLoading] = useState(false)\r\n  const [isPlaying, setIsPlaying] = useState(false)\r\n  const [isMuted, setIsMuted] = useState(false)\r\n  const [streamProjectId, setStreamProjectId] = useState<string | null>(null)\r\n  const [config, setConfig] = useState<StreamPlayerConfig | null>(null)\r\n  const [projectData, setProjectData] = useState<Project | null>(preloadedProjectData || null)\r\n  const [isFullscreen, setIsFullscreen] = useState(false)\r\n  const [error, setError] = useState<string | null>(null)\r\n  const [streamStatus, setStreamStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')\r\n  const [loadingMessage, setLoadingMessage] = useState<string>('Loading stream...')\r\n\r\n  const [isPasswordPromptOpen, setIsPasswordPromptOpen] = useState(false)\r\n  const [enteredPassword, setEnteredPassword] = useState('')\r\n  const [isPasswordValid, setIsPasswordValid] = useState(false)\r\n  const [showEmbedDialog, setShowEmbedDialog] = useState(false)\r\n\r\n  const containerRef = useRef<HTMLDivElement>(null) // for overlays/background\r\n  const sdkMountRef = useRef<HTMLDivElement>(null)   // ONLY for SDK-injected DOM\r\n\r\n  const streamRef = useRef<any>(null)\r\n  const playerRef = useRef<any>(null)\r\n  const uiControlRef = useRef<any>(null)\r\n  const isInitializedRef = useRef<boolean>(false)\r\n  const isInitializingRef = useRef<boolean>(false)\r\n\r\n  // Project data and config loader\r\n  useEffect(() => {\r\n    const fetchProjectData = async () => {\r\n      try {\r\n        setIsLoading(true)\r\n        setError(null)\r\n        const response = await fetch(`/api/projects/${projectId}`)\r\n        if (!response.ok) throw new Error('Failed to fetch project data')\r\n        const responseData = await response.json()\r\n        const data = responseData.project || responseData\r\n        setProjectData(data)\r\n        setStreamProjectId(data.stream_project_id)\r\n        const finalConfig = propConfig || data.config || {}\r\n        setConfig(finalConfig)\r\n        if (!data.stream_project_id) throw new Error('No Stream Service ID found in project data')\r\n      } catch (err) {\r\n        setError('Failed to load project configuration')\r\n      } finally {\r\n        setIsLoading(false)\r\n      }\r\n    }\r\n\r\n    const initializeFromPreloadedData = () => {\r\n      try {\r\n        setIsLoading(true)\r\n        setError(null)\r\n        if (!preloadedProjectData) throw new Error('No project data provided')\r\n        setStreamProjectId(preloadedProjectData.stream_project_id)\r\n        const finalConfig = propConfig || preloadedProjectData.config || {}\r\n        setConfig(finalConfig)\r\n        if (!preloadedProjectData.stream_project_id) throw new Error('No Stream Service ID found in project data')\r\n      } catch (err) {\r\n        setError('Failed to load project configuration')\r\n      } finally {\r\n        setIsLoading(false)\r\n      }\r\n    }\r\n\r\n    if (projectId) {\r\n      if (preloadedProjectData) {\r\n        // Use preloaded data (for embed contexts)\r\n        initializeFromPreloadedData()\r\n      } else {\r\n        // Fetch data from API (for authenticated contexts)\r\n        fetchProjectData()\r\n      }\r\n    }\r\n  }, [projectId, propConfig, preloadedProjectData])\r\n\r\n  // Clean up SDK DOM on unmount/disconnect\r\n  useEffect(() => {\r\n    return () => { cleanup() }\r\n  }, [])\r\n\r\n  // Auto-connect for embedded mode\r\n  useEffect(() => {\r\n    if (config?.autoConnect && streamProjectId && !isPlaying && !isInitializingRef.current) {\r\n      console.log('🚀 Auto-connecting stream...', {\r\n        autoConnect: config?.autoConnect,\r\n        streamProjectId,\r\n        isPlaying,\r\n        isInitializing: isInitializingRef.current\r\n      })\r\n      handlePlay()\r\n    }\r\n  }, [config?.autoConnect, streamProjectId, isPlaying])\r\n\r\n  // Prepare messages\r\n  const messages = {\r\n    loading: config?.loadingMessage ?? 'Loading stream...',\r\n    connecting: config?.connectingMessage ?? 'Connecting to stream...',\r\n    disconnected: config?.disconnectedMessage ?? 'Stream disconnected',\r\n    reconnecting: config?.reconnectingMessage ?? 'Reconnecting...',\r\n    error: config?.errorMessage ?? 'Stream error occurred',\r\n    connectButton: config?.connectButtonText ?? 'Connect to Stream'\r\n  }\r\n\r\n  const createStreamConfig = () => {\r\n    if (!streamProjectId || !config) return null\r\n    return {\r\n      AutoConnect: true,\r\n      appId: streamProjectId,\r\n      touchInput: config?.touchInput ?? true,\r\n      keyBoardInput: config?.keyBoardInput ?? true,\r\n      resolutionMode: (config?.resolutionMode as \"Dynamic Resolution Mode\" | \"Fixed Resolution Mode\" | \"Crop on Resize Mode\") ?? \"Dynamic Resolution Mode\",\r\n      maxStreamQuality: config?.maxStreamQuality ?? \"1080p (1920x1080)\",\r\n      primaryCodec: (config?.primaryCodec as \"H264\" | \"VP8\" | \"AV1\" | \"VP9\") ?? \"H264\",\r\n      fallBackCodec: (config?.fallBackCodec as \"H264\" | \"VP8\" | \"AV1\" | \"VP9\") ?? \"VP8\"\r\n    }\r\n  }\r\n\r\n const initializeStream = async () => {\r\n  if (isInitializingRef.current) return // guard against parallel inits\r\n  isInitializingRef.current = true\r\n  setIsLoading(true)\r\n  setError(null)\r\n\r\n  try {\r\n    const streamConfig = createStreamConfig()\r\n    if (!streamConfig) throw new Error('Cannot create stream config: missing required data')\r\n    const { appStream, pixelStreaming, UIControl } = await StreamPixelApplication(streamConfig)\r\n    streamRef.current = appStream\r\n    playerRef.current = pixelStreaming\r\n    uiControlRef.current = UIControl\r\n\r\n    // Inject SDK DOM\r\n    if (sdkMountRef.current && appStream.rootElement) {\r\n      if (appStream.rootElement.parentNode) {\r\n        appStream.rootElement.parentNode.removeChild(appStream.rootElement)\r\n      }\r\n      sdkMountRef.current.innerHTML = ''\r\n      sdkMountRef.current.appendChild(appStream.rootElement)\r\n    }\r\n\r\n    setupEventListeners()\r\n    isInitializedRef.current = true\r\n    // Keep loading true - only onVideoInitialized will set it to false\r\n  } catch (err: unknown) {\r\n    setError(err instanceof Error? err.message : 'Failed to initialize stream')\r\n    setIsLoading(false)\r\n  } finally {\r\n    isInitializingRef.current = false\r\n  }\r\n}\r\n  const cleanup = () => {\r\n   try {\r\n  \r\n\r\n\r\n    if (streamRef.current?.stream?.disconnect) streamRef.current.stream.disconnect()\r\n    if (playerRef.current?.disconnect) playerRef.current.disconnect()\r\n\r\n  \r\n    if (sdkMountRef.current) sdkMountRef.current.innerHTML = ''\r\n  } catch (err) {}\r\n  // Nullify refs\r\n  streamRef.current = null\r\n  playerRef.current = null\r\n  uiControlRef.current = null\r\n  isInitializedRef.current = false\r\n  isInitializingRef.current = false\r\n  }\r\n\r\n  // Iframe communication system\r\n  const sendMessageToParent = (type: string, data: any = {}) => {\r\n    if (enableIframeComms && typeof window !== 'undefined' && window.parent !== window) {\r\n      try {\r\n        window.parent.postMessage({\r\n          type: `omnipixel-${type}`,\r\n          projectId,\r\n          buildId,\r\n          timestamp: new Date().toISOString(),\r\n          ...data\r\n        }, '*')\r\n      } catch (error) {\r\n        console.warn('Failed to send message to parent:', error)\r\n      }\r\n    }\r\n  }\r\n\r\n  // Listen for messages from parent iframe\r\n  useEffect(() => {\r\n    if (!enableIframeComms || typeof window === 'undefined') return\r\n\r\n    const handleMessage = (event: MessageEvent) => {\r\n      if (!event.data?.type?.startsWith('omnipixel-')) return\r\n\r\n      const { type, data } = event.data\r\n      console.log('📨 Received message from parent:', type, data)\r\n\r\n      switch (type) {\r\n        case 'omnipixel-connect':\r\n          if (!isPlaying) {\r\n            handlePlay()\r\n          }\r\n          break\r\n        case 'omnipixel-disconnect':\r\n          if (isPlaying) {\r\n            handlePause()\r\n          }\r\n          break\r\n        case 'omnipixel-mute':\r\n          setIsMuted(true)\r\n          break\r\n        case 'omnipixel-unmute':\r\n          setIsMuted(false)\r\n          break\r\n        case 'omnipixel-fullscreen':\r\n          setIsFullscreen(true)\r\n          break\r\n        case 'omnipixel-exit-fullscreen':\r\n          setIsFullscreen(false)\r\n          break\r\n        case 'omnipixel-send-input':\r\n          // Forward input to StreamPixel\r\n          if (playerRef.current && data.input) {\r\n            // Handle different input types\r\n            if (data.input.type === 'keyboard') {\r\n              // Send keyboard input to StreamPixel\r\n            } else if (data.input.type === 'mouse') {\r\n              // Send mouse input to StreamPixel\r\n            }\r\n          }\r\n          break\r\n      }\r\n    }\r\n\r\n    window.addEventListener('message', handleMessage)\r\n    return () => window.removeEventListener('message', handleMessage)\r\n  }, [enableIframeComms, isPlaying, projectId, buildId])\r\n\r\n  // Send status updates to parent\r\n  useEffect(() => {\r\n    if (enableIframeComms) {\r\n      sendMessageToParent('status-change', {\r\n        isLoading,\r\n        isPlaying,\r\n        streamStatus,\r\n        error,\r\n        isMuted,\r\n        isFullscreen\r\n      })\r\n    }\r\n  }, [enableIframeComms, isLoading, isPlaying, streamStatus, error, isMuted, isFullscreen])\r\n\r\n  // Event listeners (simplified for clarity, you can add more as needed)\r\n  const setupEventListeners = () => {\r\n    if (!streamRef.current) return\r\n\r\n    streamRef.current.onConnectAction = () => {\r\n      console.log('🔗 Stream: Connect action triggered')\r\n      setStreamStatus('connecting')\r\n      setIsLoading(true)\r\n      setLoadingMessage(config?.connectingMessage ?? 'Connecting to stream...')\r\n      sendMessageToParent('connect-started')\r\n    }\r\n\r\n    streamRef.current.onWebRtcConnecting = () => {\r\n      console.log('🌐 Stream: WebRTC connecting')\r\n      setStreamStatus('connecting')\r\n      setLoadingMessage(config?.connectingMessage ?? 'Establishing connection...')\r\n      sendMessageToParent('webrtc-connecting')\r\n    }\r\n\r\n    streamRef.current.onWebRtcConnected = () => {\r\n      console.log('✅ Stream: WebRTC connected')\r\n      setStreamStatus('connecting')\r\n      setLoadingMessage('Initializing video stream...')\r\n      // Don't hide loading yet - wait for video to be ready\r\n      sendMessageToParent('webrtc-connected')\r\n    }\r\n\r\n    streamRef.current.onVideoInitialized = () => {\r\n      console.log('🎥 Stream: Video initialized and ready')\r\n      console.log('🔄 Setting loading state to false - video ready')\r\n      setIsLoading(false) // Hide loading when video is actually ready\r\n      setStreamStatus('connected')\r\n      setIsPlaying(true)\r\n      sendMessageToParent('video-initialized')\r\n    }\r\n\r\n    streamRef.current.onDisconnect = () => {\r\n      console.log('🔌 Stream: Disconnected')\r\n      setIsPlaying(false)\r\n      setIsLoading(false)\r\n      setStreamStatus('disconnected')\r\n      setLoadingMessage(config?.disconnectedMessage ?? 'Stream disconnected')\r\n      sendMessageToParent('disconnected')\r\n\r\n      // Use requestAnimationFrame for React-safe cleanup\r\n      requestAnimationFrame(() => {\r\n        cleanup()\r\n      })\r\n    }\r\n\r\n    // Additional Stream Service events for iframe communication\r\n    if (streamRef.current.onDataChannelOpen) {\r\n      streamRef.current.onDataChannelOpen = () => {\r\n        console.log('📡 Stream: Data channel opened')\r\n        sendMessageToParent('data-channel-open')\r\n      }\r\n    }\r\n\r\n    if (streamRef.current.onDataChannelMessage) {\r\n      streamRef.current.onDataChannelMessage = (message: any) => {\r\n        console.log('📨 Stream: Data channel message', message)\r\n        sendMessageToParent('data-channel-message', { message })\r\n      }\r\n    }\r\n\r\n    // Unreal Engine specific events\r\n    if (streamRef.current.onUnrealMessage) {\r\n      streamRef.current.onUnrealMessage = (message: any) => {\r\n        console.log('🎮 Unreal Engine message:', message)\r\n        sendMessageToParent('unreal-message', { message })\r\n      }\r\n    }\r\n  }\r\n\r\n  // Control handlers\r\n  const handlePlay = async () => {\r\n    console.log('▶️ Play button clicked')\r\n    sendMessageToParent('play-requested')\r\n\r\n    console.log('🔄 Setting loading state to true')\r\n    setIsLoading(true)\r\n    setLoadingMessage(config?.loadingMessage ?? 'Initializing stream...')\r\n\r\n    cleanup()\r\n    await initializeStream()\r\n    setStreamStatus('connecting')\r\n    setIsPlaying(true)\r\n  }\r\n\r\n  const handlePause = () => {\r\n    console.log('⏸️ Pause button clicked')\r\n    sendMessageToParent('pause-requested')\r\n\r\n    playerRef.current?.disconnect()\r\n    streamRef.current?.stream?.disconnect()\r\n\r\n    // Use requestAnimationFrame for React-safe state updates\r\n    requestAnimationFrame(() => {\r\n      setIsPlaying(false)\r\n      setStreamStatus('disconnected')\r\n    })\r\n\r\n    cleanup()\r\n    window.location.reload();\r\n  }\r\n\r\n  const handleMute = () => {\r\n    console.log('🔇 Mute button clicked')\r\n    if (uiControlRef.current) {\r\n      uiControlRef.current.toggleAudio()\r\n      setIsMuted(!isMuted)\r\n      sendMessageToParent('mute-toggled', { isMuted: !isMuted })\r\n    }\r\n  }\r\n  const handleFullscreen = () => {\r\n    if (!containerRef.current) return\r\n    if (!isFullscreen) {\r\n      containerRef.current.requestFullscreen?.()\r\n    } else {\r\n      document.exitFullscreen?.()\r\n    }\r\n    setIsFullscreen(!isFullscreen)\r\n  }\r\n\r\n  // Password dialog handler\r\n  const handlePasswordSubmit = () => {\r\n    if (enteredPassword === config?.password) {\r\n      setIsPasswordValid(true)\r\n      setIsPasswordPromptOpen(false)\r\n    } else {\r\n      setError('Invalid password')\r\n    }\r\n  }\r\n\r\n  // Embed code utilities\r\n  const generateEmbedCode = () => {\r\n    const origin = typeof window !== 'undefined' ? window.location.origin : 'https://your-domain.com'\r\n    const params = new URLSearchParams()\r\n\r\n    // Add control visibility parameters\r\n    if (!showControls) params.set('hideControls', 'true')\r\n    if (!showHeader) params.set('hideHeader', 'true')\r\n    if (!showEmbedButton) params.set('hideEmbedButton', 'true')\r\n    if (config?.autoConnect) params.set('autoConnect', 'true')\r\n\r\n    const queryString = params.toString()\r\n    const embedUrl = `${origin}/embed/${projectId}${queryString ? `?${queryString}` : ''}`\r\n\r\n    return `<!-- OmniPixel Interactive Stream Embed -->\r\n<iframe\r\n  src=\"${embedUrl}\"\r\n  width=\"${width}\"\r\n  height=\"${height}\"\r\n  frameborder=\"0\"\r\n  allowfullscreen\r\n  allow=\"camera; microphone; fullscreen\"\r\n  style=\"border: none; border-radius: 8px;\">\r\n</iframe>\r\n\r\n<!-- Optional: Listen for iframe events -->\r\n<script>\r\nwindow.addEventListener('message', function(event) {\r\n  if (event.data?.type?.startsWith('omnipixel-')) {\r\n    console.log('Stream event:', event.data.type, event.data);\r\n\r\n    // Handle specific events\r\n    switch(event.data.type) {\r\n      case 'omnipixel-status-change':\r\n        console.log('Stream status:', event.data.streamStatus);\r\n        break;\r\n      case 'omnipixel-webrtc-connected':\r\n        console.log('Stream connected successfully');\r\n        break;\r\n      case 'omnipixel-unreal-message':\r\n        console.log('Unreal Engine message:', event.data.message);\r\n        break;\r\n    }\r\n  }\r\n});\r\n\r\n// Optional: Send commands to the stream\r\nfunction connectStream() {\r\n  document.querySelector('iframe').contentWindow.postMessage({\r\n    type: 'omnipixel-connect'\r\n  }, '*');\r\n}\r\n\r\nfunction disconnectStream() {\r\n  document.querySelector('iframe').contentWindow.postMessage({\r\n    type: 'omnipixel-disconnect'\r\n  }, '*');\r\n}\r\n</script>`\r\n  }\r\n  const copyEmbedCode = async () => {\r\n    try {\r\n      await navigator.clipboard.writeText(generateEmbedCode())\r\n      alert('Embed code copied to clipboard!')\r\n    } catch { }\r\n  }\r\n  const openInNewTab = () => {\r\n    if (typeof window !== 'undefined') {\r\n      const url = `${window.location.origin}/projects/${projectId}`\r\n      window.open(url, '_blank', 'noopener,noreferrer')\r\n    }\r\n  }\r\n\r\n  const getStatusColor = () => {\r\n    switch (streamStatus) {\r\n      case 'connected': return 'bg-green-100 text-green-800'\r\n      case 'connecting': return 'bg-yellow-100 text-yellow-800'\r\n      case 'disconnected': return 'bg-gray-100 text-gray-800'\r\n      case 'error': return 'bg-red-100 text-red-800'\r\n      default: return 'bg-gray-100 text-gray-800'\r\n    }\r\n  }\r\n  const getStatusText = () => {\r\n    switch (streamStatus) {\r\n      case 'connected': return 'Connected'\r\n      case 'connecting': return 'Connecting...'\r\n      case 'disconnected': return 'Disconnected'\r\n      case 'error': return 'Error'\r\n      default: return 'Unknown'\r\n    }\r\n  }\r\n\r\n  // Password protection UI\r\n  if (config?.isPasswordProtected && !isPasswordValid && isPasswordPromptOpen) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardContent className=\"pt-6\">\r\n          <div className=\"text-center space-y-4\">\r\n            <Lock className=\"h-12 w-12 mx-auto text-gray-400\" />\r\n            <h3 className=\"text-lg font-medium\">Password Protected</h3>\r\n            <p className=\"text-gray-600\">This stream requires a password to access.</p>\r\n            <div className=\"max-w-sm mx-auto space-y-3\">\r\n              <Input\r\n                type=\"password\"\r\n                placeholder=\"Enter password\"\r\n                value={enteredPassword}\r\n                onChange={(e) => setEnteredPassword(e.target.value)}\r\n                onKeyDown={(e) => e.key === 'Enter' && handlePasswordSubmit()}\r\n              />\r\n              <Button onClick={handlePasswordSubmit} className=\"w-full\">\r\n                <Unlock className=\"h-4 w-4 mr-2\" />\r\n                Access Stream\r\n              </Button>\r\n            </div>\r\n            {error && <p className=\"text-sm text-red-600\">{error}</p>}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    )\r\n  }\r\n\r\n  // Full-screen embedded mode\r\n  if (isEmbedded && enableIframeComms) {\r\n    return (\r\n      <div className={`relative w-full h-full ${className}`}>\r\n        {/* Stream container */}\r\n        <div\r\n          ref={containerRef}\r\n          className=\"absolute inset-0 bg-black\"\r\n        >\r\n          {/* Stream Service SDK mount point - hidden during loading/connecting */}\r\n          <div\r\n            ref={sdkMountRef}\r\n            className=\"absolute inset-0 w-full h-full\"\r\n            style={{\r\n              visibility: (isLoading || streamStatus === 'connecting') ? 'hidden' : 'visible',\r\n              zIndex: 1\r\n            }}\r\n          />\r\n          {/* Loading overlay - visible only during active loading/connecting */}\r\n          {(isLoading || streamStatus === 'connecting') && (\r\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-900/95\" style={{ zIndex: 30 }}>\r\n              <div className=\"text-center text-white\">\r\n                <Loader2 className=\"h-12 w-12 animate-spin mx-auto mb-4\" />\r\n                <p className=\"text-lg font-medium mb-2\">{loadingMessage}</p>\r\n                <p className=\"text-sm opacity-75\">Status: {getStatusText()}</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Error overlay */}\r\n          {error && (\r\n            <div className=\"absolute inset-0 flex items-center justify-center bg-red-900\" style={{ zIndex: 40 }}>\r\n              <div className=\"text-center text-white p-4\">\r\n                <AlertCircle className=\"h-8 w-8 mx-auto mb-2\" />\r\n                <p>{error}</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Floating control buttons - no bar background */}\r\n          {showControls && (\r\n            <>\r\n              {/* Status badges - top left */}\r\n              <div className=\"absolute top-4 left-4 z-30 flex items-center space-x-2\">\r\n                <Badge className={getStatusColor()}>{getStatusText()}</Badge>\r\n                {buildId && (\r\n                  <Badge variant=\"outline\" className=\"text-white border-white/50 bg-black/70 backdrop-blur-sm\">\r\n                    Build: {buildId.slice(0, 8)}...\r\n                  </Badge>\r\n                )}\r\n              </div>\r\n\r\n              {/* Control buttons - bottom right */}\r\n              <div className=\"absolute bottom-4 right-4 z-30 flex items-center space-x-2\">\r\n                {!isPlaying ? (\r\n                  <Button\r\n                    onClick={handlePlay}\r\n                    size=\"sm\"\r\n                    className=\"bg-green-600/90 hover:bg-green-700 text-white font-medium px-4 backdrop-blur-sm shadow-lg\"\r\n                  >\r\n                    <Play className=\"h-4 w-4 mr-2\" />\r\n                    Connect\r\n                  </Button>\r\n                ) : (\r\n                  <Button\r\n                    onClick={handlePause}\r\n                    variant=\"destructive\"\r\n                    size=\"sm\"\r\n                    className=\"bg-red-600/90 hover:bg-red-700 text-white font-medium px-4 backdrop-blur-sm shadow-lg\"\r\n                  >\r\n                    <Square className=\"h-4 w-4 mr-2\" />\r\n                    Disconnect\r\n                  </Button>\r\n                )}\r\n\r\n                <Button\r\n                  onClick={handleMute}\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  className=\"text-white border-white/50 bg-black/70 hover:bg-black/80 hover:text-white backdrop-blur-sm shadow-lg\"\r\n                >\r\n                  {isMuted ? <VolumeX className=\"h-4 w-4\" /> : <Volume2 className=\"h-4 w-4\" />}\r\n                </Button>\r\n\r\n                <Button\r\n                  onClick={handleFullscreen}\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  className=\"text-white border-white/50 bg-black/70 hover:bg-black/80 hover:text-white backdrop-blur-sm shadow-lg\"\r\n                >\r\n                  <Maximize className=\"h-4 w-4\" />\r\n                </Button>\r\n              </div>\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // Standard card mode\r\n  return (\r\n    <Card className={className}>\r\n      {showHeader && (\r\n        <CardHeader>\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <CardTitle>Interactive Stream</CardTitle>\r\n              <CardDescription>Real-time interactive streaming experience</CardDescription>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <Badge className={getStatusColor()}>{getStatusText()}</Badge>\r\n              {buildId && (\r\n                <Badge variant=\"outline\">\r\n                  Build: {buildId.slice(0, 8)}...\r\n                </Badge>\r\n              )}\r\n              {showEmbedButton && (\r\n                <Dialog open={showEmbedDialog} onOpenChange={setShowEmbedDialog}>\r\n                  <DialogTrigger asChild>\r\n                    <Button variant=\"outline\" size=\"sm\">\r\n                      <Code className=\"h-4 w-4 mr-2\" />\r\n                      Embed\r\n                    </Button>\r\n                  </DialogTrigger>\r\n                  <DialogContent>\r\n                    <DialogHeader>\r\n                      <DialogTitle>Embed Stream</DialogTitle>\r\n                      <DialogDescription>Copy this code to embed the stream in your website</DialogDescription>\r\n                    </DialogHeader>\r\n                    <div className=\"space-y-4\">\r\n                      <div>\r\n                        <label className=\"text-sm font-medium\">Embed Code:</label>\r\n                        <textarea\r\n                          className=\"w-full mt-1 p-2 border rounded text-sm font-mono\"\r\n                          rows={6}\r\n                          readOnly\r\n                          value={generateEmbedCode()}\r\n                        />\r\n                      </div>\r\n                      <Button onClick={copyEmbedCode} className=\"w-full\">Copy Embed Code</Button>\r\n                    </div>\r\n                  </DialogContent>\r\n                </Dialog>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </CardHeader>\r\n      )}\r\n\r\n      <CardContent>\r\n        {error && (\r\n          <div className=\"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg\">\r\n            <div className=\"flex items-center\">\r\n              <AlertCircle className=\"h-5 w-5 text-red-500 mr-2\" />\r\n              <p className=\"text-sm text-red-700\">{error}</p>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        <div\r\n          ref={containerRef}\r\n          className=\"relative bg-black rounded-lg overflow-hidden\"\r\n          style={{\r\n            width: '100%',\r\n            aspectRatio: `${width}/${height}`,\r\n            minHeight: '400px'\r\n          }}\r\n        >\r\n          {/* Loading overlay - visible only during active loading/connecting */}\r\n          {(isLoading || streamStatus === 'connecting') && (\r\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-900/95\" style={{ zIndex: 30 }}>\r\n              <div className=\"text-center text-white\">\r\n                <Loader2 className=\"h-12 w-12 animate-spin mx-auto mb-4\" />\r\n                <p className=\"text-lg font-medium mb-2\">{loadingMessage}</p>\r\n                <p className=\"text-sm opacity-75\">Status: {getStatusText()}</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n          {!isLoading && !isPlaying && !error && streamStatus === 'disconnected' && (\r\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-900\" style={{ zIndex: 20 }}>\r\n              <div className=\"text-center text-white space-y-4\">\r\n                <Play className=\"h-16 w-16 mx-auto opacity-50\" />\r\n                <div>\r\n                  <p className=\"text-lg mb-2\">Ready to Connect</p>\r\n                  <p className=\"text-sm opacity-75 mb-4\">{messages.disconnected}</p>\r\n                  {!config?.autoConnect && (\r\n                    <Button onClick={handlePlay} size=\"lg\">\r\n                      <Play className=\"h-5 w-5 mr-2\" />\r\n                      {messages.connectButton}\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n          {streamStatus === 'error' && (\r\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-900\" style={{ zIndex: 20 }}>\r\n              <div className=\"text-center text-white\">\r\n                <AlertCircle className=\"h-16 w-16 mx-auto mb-4 text-red-500\" />\r\n                <p className=\"text-lg mb-2\">Connection Error</p>\r\n                <p className=\"text-sm opacity-75\">{error || messages.error}</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* SDK DOM mounts ONLY here - hidden during loading/connecting */}\r\n          <div\r\n            ref={sdkMountRef}\r\n            className=\"absolute inset-0 w-full h-full\"\r\n            style={{\r\n              visibility: (isLoading || streamStatus === 'connecting') ? 'hidden' : 'visible',\r\n              zIndex: 1,\r\n              pointerEvents: (isLoading || streamStatus === 'connecting') ? 'none' : 'auto'\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {/* Controls */}\r\n        {showControls && (\r\n          <div className=\"mt-4 flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              {streamStatus === 'disconnected' || streamStatus === 'error' ? (\r\n                <Button onClick={handlePlay} disabled={isLoading}>\r\n                  <Play className=\"h-4 w-4\" />\r\n                  Connect\r\n                </Button>\r\n              ) : streamStatus === 'connecting' ? (\r\n                <Button disabled>\r\n                  <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                  {messages.connecting}\r\n                </Button>\r\n              ) : (\r\n                <Button variant=\"outline\" onClick={handlePause}>\r\n                  <Square className=\"h-4 w-4\" />\r\n                  Disconnect\r\n                </Button>\r\n              )}\r\n              <Button variant=\"outline\" onClick={handleMute}>\r\n                {isMuted ? (\r\n                  <VolumeX className=\"h-4 w-4\" />\r\n                ) : (\r\n                  <Volume2 className=\"h-4 w-4\" />\r\n                )}\r\n              </Button>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <Button variant=\"outline\" size=\"sm\" onClick={handleFullscreen}>\r\n                <Maximize className=\"h-4 w-4\" />\r\n              </Button>\r\n              <Button variant=\"outline\" size=\"sm\" onClick={openInNewTab} title=\"Open in new tab\">\r\n                <ExternalLink className=\"h-4 w-4\" />\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"mt-4 text-xs text-gray-500\">\r\n          <p>Stream ID: {streamProjectId}</p>\r\n          {buildId && <p>Build ID: {buildId}</p>}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n\r\n// Extend window interface for Stream Service SDK (optional if you want TS autocomplete)\r\ndeclare global {\r\n  interface Window {\r\n    StreamPixelApplication: any\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAyDO,SAAS,aAAa,EAC3B,SAAS,EACT,OAAO,EACP,YAAY,EAAE,EACd,QAAQ,UAAU,EAClB,eAAe,IAAI,EACnB,aAAa,IAAI,EACjB,kBAAkB,IAAI,EACtB,QAAQ,IAAI,EACZ,SAAS,GAAG,EACZ,aAAa,KAAK,EAClB,oBAAoB,KAAK,EACzB,aAAa,oBAAoB,EACf;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,wBAAwB;IACvF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyD;IACxG,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB,MAAM,0BAA0B;;IAC5E,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB,MAAQ,4BAA4B;;IAE/E,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IACjC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAW;IACzC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAW;IAE1C,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,aAAa;gBACb,SAAS;gBACT,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;gBACzD,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;gBAClC,MAAM,eAAe,MAAM,SAAS,IAAI;gBACxC,MAAM,OAAO,aAAa,OAAO,IAAI;gBACrC,eAAe;gBACf,mBAAmB,KAAK,iBAAiB;gBACzC,MAAM,cAAc,cAAc,KAAK,MAAM,IAAI,CAAC;gBAClD,UAAU;gBACV,IAAI,CAAC,KAAK,iBAAiB,EAAE,MAAM,IAAI,MAAM;YAC/C,EAAE,OAAO,KAAK;gBACZ,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QAEA,MAAM,8BAA8B;YAClC,IAAI;gBACF,aAAa;gBACb,SAAS;gBACT,IAAI,CAAC,sBAAsB,MAAM,IAAI,MAAM;gBAC3C,mBAAmB,qBAAqB,iBAAiB;gBACzD,MAAM,cAAc,cAAc,qBAAqB,MAAM,IAAI,CAAC;gBAClE,UAAU;gBACV,IAAI,CAAC,qBAAqB,iBAAiB,EAAE,MAAM,IAAI,MAAM;YAC/D,EAAE,OAAO,KAAK;gBACZ,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QAEA,IAAI,WAAW;YACb,IAAI,sBAAsB;gBACxB,0CAA0C;gBAC1C;YACF,OAAO;gBACL,mDAAmD;gBACnD;YACF;QACF;IACF,GAAG;QAAC;QAAW;QAAY;KAAqB;IAEhD,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YAAQ;QAAU;IAC3B,GAAG,EAAE;IAEL,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,eAAe,mBAAmB,CAAC,aAAa,CAAC,kBAAkB,OAAO,EAAE;YACtF,QAAQ,GAAG,CAAC,gCAAgC;gBAC1C,aAAa,QAAQ;gBACrB;gBACA;gBACA,gBAAgB,kBAAkB,OAAO;YAC3C;YACA;QACF;IACF,GAAG;QAAC,QAAQ;QAAa;QAAiB;KAAU;IAEpD,mBAAmB;IACnB,MAAM,WAAW;QACf,SAAS,QAAQ,kBAAkB;QACnC,YAAY,QAAQ,qBAAqB;QACzC,cAAc,QAAQ,uBAAuB;QAC7C,cAAc,QAAQ,uBAAuB;QAC7C,OAAO,QAAQ,gBAAgB;QAC/B,eAAe,QAAQ,qBAAqB;IAC9C;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,OAAO;QACxC,OAAO;YACL,aAAa;YACb,OAAO;YACP,YAAY,QAAQ,cAAc;YAClC,eAAe,QAAQ,iBAAiB;YACxC,gBAAgB,AAAC,QAAQ,kBAAkG;YAC3H,kBAAkB,QAAQ,oBAAoB;YAC9C,cAAc,AAAC,QAAQ,gBAAmD;YAC1E,eAAe,AAAC,QAAQ,iBAAoD;QAC9E;IACF;IAED,MAAM,mBAAmB;QACxB,IAAI,kBAAkB,OAAO,EAAE,QAAO,+BAA+B;QACrE,kBAAkB,OAAO,GAAG;QAC5B,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,eAAe;YACrB,IAAI,CAAC,cAAc,MAAM,IAAI,MAAM;YACnC,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,MAAM,CAAA,GAAA,4JAAA,CAAA,yBAAsB,AAAD,EAAE;YAC9E,UAAU,OAAO,GAAG;YACpB,UAAU,OAAO,GAAG;YACpB,aAAa,OAAO,GAAG;YAEvB,iBAAiB;YACjB,IAAI,YAAY,OAAO,IAAI,UAAU,WAAW,EAAE;gBAChD,IAAI,UAAU,WAAW,CAAC,UAAU,EAAE;oBACpC,UAAU,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,WAAW;gBACpE;gBACA,YAAY,OAAO,CAAC,SAAS,GAAG;gBAChC,YAAY,OAAO,CAAC,WAAW,CAAC,UAAU,WAAW;YACvD;YAEA;YACA,iBAAiB,OAAO,GAAG;QAC3B,mEAAmE;QACrE,EAAE,OAAO,KAAc;YACrB,SAAS,eAAe,QAAO,IAAI,OAAO,GAAG;YAC7C,aAAa;QACf,SAAU;YACR,kBAAkB,OAAO,GAAG;QAC9B;IACF;IACE,MAAM,UAAU;QACf,IAAI;YAIH,IAAI,UAAU,OAAO,EAAE,QAAQ,YAAY,UAAU,OAAO,CAAC,MAAM,CAAC,UAAU;YAC9E,IAAI,UAAU,OAAO,EAAE,YAAY,UAAU,OAAO,CAAC,UAAU;YAG/D,IAAI,YAAY,OAAO,EAAE,YAAY,OAAO,CAAC,SAAS,GAAG;QAC3D,EAAE,OAAO,KAAK,CAAC;QACf,eAAe;QACf,UAAU,OAAO,GAAG;QACpB,UAAU,OAAO,GAAG;QACpB,aAAa,OAAO,GAAG;QACvB,iBAAiB,OAAO,GAAG;QAC3B,kBAAkB,OAAO,GAAG;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,sBAAsB,CAAC,MAAc,OAAY,CAAC,CAAC;QACvD;;IAaF;IAEA,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAyD;;;QAEzD,MAAM;IA6CR,GAAG;QAAC;QAAmB;QAAW;QAAW;KAAQ;IAErD,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB;YACrB,oBAAoB,iBAAiB;gBACnC;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;IACF,GAAG;QAAC;QAAmB;QAAW;QAAW;QAAc;QAAO;QAAS;KAAa;IAExF,uEAAuE;IACvE,MAAM,sBAAsB;QAC1B,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,UAAU,OAAO,CAAC,eAAe,GAAG;YAClC,QAAQ,GAAG,CAAC;YACZ,gBAAgB;YAChB,aAAa;YACb,kBAAkB,QAAQ,qBAAqB;YAC/C,oBAAoB;QACtB;QAEA,UAAU,OAAO,CAAC,kBAAkB,GAAG;YACrC,QAAQ,GAAG,CAAC;YACZ,gBAAgB;YAChB,kBAAkB,QAAQ,qBAAqB;YAC/C,oBAAoB;QACtB;QAEA,UAAU,OAAO,CAAC,iBAAiB,GAAG;YACpC,QAAQ,GAAG,CAAC;YACZ,gBAAgB;YAChB,kBAAkB;YAClB,sDAAsD;YACtD,oBAAoB;QACtB;QAEA,UAAU,OAAO,CAAC,kBAAkB,GAAG;YACrC,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;YACZ,aAAa,QAAO,4CAA4C;YAChE,gBAAgB;YAChB,aAAa;YACb,oBAAoB;QACtB;QAEA,UAAU,OAAO,CAAC,YAAY,GAAG;YAC/B,QAAQ,GAAG,CAAC;YACZ,aAAa;YACb,aAAa;YACb,gBAAgB;YAChB,kBAAkB,QAAQ,uBAAuB;YACjD,oBAAoB;YAEpB,mDAAmD;YACnD,sBAAsB;gBACpB;YACF;QACF;QAEA,4DAA4D;QAC5D,IAAI,UAAU,OAAO,CAAC,iBAAiB,EAAE;YACvC,UAAU,OAAO,CAAC,iBAAiB,GAAG;gBACpC,QAAQ,GAAG,CAAC;gBACZ,oBAAoB;YACtB;QACF;QAEA,IAAI,UAAU,OAAO,CAAC,oBAAoB,EAAE;YAC1C,UAAU,OAAO,CAAC,oBAAoB,GAAG,CAAC;gBACxC,QAAQ,GAAG,CAAC,mCAAmC;gBAC/C,oBAAoB,wBAAwB;oBAAE;gBAAQ;YACxD;QACF;QAEA,gCAAgC;QAChC,IAAI,UAAU,OAAO,CAAC,eAAe,EAAE;YACrC,UAAU,OAAO,CAAC,eAAe,GAAG,CAAC;gBACnC,QAAQ,GAAG,CAAC,6BAA6B;gBACzC,oBAAoB,kBAAkB;oBAAE;gBAAQ;YAClD;QACF;IACF;IAEA,mBAAmB;IACnB,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC;QACZ,oBAAoB;QAEpB,QAAQ,GAAG,CAAC;QACZ,aAAa;QACb,kBAAkB,QAAQ,kBAAkB;QAE5C;QACA,MAAM;QACN,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,cAAc;QAClB,QAAQ,GAAG,CAAC;QACZ,oBAAoB;QAEpB,UAAU,OAAO,EAAE;QACnB,UAAU,OAAO,EAAE,QAAQ;QAE3B,yDAAyD;QACzD,sBAAsB;YACpB,aAAa;YACb,gBAAgB;QAClB;QAEA;QACA,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC;QACZ,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,WAAW;YAChC,WAAW,CAAC;YACZ,oBAAoB,gBAAgB;gBAAE,SAAS,CAAC;YAAQ;QAC1D;IACF;IACA,MAAM,mBAAmB;QACvB,IAAI,CAAC,aAAa,OAAO,EAAE;QAC3B,IAAI,CAAC,cAAc;YACjB,aAAa,OAAO,CAAC,iBAAiB;QACxC,OAAO;YACL,SAAS,cAAc;QACzB;QACA,gBAAgB,CAAC;IACnB;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB;QAC3B,IAAI,oBAAoB,QAAQ,UAAU;YACxC,mBAAmB;YACnB,wBAAwB;QAC1B,OAAO;YACL,SAAS;QACX;IACF;IAEA,uBAAuB;IACvB,MAAM,oBAAoB;QACxB,MAAM,SAAS,sCAAgC,0BAAyB;QACxE,MAAM,SAAS,IAAI;QAEnB,oCAAoC;QACpC,IAAI,CAAC,cAAc,OAAO,GAAG,CAAC,gBAAgB;QAC9C,IAAI,CAAC,YAAY,OAAO,GAAG,CAAC,cAAc;QAC1C,IAAI,CAAC,iBAAiB,OAAO,GAAG,CAAC,mBAAmB;QACpD,IAAI,QAAQ,aAAa,OAAO,GAAG,CAAC,eAAe;QAEnD,MAAM,cAAc,OAAO,QAAQ;QACnC,MAAM,WAAW,GAAG,OAAO,OAAO,EAAE,YAAY,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEtF,OAAO,CAAC;;OAEL,EAAE,SAAS;SACT,EAAE,MAAM;UACP,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAwCV,CAAC;IACR;IACA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,MAAM;QACR,EAAE,OAAM,CAAE;IACZ;IACA,MAAM,eAAe;QACnB;;IAIF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IACA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,yBAAyB;IACzB,IAAI,QAAQ,uBAAuB,CAAC,mBAAmB,sBAAsB;QAC3E,qBACE,8OAAC,yHAAA,CAAA,OAAI;YAAC,WAAW;sBACf,cAAA,8OAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAG,WAAU;sCAAsB;;;;;;sCACpC,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAC7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;8CAEzC,8OAAC,2HAAA,CAAA,SAAM;oCAAC,SAAS;oCAAsB,WAAU;;sDAC/C,8OAAC,4MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;wBAItC,uBAAS,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAKzD;IAEA,4BAA4B;IAC5B,IAAI,cAAc,mBAAmB;QACnC,qBACE,8OAAC;YAAI,WAAW,CAAC,uBAAuB,EAAE,WAAW;sBAEnD,cAAA,8OAAC;gBACC,KAAK;gBACL,WAAU;;kCAGV,8OAAC;wBACC,KAAK;wBACL,WAAU;wBACV,OAAO;4BACL,YAAY,AAAC,aAAa,iBAAiB,eAAgB,WAAW;4BACtE,QAAQ;wBACV;;;;;;oBAGD,CAAC,aAAa,iBAAiB,YAAY,mBAC1C,8OAAC;wBAAI,WAAU;wBAAmE,OAAO;4BAAE,QAAQ;wBAAG;kCACpG,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;oCAAE,WAAU;8CAA4B;;;;;;8CACzC,8OAAC;oCAAE,WAAU;;wCAAqB;wCAAS;;;;;;;;;;;;;;;;;;oBAMhD,uBACC,8OAAC;wBAAI,WAAU;wBAA+D,OAAO;4BAAE,QAAQ;wBAAG;kCAChG,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;8CAAG;;;;;;;;;;;;;;;;;oBAMT,8BACC;;0CAEE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,QAAK;wCAAC,WAAW;kDAAmB;;;;;;oCACpC,yBACC,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAA0D;4CACnF,QAAQ,KAAK,CAAC,GAAG;4CAAG;;;;;;;;;;;;;0CAMlC,8OAAC;gCAAI,WAAU;;oCACZ,CAAC,0BACA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;6DAInC,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAKvC,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAET,wBAAU,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAAe,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAGlE,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQpC;IAEA,qBAAqB;IACrB,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAW;;YACd,4BACC,8OAAC,yHAAA,CAAA,aAAU;0BACT,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC,yHAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,yHAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAEnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,QAAK;oCAAC,WAAW;8CAAmB;;;;;;gCACpC,yBACC,8OAAC,0HAAA,CAAA,QAAK;oCAAC,SAAQ;;wCAAU;wCACf,QAAQ,KAAK,CAAC,GAAG;wCAAG;;;;;;;gCAG/B,iCACC,8OAAC,2HAAA,CAAA,SAAM;oCAAC,MAAM;oCAAiB,cAAc;;sDAC3C,8OAAC,2HAAA,CAAA,gBAAa;4CAAC,OAAO;sDACpB,cAAA,8OAAC,2HAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;;kEAC7B,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAIrC,8OAAC,2HAAA,CAAA,gBAAa;;8DACZ,8OAAC,2HAAA,CAAA,eAAY;;sEACX,8OAAC,2HAAA,CAAA,cAAW;sEAAC;;;;;;sEACb,8OAAC,2HAAA,CAAA,oBAAiB;sEAAC;;;;;;;;;;;;8DAErB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAsB;;;;;;8EACvC,8OAAC;oEACC,WAAU;oEACV,MAAM;oEACN,QAAQ;oEACR,OAAO;;;;;;;;;;;;sEAGX,8OAAC,2HAAA,CAAA,SAAM;4DAAC,SAAS;4DAAe,WAAU;sEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUnE,8OAAC,yHAAA,CAAA,cAAW;;oBACT,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAK3C,8OAAC;wBACC,KAAK;wBACL,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,aAAa,GAAG,MAAM,CAAC,EAAE,QAAQ;4BACjC,WAAW;wBACb;;4BAGC,CAAC,aAAa,iBAAiB,YAAY,mBAC1C,8OAAC;gCAAI,WAAU;gCAAmE,OAAO;oCAAE,QAAQ;gCAAG;0CACpG,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAE,WAAU;sDAA4B;;;;;;sDACzC,8OAAC;4CAAE,WAAU;;gDAAqB;gDAAS;;;;;;;;;;;;;;;;;;4BAIhD,CAAC,aAAa,CAAC,aAAa,CAAC,SAAS,iBAAiB,gCACtD,8OAAC;gCAAI,WAAU;gCAAgE,OAAO;oCAAE,QAAQ;gCAAG;0CACjG,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAe;;;;;;8DAC5B,8OAAC;oDAAE,WAAU;8DAA2B,SAAS,YAAY;;;;;;gDAC5D,CAAC,QAAQ,6BACR,8OAAC,2HAAA,CAAA,SAAM;oDAAC,SAAS;oDAAY,MAAK;;sEAChC,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;4BAOlC,iBAAiB,yBAChB,8OAAC;gCAAI,WAAU;gCAAgE,OAAO;oCAAE,QAAQ;gCAAG;0CACjG,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAE,WAAU;sDAAe;;;;;;sDAC5B,8OAAC;4CAAE,WAAU;sDAAsB,SAAS,SAAS,KAAK;;;;;;;;;;;;;;;;;0CAMhE,8OAAC;gCACC,KAAK;gCACL,WAAU;gCACV,OAAO;oCACL,YAAY,AAAC,aAAa,iBAAiB,eAAgB,WAAW;oCACtE,QAAQ;oCACR,eAAe,AAAC,aAAa,iBAAiB,eAAgB,SAAS;gCACzE;;;;;;;;;;;;oBAKH,8BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,iBAAiB,kBAAkB,iBAAiB,wBACnD,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS;wCAAY,UAAU;;0DACrC,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;+CAG5B,iBAAiB,6BACnB,8OAAC,2HAAA,CAAA,SAAM;wCAAC,QAAQ;;0DACd,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAClB,SAAS,UAAU;;;;;;6DAGtB,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;;0DACjC,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAIlC,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;kDAChC,wBACC,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;kDAC3C,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;wCAAc,OAAM;kDAC/D,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMhC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAE;oCAAY;;;;;;;4BACd,yBAAW,8OAAC;;oCAAE;oCAAW;;;;;;;;;;;;;;;;;;;;;;;;;AAKpC", "debugId": null}}]}