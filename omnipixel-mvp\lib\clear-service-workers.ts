'use client'

// Utility to clear all service workers that might interfere with Uppy
export async function clearAllServiceWorkers(): Promise<void> {
  if (typeof navigator === 'undefined' || !('serviceWorker' in navigator)) {
    console.log('Service workers not supported')
    return
  }

  try {
    const registrations = await navigator.serviceWorker.getRegistrations()
    console.log(`Found ${registrations.length} service worker registrations`)
    
    for (const registration of registrations) {
      console.log('Unregistering service worker:', registration.scope)
      await registration.unregister()
    }
    
    console.log('All service workers unregistered')
  } catch (error) {
    console.error('Failed to unregister service workers:', error)
  }
}

// Call this function to clear service workers
if (typeof window !== 'undefined') {
  // Auto-clear on load to prevent interference with Uppy
  clearAllServiceWorkers()
}
