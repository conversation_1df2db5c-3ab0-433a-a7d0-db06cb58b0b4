'use client'

import { useState } from 'react'
import { Navigation } from '@/components/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Users, Search, User as UserIcon, Shield, Calendar, Package, Loader2, ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { User } from '@/types/supabase'
import { Profile } from '@/lib/supabase'

interface AdminUser {
  id: string
  email: string
  role: 'user' | 'platform_admin'
  created_at: string
  updated_at: string
  projects: Array<{
    id: string
    name: string
    created_at: string
  }>
}
interface Pagination {
  page: number
  limit: number
  total: number
  totalPages: number
}

export default function AdminUsersClient({
  user,
  profile,
  initialUsers,
  initialPagination
}: {
  user: User
  profile: Profile
  initialUsers: AdminUser[]
  initialPagination: Pagination
}) {
  const router = useRouter()
  const [users, setUsers] = useState<AdminUser[]>(initialUsers)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [pagination, setPagination] = useState<Pagination>(initialPagination)
  const [updatingRole, setUpdatingRole] = useState<string | null>(null)

  // Fetch users for current page/search
  const fetchUsers = async (opts: { page?: number; search?: string } = {}) => {
    try {
      setLoading(true)
      setError(null)
      const page = opts.page ?? pagination.page
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        ...(opts.search !== undefined ? { search: opts.search } : (searchTerm ? { search: searchTerm } : {})),
      })
      const response = await fetch(`/api/admin/users?${params}`)
      if (!response.ok) throw new Error('Failed to fetch users')
      const data = await response.json()
      setUsers(data.users)
      setPagination(data.pagination)
    } catch (err: unknown) {
    const errMessage = err instanceof Error ? err.message : 'An unknown error occurred'
      setError(errMessage)
      console.error(errMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleRoleUpdate = async (userId: string, newRole: 'user' | 'platform_admin') => {
    try {
      setUpdatingRole(userId)
      const response = await fetch('/api/admin/users', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ user_id: userId, role: newRole }),
      })
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update user role')
      }
      await fetchUsers()
      alert('User role updated successfully!')
    } catch (err: unknown) {
        const errMessage = err instanceof Error ? err.message : 'An unknown error occurred'
        alert('Failed to update user role: ' + errMessage)
    } finally {
      setUpdatingRole(null)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setPagination((prev) => ({ ...prev, page: 1 }))
    fetchUsers({ page: 1, search: searchTerm })
  }

  const formatDate = (dateString: string) =>
    new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit'
    })

  const getRoleBadgeColor = (role: string) =>
    role === 'platform_admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'

  if (!user || profile?.role !== 'platform_admin') {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You need platform admin privileges to access this page.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => router.push('/admin')}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Admin Panel
          </Button>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <Users className="h-8 w-8 mr-3" />
                User Management
              </h1>
              <p className="text-gray-600 mt-1">
                Manage user accounts and permissions
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant="secondary">
                {pagination.total} total users
              </Badge>
            </div>
          </div>
        </div>
        {/* Search */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <form onSubmit={handleSearch} className="flex space-x-4">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Search users by email or name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <Button type="submit">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Error State */}
        {error && (
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="text-center text-red-600">
                <p>Error: {error}</p>
                <Button onClick={() => fetchUsers()} className="mt-2">
                  Try Again
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
        {/* Loading State */}
        {loading && (
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin mr-2" />
                <span>Loading users...</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Users List */}
        {!loading && !error && (
          <div className="space-y-4">
            {users.map((user) => (
              <Card key={user.id}>
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="flex items-center">
                          {user.role === 'platform_admin' ? (
                            <Shield className="h-5 w-5 text-purple-500 mr-2" />
                          ) : (
                            <UserIcon className="h-5 w-5 text-blue-500 mr-2" />
                          )}
                          <h3 className="text-lg font-medium text-gray-900">
                            {user.email}
                          </h3>
                        </div>
                        <Badge className={getRoleBadgeColor(user.role)}>
                          {user.role === 'platform_admin' ? 'Admin' : 'User'}
                        </Badge>
                      </div>
                      <div className="space-y-1 text-sm text-gray-600">
                        <p className="flex items-center">
                          <span className="font-medium mr-2">Email:</span>
                          {user.email}
                        </p>
                        <p className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2" />
                          Joined {formatDate(user.created_at)}
                        </p>
                        <p className="flex items-center">
                          <Package className="h-4 w-4 mr-2" />
                          {user.projects.length} project{user.projects.length !== 1 ? 's' : ''}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {/* Role Update */}
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            Change Role
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Change User Role</DialogTitle>
                            <DialogDescription>
                              Update the role for {user.email}
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <label className="text-sm font-medium text-gray-700">Current Role</label>
                              <p className="text-sm text-gray-900 capitalize">{user.role.replace('_', ' ')}</p>
                            </div>
                            <div className="flex space-x-2">
                              <Button
                                onClick={() => handleRoleUpdate(user.id, 'user')}
                                disabled={updatingRole === user.id || user.role === 'user'}
                                variant={user.role === 'user' ? 'default' : 'outline'}
                              >
                                {updatingRole === user.id ? (
                                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                ) : (
                                  <UserIcon className="h-4 w-4 mr-2" />
                                )}
                                User
                              </Button>
                              <Button
                                onClick={() => handleRoleUpdate(user.id, 'platform_admin')}
                                disabled={updatingRole === user.id || user.role === 'platform_admin' || user.id === profile?.id}
                                variant={user.role === 'platform_admin' ? 'default' : 'outline'}
                              >
                                {updatingRole === user.id ? (
                                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                ) : (
                                  <Shield className="h-4 w-4 mr-2" />
                                )}
                                Admin
                              </Button>
                            </div>
                            {user.id === profile?.id && (
                              <p className="text-xs text-gray-500">
                                You cannot change your own role
                              </p>
                            )}
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-600">
                      Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} users
                    </p>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          const next = Math.max(1, pagination.page - 1)
                          setPagination((prev) => ({ ...prev, page: next }))
                          fetchUsers({ page: next })
                        }}
                        disabled={pagination.page <= 1}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          const next = Math.min(pagination.page + 1, pagination.totalPages)
                          setPagination((prev) => ({ ...prev, page: next }))
                          fetchUsers({ page: next })
                        }}
                        disabled={pagination.page >= pagination.totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
