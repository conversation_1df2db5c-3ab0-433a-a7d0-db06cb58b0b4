# Frontend Architecture Documentation v1.0

## Overview
The Omnipixel MVP frontend is built with Next.js 15, TypeScript, and Tailwind CSS, following modern React patterns and best practices.

## Technology Stack

### Core Framework
- **Next.js 15**: React framework with App Router
- **TypeScript**: Type-safe JavaScript
- **React 19**: Latest React features and hooks

### Styling & UI
- **Tailwind CSS 3.x**: Utility-first CSS framework
- **shadcn/ui**: High-quality React components
- **Lucide React**: Icon library

### State Management
- **React Context**: Authentication state
- **Custom Hooks**: Data fetching and state management
- **React Hook Form**: Form state management

## Project Structure

```
app/
├── dashboard/              # Dashboard pages
│   └── page.tsx           # Main dashboard
├── login/                 # Authentication
│   └── page.tsx           # Login/signup page
├── projects/              # Project management
│   └── [projectId]/       # Dynamic project routes
│       ├── page.tsx       # Project details
│       ├── upload/        # Upload interface
│       └── stream/        # Streaming interface
├── layout.tsx             # Root layout
├── page.tsx               # Home page (redirects)
└── globals.css            # Global styles

components/
├── ui/                    # shadcn/ui components
│   ├── button.tsx
│   ├── card.tsx
│   ├── input.tsx
│   └── ...
├── navigation.tsx         # Main navigation
├── project-card.tsx       # Project display card
├── build-list.tsx         # Build history list
├── upload-progress.tsx    # Upload progress indicator
└── player.tsx             # Streaming player

hooks/
└── use-admin.ts          # Admin operations

lib/
├── supabase.ts           # Supabase client config
├── admin.ts              # Admin Supabase client
└── aws.ts                # AWS S3 configuration
```

## Component Architecture

### Layout Components

#### Root Layout (`app/layout.tsx`)
- Provides global HTML structure
- Sets up global fonts and metadata

#### Navigation (`components/navigation.tsx`)
- Responsive navigation header
- User menu with role-based options
- Server-side user data integration

### Page Components

#### Dashboard (`app/dashboard/page.tsx`)
- Project listing with search functionality
- Create new project dialog
- Role-based UI elements

#### Project Pages
- **Project Details**: Full project management interface
- **Upload Page**: File upload with progress tracking
- **Stream Page**: Project streaming interface

### UI Components

#### ProjectCard (`components/project-card.tsx`)
```typescript
interface ProjectCardProps {
  project: Project & {
    builds?: Array<{
      id: string
      filename: string
      version: number
      created_at: string
    }>
  }
}
```

Features:
- Project information display
- Latest build information
- Quick action buttons
- Build count and status

#### BuildList (`components/build-list.tsx`)
```typescript
interface BuildListProps {
  builds: Build[]
  projectId: string
  onDelete?: (buildId: string) => void
  showActions?: boolean
}
```

Features:
- Tabular build display
- Version badges
- Action buttons (stream, download, delete)
- Empty state handling

#### UploadProgress (`components/upload-progress.tsx`)
```typescript
interface UploadState {
  status: 'idle' | 'uploading' | 'processing' | 'success' | 'error'
  progress: number
  fileName?: string
  error?: string
  currentPart?: number
  totalParts?: number
}
```

Features:
- Real-time progress tracking
- Multi-part upload support
- Error handling and retry
- Status indicators

#### Player (`components/player.tsx`)
```typescript
interface PlayerProps {
  streamProjectId: string
  projectName: string
}
```

Features:
- Video player interface
- Playback controls
- Fullscreen support
- Stream status indicators

## Custom Hooks

### useAdmin (`hooks/use-admin.ts`)
Manages admin operations:

```typescript
export function useAdmin() {
  return {
    loading: boolean
    error: string | null
    createProject: (data) => Promise<AdminProject>
    fetchAllProjects: () => Promise<AdminProject[]>
  }
}
```

## State Management Patterns

### Authentication State
- Server-side authentication with Supabase SSR
- Middleware-based route protection
- Props-based user data passing

### Data Fetching
- Server-side data fetching with Next.js
- Loading and error states in components
- Real-time updates via polling

### Form State
- React Hook Form for complex forms
- Zod for validation schemas
- Error handling and display

## Styling Architecture

### Tailwind CSS Configuration
```javascript
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './hooks/**/*.{js,ts,jsx,tsx,mdx}',
    './lib/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        // ... additional color variables
      }
    }
  }
}
```

### CSS Variables
- Design system tokens
- Dark mode support
- Consistent spacing and colors

### Component Styling
- Utility-first approach
- Responsive design patterns
- Consistent component variants

## Performance Optimizations

### Next.js Features
- App Router for improved performance
- Server Components where appropriate
- Automatic code splitting

### React Optimizations
- Proper dependency arrays in hooks
- Memoization where beneficial
- Efficient re-rendering patterns

### Loading States
- Skeleton screens
- Progressive loading
- Error boundaries

## Error Handling

### Error Boundaries
- Component-level error catching
- Graceful fallback UI
- Error reporting

### API Error Handling
- Consistent error message display
- Retry mechanisms
- User-friendly error messages

### Form Validation
- Client-side validation
- Real-time feedback
- Accessibility considerations

## Accessibility

### ARIA Support
- Proper semantic HTML
- ARIA labels and descriptions
- Keyboard navigation

### Screen Reader Support
- Descriptive text for actions
- Status announcements
- Focus management

### Color and Contrast
- WCAG compliant color schemes
- High contrast mode support
- Color-blind friendly design

## Testing Strategy

### Component Testing
- Unit tests for components
- Integration tests for hooks
- Snapshot testing for UI consistency

### E2E Testing
- Critical user flows
- Authentication testing
- Cross-browser compatibility

## Future Enhancements

### Performance
- Image optimization
- Bundle size optimization
- Caching strategies

### Features
- Real-time updates
- Offline support
- Progressive Web App features

### Developer Experience
- Storybook for component development
- Better TypeScript integration
- Enhanced debugging tools
