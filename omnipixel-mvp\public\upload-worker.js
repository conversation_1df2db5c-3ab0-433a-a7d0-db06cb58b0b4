// OmniPixel Service Worker - Simplified for Uppy Integration
// Uppy handles background uploads natively, so this worker is minimal

const CACHE_NAME = 'omnipixel-v2'

// Install event
self.addEventListener('install', (event) => {
  console.log('OmniPixel Service Worker installing...')
  self.skipWaiting()
})

// Activate event
self.addEventListener('activate', (event) => {
  console.log('OmniPixel Service Worker activated')
  event.waitUntil(self.clients.claim())
})

// Message handler
self.addEventListener('message', (event) => {
  const { type, data } = event.data

  switch (type) {
    case 'PING':
      event.source?.postMessage({ type: 'PONG', data: 'Service Worker is active' })
      break
    default:
      console.log('Service Worker received message:', type, data)
  }
})

// No fetch event handler - let all requests pass through normally
// This prevents interference with Uppy's upload requests


