import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

interface StreamPixelWebhookPayload {
  id: string // StreamPixel project ID (success) | ZIP filename (failure)
  status: 'success' | 'failure'
  updated_at: string
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔔 StreamPixel webhook received')

    // Parse the webhook payload
    const payload: StreamPixelWebhookPayload = await request.json()
    console.log('Webhook payload:', payload)

    // Validate required fields
    if (!payload.id || !payload.status || !payload.updated_at) {
      console.error('Invalid webhook payload - missing required fields')
      return NextResponse.json(
        { error: 'Invalid payload: missing required fields (id, status, updated_at)' },
        { status: 400 }
      )
    }

    // Create Supabase client (service role for webhook)
    const supabase = await createClient()

    let build = null
    let projects: any = null
    let updateData: Record<string, unknown> = {
      updated_at: new Date().toISOString()
    }

    if (payload.status === 'success') {
      // Success: payload.id is the StreamPixel project ID
      console.log('✅ StreamPixel upload successful for project:', payload.id)
      payload.id = payload.id.replace('https://share.streampixel.io/', '')

      // First find the project by StreamPixel project ID (include auto_release setting)
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select('id, auto_release')
        .eq('stream_project_id', payload.id)
        .single()

      if (projectError) {
        console.error('Error finding project by StreamPixel ID:', projectError)
        return NextResponse.json(
          { error: 'Failed to find project' },
          { status: 500 }
        )
      }

      if (!projectData) {
        console.error('No project found with StreamPixel ID:', payload.id)
        return NextResponse.json(
          { error: 'Project not found' },
          { status: 404 }
        )
      }

      projects = projectData

      // Now find the latest build in that project by activation time
      // to identify which build this webhook is for
      const { data: builds, error: findError } = await supabase
        .from('builds')
        .select('*')
        .eq('project_id', projects.id)
        .eq('status', 'processing') // Only look for builds that are currently processing
        .order('activated_at', { ascending: false })
        .limit(1)

      if (findError) {
        console.error('Error finding latest build:', findError)
        return NextResponse.json(
          { error: 'Failed to find build' },
          { status: 500 }
        )
      }

      if (!builds || builds.length === 0) {
        console.error('No processing build found for project:', projects.id)
        return NextResponse.json(
          { error: 'No processing build found' },
          { status: 404 }
        )
      }

      build = builds[0]

      // Check if this is auto-release or manual activation
      const isAutoRelease = projects.auto_release
      const isManualActivation = build.status === 'processing' // Manual activation sets status to 'processing'

      console.log(`🔄 Processing StreamPixel success - Auto-release: ${isAutoRelease}, Manual activation: ${isManualActivation}`)

      if (isAutoRelease || isManualActivation) {
        // Auto-release enabled OR manual activation: make this build current and deactivate others
        console.log('✅ Activating build and deactivating previous builds')

        // First, deactivate all current builds in this project
        const { error: deactivateError } = await supabase
          .from('builds')
          .update({
            is_current: false,
            status: 'archived',
            updated_at: new Date().toISOString()
          })
          .eq('project_id', projects.id)
          .eq('is_current', true)
          .neq('id', build.id)

        if (deactivateError) {
          console.error('Error deactivating previous builds:', deactivateError)
          return NextResponse.json(
            { error: 'Failed to deactivate previous builds' },
            { status: 500 }
          )
        }

        // Update this build to active and current
        updateData = {
          ...updateData,
          status: 'active',
          streampixel_status: 'live',
          is_current: true,
          error_message: null
        }
      } else {
        // Auto-release disabled and not manual activation: just mark as uploaded but not current
        console.log('📦 Build uploaded successfully but not activated (auto-release disabled)')
        updateData = {
          ...updateData,
          status: 'inactive', // Build is ready but not active
          streampixel_status: 'live',
          is_current: false, // Don't make it current
          error_message: null
        }
      }

    } else {
      // Failure: payload.id is the ZIP filename (timestamp_build-id.zip)
      console.log('❌ StreamPixel upload failed for file:', payload.id)

      // Extract build ID from filename
      // New format: [build-id].zip (filename IS the build ID)
      // Legacy format: [timestamp]_[build-id].zip (for backward compatibility)
      const filenameWithoutExt = payload.id.replace('.zip', '')
      const buildId = filenameWithoutExt.includes('_') ? filenameWithoutExt.split('_')[1] : filenameWithoutExt
      
      // Find the build by ID
      const { data: builds, error: findError } = await supabase
        .from('builds')
        .select('*')
        .eq('id', buildId)
        .single()

      if (findError) {
        console.error('Error finding build by ID:', findError)
        return NextResponse.json(
          { error: 'Failed to find build' },
          { status: 500 }
        )
      }

      if (!builds) {
        console.error('No build found with ID:', buildId)
        return NextResponse.json(
          { error: 'Build not found' },
          { status: 404 }
        )
      }

      build = builds
      
      // Update build status to failed
      updateData = {
        ...updateData,
        status: 'failed',
        streampixel_status: 'failed',
        error_message: 'Stream Service processing failed',
        is_current: false // Failed builds cannot be current
      }
    }

    // Update the build in the database
    const { data: updatedBuild, error: updateError } = await supabase
      .from('builds')
      .update(updateData)
      .eq('id', build.id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating build:', updateError)
      return NextResponse.json(
        { error: 'Failed to update build' },
        { status: 500 }
      )
    }

    console.log('✅ Build updated successfully:', updatedBuild)

    // Log the final result for debugging
    if (payload.status === 'success') {
      if (updatedBuild.is_current) {
        console.log(`🎉 Build ${updatedBuild.id} is now the active build for project ${projects.id}`)
      } else {
        console.log(`📦 Build ${updatedBuild.id} uploaded successfully but not activated (auto-release: ${projects.auto_release})`)
      }
    }

    return NextResponse.json({
      message: 'Webhook processed successfully',
      build: updatedBuild,
      status: payload.status
    })

  } catch (error) {
    console.error('❌ Error processing StreamPixel webhook:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
