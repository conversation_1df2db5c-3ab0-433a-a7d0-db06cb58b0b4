-- Simple migration: Add Backblaze B2 key column
-- Run this in your Supabase SQL editor

-- 1. Add b2_key column to builds table
ALTER TABLE builds ADD COLUMN IF NOT EXISTS b2_key TEXT;

-- 2. Add index on b2_key for performance
CREATE INDEX IF NOT EXISTS idx_builds_b2_key ON builds(b2_key);

-- 3. Add comment to document the change
COMMENT ON COLUMN builds.b2_key IS 'Backblaze B2 file key for the build file';

-- 4. Verification query - check the column was added
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'builds' 
AND column_name IN ('s3_key', 'b2_key');
