import { notFound, redirect } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'
import ProjectDetailClient from './client'

interface Props {
  params: Promise<{ id: string }>
}

export default async function ProjectDetailPage({ params }: Props) {
  const { id } =  await params;
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login')

  // Get user profile to check if they're a platform admin
  const { data: profile } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', user.id)
    .single()

  const isPlatformAdmin = profile?.role === 'platform_admin'

  // Fetch project and builds - platform admins can access any project
  let query = supabase
    .from('projects')
    .select(`
      *,
      builds (
        id, filename, original_filename, s3_key, version, status, is_current, file_size,
        streampixel_build_id, streampixel_status, error_message, activated_at,
        created_at, updated_at
      )
    `)
    .eq('id', id)

  // If not platform admin, restrict to user's own projects
  if (!isPlatformAdmin) {
    query = query.eq('user_id', user.id)
  }

  const { data, error } = await query.single()
  console.log(data)
  if (error || !data) notFound()

  return (
    <ProjectDetailClient
      user={user}
      project={data}
      initialBuilds={data.builds || []}
    />
  )
}
