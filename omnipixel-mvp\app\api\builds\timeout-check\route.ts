import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

// POST - Check for timed-out builds and reset them to inactive
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Find builds that have been processing for more than 15 minutes since activation
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000).toISOString()

    const { data: timedOutBuilds, error: queryError } = await supabase
      .from('builds')
      .select(`
        id,
        project_id,
        status,
        activated_at,
        updated_at,
        projects!inner(user_id)
      `)
      .eq('status', 'processing')
      .not('activated_at', 'is', null)
      .lt('activated_at', fifteenMinutesAgo)
      .eq('projects.user_id', user.id)

    if (queryError) {
      console.error('Error querying timed-out builds:', queryError)
      return NextResponse.json(
        { error: 'Failed to query builds' },
        { status: 500 }
      )
    }

    if (!timedOutBuilds || timedOutBuilds.length === 0) {
      return NextResponse.json({
        message: 'No timed-out builds found',
        resetCount: 0
      })
    }

    console.log(`Found ${timedOutBuilds.length} timed-out builds, resetting to inactive...`)

    // Reset timed-out builds to inactive status
    const buildIds = timedOutBuilds.map(build => build.id)
    
    const { error: updateError } = await supabase
      .from('builds')
      .update({
        status: 'inactive',
        is_current: false,
        streampixel_status: 'timeout',
        error_message: 'Build activation timed out after 15 minutes. Please try activating again.',
        updated_at: new Date().toISOString()
      })
      .in('id', buildIds)

    if (updateError) {
      console.error('Error updating timed-out builds:', updateError)
      return NextResponse.json(
        { error: 'Failed to reset timed-out builds' },
        { status: 500 }
      )
    }

    console.log(`✅ Successfully reset ${timedOutBuilds.length} timed-out builds to inactive`)

    return NextResponse.json({
      message: `Reset ${timedOutBuilds.length} timed-out builds to inactive`,
      resetCount: timedOutBuilds.length,
      resetBuilds: timedOutBuilds.map(build => ({
        id: build.id,
        project_id: build.project_id
      }))
    })

  } catch (error: unknown) {
    console.error('Error in timeout check:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
