'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import type { User } from '@supabase/supabase-js'
import type { Project, Build } from '@/lib/supabase'
import { Navigation } from '@/components/navigation'
import { UppyFileUpload } from '@/components/uppy-file-upload'
import { EnhancedConfigEditor } from '@/components/enhanced-config-editor'
import { BuildHistory } from '@/components/build-history'
import { StreamPlayer } from '@/components/stream-player'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Download, Calendar, Package, Loader2, Edit2, Save, X } from 'lucide-react'
import { Body, Meta, UploadResult } from '@uppy/core'

type ProjectWithBuilds = Project & { builds: Build[] }

interface Props {
  user: User
  project: ProjectWithBuilds
  initialBuilds: Build[]
}

export default function ProjectDetailClient({ user, project, initialBuilds }: Props) {
  const router = useRouter()
  const [builds, setBuilds] = useState<Build[]>(initialBuilds)
  const [isEditing, setIsEditing] = useState(false)
  const [editedName, setEditedName] = useState(project.name)
  const [autoRelease, setAutoRelease] = useState(project.auto_release)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Handler examples below

  const fetchBuilds = useCallback(async (isPolling = false) => {
    try {
      if (!isPolling) {
        setLoading(true)
      }
      setError(null)
      const response = await fetch(`/api/projects/${project.id}/builds`)
      if (!response.ok) throw new Error('Could not fetch builds')
      const data = await response.json()
      setBuilds(data.builds)
    } catch (err:unknown) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
    } finally {
      if (!isPolling) {
        setLoading(false)
      }
    }
  }, [project.id])

  // Auto-refresh builds every 10 seconds when there are processing builds
  useEffect(() => {
    const hasProcessingBuilds = builds.some(build => build.status === 'processing')

    if (hasProcessingBuilds) {
      // Clear any existing interval
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
      }

      // Set up new polling interval
      pollingIntervalRef.current = setInterval(() => {
        console.log('🔄 Polling for build updates...')
        fetchBuilds(true) // Pass true to indicate this is polling
      }, 10000) // 10 seconds

      console.log('✅ Started polling for processing builds')
    } else {
      // No processing builds, clear the interval
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
        pollingIntervalRef.current = null
        console.log('⏹️ Stopped polling - no processing builds')
      }
    }

    // Cleanup on unmount
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
        pollingIntervalRef.current = null
      }
    }
  }, [builds, fetchBuilds]) // Re-run when builds change


  const handleUploadComplete = (result: UploadResult<Meta, Body>) => {
    if (result.build) {
      setBuilds((prev) => {
        const newBuild = result.build as Build;
        return [newBuild, ...prev].sort((a, b) => b.version - a.version);
      });
    } else {
      fetchBuilds()
    }
  }

  const handleUploadError = (err: string) => setError(err)

  const handleBuildRevert = async (buildId: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/projects/${project.id}/builds/revert`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ build_id: buildId }),
      })
      if (!response.ok) throw new Error('Failed to revert build')
      await fetchBuilds()
      alert('Build reverted successfully!')
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleBuildDelete = async (buildId: string) => {
    try {
      setLoading(true)
      console.log('🗑️ Deleting build:', buildId, 'from project:', project.id)

      const response = await fetch(`/api/projects/${project.id}/builds/${buildId}`, { method: 'DELETE' })
      console.log('Delete response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Delete failed:', errorData)
        throw new Error(errorData.error || 'Failed to delete build')
      }

      const result = await response.json()
      console.log('Delete result:', result)

      await fetchBuilds()
      alert('Build deleted successfully!')
    } catch (err: unknown) {
      console.error('Build deletion error:', err)
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred'
      setError(errorMessage)
      alert('Failed to delete build: ' + errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleBuildActivate = async (buildId: string) => {
    try {
      setLoading(true)
      setError(null)
      console.log('Activating build:', buildId, 'for project:', project.id)

      const response = await fetch(`/api/projects/${project.id}/builds/${buildId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      console.log('Activation response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Activation failed:', errorData)
        throw new Error(errorData.error || 'Failed to activate build')
      }

      const result = await response.json()
      console.log('Activation result:', result)

      await fetchBuilds()
      alert('Build activated successfully!')
    } catch (err: unknown) {
      console.error('Build activation error:', err)
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred'
      setError(errorMessage)
      alert('Failed to activate build: ' + errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveProjectName = async () => {
    if (!editedName.trim()) return
    try {
      setLoading(true)
      const response = await fetch(`/api/projects/${project.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: editedName.trim() }),
      })
      if (!response.ok) throw new Error('Failed to update project name')
      setIsEditing(false)
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleCancelEdit = () => {
    setEditedName(project.name)
    setIsEditing(false)
  }

  const handleAutoReleaseToggle = async (enabled: boolean) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/projects/${project.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ auto_release: enabled }),
      })
      if (!response.ok) throw new Error('Failed to update auto-release setting')
      setAutoRelease(enabled)
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // Render (shortened: fill in with your actual layout/logic)
  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation user={user} />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Button variant="ghost" onClick={() => router.push('/dashboard')} className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" /> Back to Dashboard
        </Button>

        <div className="flex items-center space-x-2 mb-2">
          {isEditing ? (
            <>
              <Input value={editedName} onChange={e => setEditedName(e.target.value)} />
              <Button size="sm" onClick={handleSaveProjectName}><Save className="h-4 w-4" /></Button>
              <Button size="sm" variant="outline" onClick={handleCancelEdit}><X className="h-4 w-4" /></Button>
            </>
          ) : (
            <>
              <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
              <Button size="sm" variant="ghost" onClick={() => setIsEditing(true)}>
                <Edit2 className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>
        <p className="text-gray-600">Stream Project ID: <span className="font-mono">{project.stream_project_id}</span></p>
        <Badge variant="secondary">{builds.length} build{builds.length !== 1 ? 's' : ''}</Badge>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
          <div className="lg:col-span-2 space-y-6">
            <StreamPlayer
              projectId={project.id}
              buildId={builds.find(b => b.is_current)?.id}
              config={project.config}
              showControls={true}
              showHeader={true}
              showEmbedButton={true}
            />
            <EnhancedConfigEditor
              projectId={project.id}
              currentConfig={project.config || {}}
              onConfigUpdate={(newConfig) => { /* update logic here */ }}
              isAdmin={false}
            />
            {/* Conditionally show upload section based on build limits */}
            {builds.length < 2 ? (
              <UppyFileUpload
                    projectId={project.id}
                    onUploadComplete={handleUploadComplete}
                    onUploadError={handleUploadError}
                  />
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>Build Limit Reached</CardTitle>
                  <CardDescription>
                    You have reached the maximum of 2 builds per project. Delete an existing build to upload a new one.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">
                    Active builds: {builds.filter(b => !['failed', 'archived'].includes(b.status)).length}/2
                  </p>
                </CardContent>
              </Card>
            )}
            <BuildHistory
              projectId={project.id}
              builds={builds}
              onBuildRevert={handleBuildRevert}
              onBuildDelete={handleBuildDelete}
              onBuildActivate={handleBuildActivate}
              onRefresh={fetchBuilds}
              isAdmin={false}
            />
          </div>
          {/* Sidebar Info */}
          <div>
            <Card>
              <CardHeader><CardTitle>Project Settings</CardTitle></CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="auto-release" className="text-sm font-medium text-gray-700">
                      Auto-Release
                    </Label>
                    <p className="text-xs text-gray-500">
                      Automatically activate new builds when uploaded
                    </p>
                  </div>
                  <Switch
                    id="auto-release"
                    checked={autoRelease}
                    onCheckedChange={handleAutoReleaseToggle}
                    disabled={loading}
                  />
                </div>
                <div><p className="text-sm font-medium text-gray-500">Created</p>
                <p className="text-sm text-gray-900">{formatDate(project.created_at)}</p></div>
                <div><p className="text-sm font-medium text-gray-500">Last Updated</p>
                <p className="text-sm text-gray-900">{formatDate(project.updated_at)}</p></div>
                <div><p className="text-sm font-medium text-gray-500">Total Builds</p>
                <p className="text-sm text-gray-900">{builds.length}</p></div>
              </CardContent>
            </Card>
          </div>
        </div>
       
        {error && (
          <div className="mt-4 text-red-600 text-center">{error}</div>
        )}
        {loading && (
          <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
            <Loader2 className="h-10 w-10 animate-spin text-white" />
          </div>
        )}
      </main>
    </div>
  )
}
