-- Insert sample data for development
-- Note: In production, users will be created through authentication

-- Sample platform admin user (this would be created through auth in real scenario)
-- INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at)
-- VALUES (
--     '00000000-0000-0000-0000-000000000001',
--     '<EMAIL>',
--     crypt('admin123', gen_salt('bf')),
--     NOW(),
--     NOW(),
--     NOW()
-- );

-- Sample profile for platform admin
INSERT INTO profiles (id, email, role, created_at, updated_at)
VALUES (
    '00000000-0000-0000-0000-000000000001',
    '<EMAIL>',
    'platform_admin',
    NOW(),
    NOW()
) ON CONFLICT (id) DO NOTHING;

-- Sample regular user profile
INSERT INTO profiles (id, email, role, created_at, updated_at)
VALUES (
    '00000000-0000-0000-0000-000000000002',
    '<EMAIL>',
    'user',
    NOW(),
    NOW()
) ON CONFLICT (id) DO NOTHING;

-- <PERSON><PERSON> projects
INSERT INTO projects (id, user_id, name, stream_project_id, config, created_at, updated_at)
VALUES 
(
    '10000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000002',
    'Sample Game Project',
    'stream_project_123',
    '{"resolution": "1920x1080", "fps": 60}',
    NOW(),
    NOW()
),
(
    '10000000-0000-0000-0000-000000000002',
    '00000000-0000-0000-0000-000000000002',
    'Demo Application',
    'stream_project_456',
    '{"resolution": "1280x720", "fps": 30}',
    NOW(),
    NOW()
) ON CONFLICT (id) DO NOTHING;

-- Sample builds
INSERT INTO builds (id, project_id, filename, s3_key, version, created_at)
VALUES 
(
    '20000000-0000-0000-0000-000000000001',
    '10000000-0000-0000-0000-000000000001',
    'game-v1.0.zip',
    'projects/10000000-0000-0000-0000-000000000001/builds/game-v1.0.zip',
    1,
    NOW() - INTERVAL '2 days'
),
(
    '20000000-0000-0000-0000-000000000002',
    '10000000-0000-0000-0000-000000000001',
    'game-v1.1.zip',
    'projects/10000000-0000-0000-0000-000000000001/builds/game-v1.1.zip',
    2,
    NOW() - INTERVAL '1 day'
),
(
    '20000000-0000-0000-0000-000000000003',
    '10000000-0000-0000-0000-000000000002',
    'demo-app-v1.0.zip',
    'projects/10000000-0000-0000-0000-000000000002/builds/demo-app-v1.0.zip',
    1,
    NOW() - INTERVAL '3 days'
) ON CONFLICT (id) DO NOTHING;
