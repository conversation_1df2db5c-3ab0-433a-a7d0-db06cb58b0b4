import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BuildHistory } from '@/components/build-history'
import { Build } from '@/lib/supabase'

// Mock data
const mockBuilds: Build[] = [
  {
    id: '1',
    project_id: 'project-1',
    filename: 'game-v1.zip',
    s3_key: 'uploads/user1/project1/game-v1.zip',
    version: 1,
    status: 'active',
    is_current: true,
    file_size: 1024000,
    streampixel_build_id: 'sp-build-1',
    streampixel_status: 'uploaded',
    error_message: null,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    project_id: 'project-1',
    filename: 'game-v2.zip',
    s3_key: 'uploads/user1/project1/game-v2.zip',
    version: 2,
    status: 'archived',
    is_current: false,
    file_size: 2048000,
    streampixel_build_id: null,
    streampixel_status: null,
    error_message: null,
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
  },
]

describe('BuildHistory Component', () => {
  const defaultProps = {
    projectId: 'project-1',
    builds: mockBuilds,
    onRefresh: jest.fn(),
    isAdmin: false,
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders build history correctly', () => {
    render(<BuildHistory {...defaultProps} />)
    
    expect(screen.getByText('Build History')).toBeInTheDocument()
    expect(screen.getByText('2 total')).toBeInTheDocument()
    expect(screen.getByText('Current Active Build')).toBeInTheDocument()
    expect(screen.getByText('Previous Builds (1)')).toBeInTheDocument()
  })

  it('displays current build information', () => {
    render(<BuildHistory {...defaultProps} />)
    
    expect(screen.getByText('game-v1.zip')).toBeInTheDocument()
    expect(screen.getByText('Version 1')).toBeInTheDocument()
    expect(screen.getByText('Current')).toBeInTheDocument()
    expect(screen.getByText('1.00 MB')).toBeInTheDocument()
  })

  it('displays archived builds', () => {
    render(<BuildHistory {...defaultProps} />)
    
    expect(screen.getByText('game-v2.zip')).toBeInTheDocument()
    expect(screen.getByText('Version 2')).toBeInTheDocument()
    expect(screen.getByText('archived')).toBeInTheDocument()
  })

  it('calls onBuildDelete when delete button is clicked', async () => {
    const onBuildDelete = jest.fn()
    const props = { ...defaultProps, onBuildDelete }
    
    // Mock window.confirm
    window.confirm = jest.fn(() => true)
    
    render(<BuildHistory {...props} />)
    
    const deleteButtons = screen.getAllByRole('button', { name: /delete/i })
    fireEvent.click(deleteButtons[0])
    
    await waitFor(() => {
      expect(onBuildDelete).toHaveBeenCalledWith('1')
    })
  })

  it('calls onBuildActivate when activate button is clicked', async () => {
    const onBuildActivate = jest.fn()
    const props = { ...defaultProps, onBuildActivate }
    
    // Mock window.confirm
    window.confirm = jest.fn(() => true)
    
    render(<BuildHistory {...props} />)
    
    const activateButton = screen.getByRole('button', { name: /activate/i })
    fireEvent.click(activateButton)
    
    await waitFor(() => {
      expect(onBuildActivate).toHaveBeenCalledWith('2')
    })
  })

  it('shows build limit information', () => {
    render(<BuildHistory {...defaultProps} />)
    
    expect(screen.getByText('Build Management')).toBeInTheDocument()
    expect(screen.getByText(/1 active build.*1 archived build/)).toBeInTheDocument()
    expect(screen.getByText(/Only.*ZIP files.*are accepted/)).toBeInTheDocument()
  })

  it('handles empty builds list', () => {
    const props = { ...defaultProps, builds: [] }
    render(<BuildHistory {...props} />)
    
    expect(screen.getByText('No builds yet')).toBeInTheDocument()
    expect(screen.getByText('Upload your first build to get started.')).toBeInTheDocument()
  })

  it('shows error message when build has error', () => {
    const buildsWithError: Build[] = [
      {
        ...mockBuilds[0],
        status: 'failed',
        error_message: 'Upload failed: Network error',
      },
    ]
    
    const props = { ...defaultProps, builds: buildsWithError }
    render(<BuildHistory {...props} />)
    
    expect(screen.getByText('Upload failed: Network error')).toBeInTheDocument()
  })

  it('disables buttons during operations', async () => {
    const onBuildDelete = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)))
    const props = { ...defaultProps, onBuildDelete }
    
    window.confirm = jest.fn(() => true)
    
    render(<BuildHistory {...props} />)
    
    const deleteButton = screen.getAllByRole('button', { name: /delete/i })[0]
    fireEvent.click(deleteButton)
    
    // Button should be disabled during operation
    expect(deleteButton).toBeDisabled()
  })

  it('shows admin badge when isAdmin is true', () => {
    const props = { ...defaultProps, isAdmin: true }
    render(<BuildHistory {...props} />)
    
    expect(screen.getByText('Admin Access')).toBeInTheDocument()
  })
})
