'use client'

import { useState, useMemo } from 'react'
import { Navigation } from '@/components/navigation'
import { ProjectCard } from '@/components/project-card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Plus, Search } from 'lucide-react'
import { Project } from '@/lib/supabase'
import { User } from '@/types/supabase'

interface DashboardProps {
  user: User ;
  profile: {
    role?: string;
  };
  projects: Project[];
}

export default function DashboardClient({ user, profile, projects }: DashboardProps) {
  const [searchTerm, setSearchTerm] = useState('')

  const isAdmin = profile?.role === 'platform_admin'

  const filteredProjects = useMemo(() => 
    projects.filter(project =>
      project.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.stream_project_id?.toLowerCase().includes(searchTerm.toLowerCase())
    ),
    [projects, searchTerm]
  )

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation user={user} profile={profile} />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
              <p className="text-gray-600 mt-1">
                Welcome back, {user.email}
                {isAdmin && (
                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    Platform Admin
                  </span>
                )}
              </p>
            </div>

            {isAdmin && (
              <Button onClick={() => window.location.href = '/admin'}>
                <Plus className="h-4 w-4 mr-2" />
                Admin Panel
              </Button>
            )}
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search projects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Projects Grid */}
        {filteredProjects.length === 0 ? (
          <Card>
            <CardContent className="py-12">
              <div className="text-center">
                {projects.length === 0 ? (
                  <>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No projects assigned</h3>
                    <p className="text-gray-600 mb-4">
                      {isAdmin
                        ? "Use the Admin Panel to create projects for users."
                        : "Contact your administrator to get projects assigned to your account."
                      }
                    </p>
                    {isAdmin && (
                      <Button onClick={() => window.location.href = '/admin'}>
                        <Plus className="h-4 w-4 mr-2" />
                        Go to Admin Panel
                      </Button>
                    )}
                  </>
                ) : (
                  <>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
                    <p className="text-gray-600">Try adjusting your search terms.</p>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProjects.map((project:Project) => (
              <ProjectCard key={project.id} project={project} />
            ))}
          </div>
        )}
      </main>
    </div>
  )
}
