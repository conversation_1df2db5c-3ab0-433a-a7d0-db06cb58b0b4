'use client'
import { useState } from 'react'
import { loginAction, signupAction } from './actions'
import { useFormStatus } from 'react-dom'
import {Input} from "@/components/ui/input"
import {Button} from "@/components/ui/button"
import {Label} from "@/components/ui/label"
import {Card, CardHeader, CardTitle, CardDescription, CardContent} from "@/components/ui/card"

export default function LoginForm() {
  const [isSignUp, setIsSignUp] = useState(false)
  const [feedback, setFeedback] = useState<string | null>(null)
  const { pending } = useFormStatus()

  async function onSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault()
    setFeedback(null)
    const form = new FormData(e.currentTarget)
    const result = isSignUp ? await signupAction(form) : await loginAction(form)
    if (result.error) setFeedback(result.error)
    else if (result.message) setFeedback(result.message)
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center">
        <CardTitle>Omnipixel</CardTitle>
        <CardDescription>
          {isSignUp ? 'Create an account' : 'Sign in to your account'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={onSubmit} className="space-y-4">
          <div>
            <Label htmlFor="email">Email</Label>
            <Input id="email" name="email" type="email" required />
          </div>
          <div>
            <Label htmlFor="password">Password</Label>
            <Input id="password" name="password" type="password" required minLength={6} />
          </div>
          {feedback && <div className="text-sm text-red-600 bg-red-50 p-3 rounded">{feedback}</div>}
          <Button type="submit" disabled={pending}>
            {pending ? 'Loading…' : isSignUp ? 'Sign Up' : 'Sign In'}
          </Button>
        </form>
        <div className="mt-4 text-center">
          <button onClick={() => { setIsSignUp(!isSignUp); setFeedback(null) }} className="text-sm text-blue-600 hover:underline">
            {isSignUp ? 'Already have an account? Sign in' : "Don't have an account? Sign up"}
          </button>
        </div>
      </CardContent>
    </Card>
  )
}
