Stack trace:
Frame         Function      Args
0007FFFFBDD0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFACD0) msys-2.0.dll+0x2118E
0007FFFFBDD0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFBDD0  0002100469F2 (00021028DF99, 0007FFFFBC88, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBDD0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBDD0  00021006A545 (0007FFFFBDE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFBDE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAE6980000 ntdll.dll
7FFAE5910000 KERNEL32.DLL
7FFAE4130000 KERNELBASE.dll
7FFAE5E40000 USER32.dll
7FFAE3AD0000 win32u.dll
7FFAE5020000 GDI32.dll
7FFAE4520000 gdi32full.dll
7FFAE4080000 msvcp_win.dll
7FFAE3B00000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFAE4F60000 advapi32.dll
7FFAE6010000 msvcrt.dll
7FFAE6200000 sechost.dll
7FFAE57F0000 RPCRT4.dll
7FFAE29D0000 CRYPTBASE.DLL
7FFAE3E60000 bcryptPrimitives.dll
7FFAE5C50000 IMM32.DLL
