import { NextRequest, NextResponse } from 'next/server'
import { 
  generatePresignedUploadUrl,
  isAWSConfigured 
} from '@/lib/aws'
import { createClient } from '@/utils/supabase/server'

export async function POST(request: NextRequest) {
  try {
    // Check if AWS is configured
    if (!isAWSConfigured()) {
      return NextResponse.json(
        { error: 'File upload is not configured. Please contact the administrator.' },
        { status: 503 }
      )
    }

    // Create Supabase client for server-side auth
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { key, uploadId, partNumber } = body

    // Validate required fields
    if (!key || !uploadId || !partNumber) {
      return NextResponse.json(
        { error: 'Missing required fields: key, uploadId, partNumber' },
        { status: 400 }
      )
    }

    // Generate presigned URL for the part
    const presignedUrl = await generatePresignedUploadUrl(
      key,
      'application/octet-stream',
      uploadId,
      partNumber
    )

    return NextResponse.json({
      url: presignedUrl
    })

  } catch (error) {
    console.error('Error in sign-part:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
