# OmniPixel MVP

A comprehensive platform for game streaming and build management with whitelabeled, embeddable streaming capabilities.

## 🌟 Overview

OmniPixel MVP is a full-stack application that enables game developers to upload, manage, and stream their builds through an intuitive web interface. The platform features whitelabeled streaming players, password protection, and easy embedding capabilities.

## ✨ Key Features

- **🔐 Secure Authentication**: Email/password login with role-based access control
- **📁 Project Management**: Create, edit, and manage projects with version control
- **⬆️ Large File Upload**: Multipart upload system supporting files up to 50GB
- **🎮 Whitelabeled Streaming**: Embeddable, password-protected streaming with custom UI
- **🛡️ Admin Interface**: Complete platform administration for users and projects
- **🚀 Easy Deployment**: One-command AWS deployment with automatic environment loading
- **📱 Responsive Design**: Mobile-friendly interface with modern UI components
- **🔧 Configurable Player**: Form-based configuration for all streaming settings

## 🚀 Quick Start

### Prerequisites

- **Node.js 18+** - [Download](https://nodejs.org/)
- **AWS Account** - [Sign up](https://aws.amazon.com/free/)
- **Supabase Account** - [Sign up](https://supabase.com/)
- **StreamPixel Account** - [Sign up](https://streampixel.io/)

### 1. Clone and Install

```bash
# Clone the repository
git clone <repository-url>
cd omnipixel-mvp

# Install dependencies
npm install
```

### 2. Environment Setup

```bash
# Copy environment template
npm run setup:env

# Edit .env.local with your credentials
# Required variables:
#   NEXT_PUBLIC_SUPABASE_URL
#   SUPABASE_SERVICE_ROLE_KEY
#   STREAMPIXEL_API_KEY
```

### 3. Database Setup

```bash
# Setup Supabase database
npm run setup:db
```

### 4. Development Server

```bash
# Start development server
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see the application.

### 5. Deploy to AWS

```bash
# Cross-platform Node.js script (Recommended)
npm run deploy:aws

# Environment-specific deployments
npm run deploy:aws:staging
npm run deploy:aws:prod

# AWS profiles (multiple accounts)
npm run deploy:aws -- -p my-profile
npm run deploy:aws -- -e prod -p production-profile

# Advanced options
npm run deploy:aws -- -h      # Show all options
npm run deploy:aws -- -e dev -r eu-west-1 -p my-profile
```

## 📋 Available Scripts

### Development
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Testing
- `npm test` - Run unit tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage
- `npm run test:e2e` - Run end-to-end tests
- `npm run test:all` - Run all tests

### Setup & Deployment
- `npm run setup:env` - Copy environment template
- `npm run setup:aws` - Check AWS prerequisites and setup
- `npm run deploy:aws` - Deploy to AWS (cross-platform)
- `npm run deploy:aws:staging` - Deploy to staging environment
- `npm run deploy:aws:prod` - Deploy to production environment
- `npm run deploy:aws:help` - Show deployment options

## 🏗️ Architecture

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - Modern UI components

### Backend
- **Next.js API Routes** - Serverless API endpoints
- **Supabase** - Database and authentication
- **AWS S3** - File storage with multipart upload
- **AWS Lambda** - Serverless functions

### Streaming
- **StreamPixel** - Interactive streaming platform
- **Whitelabeled Player** - Custom-branded streaming experience
- **Embeddable** - iframe-ready for external websites

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file with the following variables:

```bash
# Supabase Configuration (Required)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY=your_publishable_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# AWS Configuration (Required for deployment)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_S3_BUCKET_NAME=omnipixel-uploads

# StreamPixel Configuration (Required)
STREAMPIXEL_API_KEY=your_streampixel_api_key

# Optional Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret
NODE_ENV=development
```

### Stream Player Configuration

The platform includes a comprehensive configuration editor for:

- **Stream Settings**: Resolution, codecs, input controls
- **Security**: Password protection and access control
- **UI Messages**: Customizable loading, connecting, and error messages
- **Embedding**: Generate iframe codes for external websites

## 🚀 Deployment

### AWS Deployment (Recommended)

The platform includes automated AWS deployment scripts:

#### Prerequisites
- AWS CLI installed and configured
- SAM CLI installed
- Environment variables set in `.env.local`

#### Quick Deployment
```bash
# Check prerequisites and deploy (Cross-platform)
npm run setup:aws

# Or deploy directly (Cross-platform)
npm run deploy:aws
```

#### Environment-Specific Deployment
```bash
npm run deploy:aws:staging    # Deploy to staging
npm run deploy:aws:prod       # Deploy to production
```

### Vercel Deployment

For frontend-only deployment:

```bash
# Install Vercel CLI
npm install -g vercel

# Deploy to Vercel
vercel --prod
```

## 🧪 Testing

### Unit Tests
```bash
npm test                      # Run all unit tests
npm run test:watch           # Run tests in watch mode
npm run test:coverage        # Generate coverage report
```

### End-to-End Tests
```bash
npm run test:e2e             # Run E2E tests
npm run test:e2e:ui          # Run E2E tests with UI
```

### Test Coverage Goals
- **Branches**: 70%+
- **Functions**: 70%+
- **Lines**: 70%+
- **Statements**: 70%+

## 📚 Documentation

### Guides
- [Deployment Guide](docs/deployment.md) - Complete deployment instructions
- [Testing Guide](docs/testing.md) - Testing strategy and setup
- [Stream Player Features](docs/stream-player-features.md) - Player capabilities

### AWS Deployment
- [AWS README](aws/README.md) - AWS deployment guide
- [Windows Guide](aws/WINDOWS.md) - Windows-specific instructions
- [AWS Profiles Guide](docs/aws-profiles.md) - Multiple AWS accounts support

### API Documentation
- [API Routes](docs/api.md) - API endpoint documentation
- [Database Schema](docs/database.md) - Database structure

## 🔒 Security

### Authentication
- Supabase Auth with email/password
- Row Level Security (RLS) policies
- Role-based access control (user/admin)

### File Upload Security
- Presigned URLs for secure uploads
- File type validation
- Size limits and virus scanning

### Stream Security
- Optional password protection
- Embed security controls
- Cross-origin restrictions

## 🌍 Browser Support

### Desktop
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### Mobile
- iOS Safari 13+
- Chrome Mobile 80+
- Samsung Internet 12+

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Workflow
1. Run tests: `npm run test:all`
2. Check linting: `npm run lint`
3. Test deployment: `npm run deploy:aws`

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Common Issues
- [Troubleshooting Guide](docs/troubleshooting.md)
- [FAQ](docs/faq.md)

### Getting Help
1. Check the documentation
2. Search existing issues
3. Create a new issue with detailed information

## 🗺️ Roadmap

### Current Version (v1.0)
- ✅ Basic project management
- ✅ File upload system
- ✅ Whitelabeled streaming
- ✅ AWS deployment

### Upcoming Features
- 🔄 Real-time collaboration
- 🔄 Advanced analytics
- 🔄 Multi-region deployment
- 🔄 Mobile app

## 📊 Performance

### Metrics
- **Upload Speed**: Up to 50GB files supported
- **Streaming Latency**: <100ms with StreamPixel
- **Page Load**: <2s initial load
- **Build Time**: <30s for production builds

### Optimization
- Code splitting and lazy loading
- Image optimization
- CDN distribution via Vercel
- Database query optimization

---

**Built with ❤️ for game developers**
