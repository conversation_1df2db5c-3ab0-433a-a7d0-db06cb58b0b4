import { NextRequest, NextResponse } from 'next/server'
import {
  createMultipartUpload,
  isAWSConfigured
} from '@/lib/aws'
import { createClient } from '@/utils/supabase/server'
import { randomUUID } from 'crypto'

export async function POST(request: NextRequest) {
  try {
    // Check if AWS is configured
    if (!isAWSConfigured()) {
      return NextResponse.json(
        { error: 'File upload is not configured. Please contact the administrator.' },
        { status: 503 }
      )
    }

    // Create Supabase client for server-side auth
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { filename, type, projectId, fileSize } = body

    // Validate required fields
    if (!filename || !projectId || !fileSize) {
      return NextResponse.json(
        { error: 'Missing required fields: filename, projectId, fileSize' },
        { status: 400 }
      )
    }

    // Get user profile to check if they're a platform admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    const isPlatformAdmin = profile?.role === 'platform_admin'

    // Verify that the project exists and user has access (owner or platform admin)
    let query = supabase
      .from('projects')
      .select('id, user_id')
      .eq('id', projectId)

    // If not platform admin, restrict to user's own projects
    if (!isPlatformAdmin) {
      query = query.eq('user_id', user.id)
    }

    const { data: project, error: projectError } = await query.single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    }

    // Generate build ID for unique filename
    const buildId = randomUUID()

    // Use build ID as filename for guaranteed uniqueness: [build-id].zip
    const newFilename = `${buildId}.zip`
    // Use project owner's user ID for consistent file organization
    const s3Key = `uploads/${project.user_id}/${projectId}/${newFilename}`

    // Create multipart upload
    const { uploadId } = await createMultipartUpload(s3Key, type || 'application/octet-stream')

    // Return parameters for Uppy AWS S3 plugin
    return NextResponse.json({
      key: s3Key,
      uploadId,
      bucket: process.env.AWS_S3_BUCKET_NAME,
      region: process.env.AWS_REGION,
      // Include build ID and filename for the complete route
      buildId,
      filename: newFilename,
      originalFilename: filename
    })

  } catch (error) {
    console.error('Error in multipart-params:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
