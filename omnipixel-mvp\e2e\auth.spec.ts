import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Start from the home page
    await page.goto('/')
  })

  test('should redirect to login when not authenticated', async ({ page }) => {
    // Try to access dashboard without authentication
    await page.goto('/dashboard')
    
    // Should be redirected to login
    await expect(page).toHaveURL('/login')
    await expect(page.locator('h1')).toContainText('Sign In')
  })

  test('should display login form correctly', async ({ page }) => {
    await page.goto('/login')
    
    // Check login form elements
    await expect(page.locator('h1')).toContainText('Sign In')
    await expect(page.locator('input[type="email"]')).toBeVisible()
    await expect(page.locator('input[type="password"]')).toBeVisible()
    await expect(page.locator('button[type="submit"]')).toBeVisible()
    await expect(page.locator('text=Don\'t have an account?')).toBeVisible()
  })

  test('should display signup form correctly', async ({ page }) => {
    await page.goto('/signup')
    
    // Check signup form elements
    await expect(page.locator('h1')).toContainText('Create Account')
    await expect(page.locator('input[type="email"]')).toBeVisible()
    await expect(page.locator('input[type="password"]')).toBeVisible()
    await expect(page.locator('button[type="submit"]')).toBeVisible()
    await expect(page.locator('text=Already have an account?')).toBeVisible()
  })

  test('should show validation errors for invalid login', async ({ page }) => {
    await page.goto('/login')
    
    // Try to submit empty form
    await page.click('button[type="submit"]')
    
    // Should show validation errors
    await expect(page.locator('text=Invalid email or password')).toBeVisible()
  })

  test('should navigate between login and signup', async ({ page }) => {
    await page.goto('/login')
    
    // Click signup link
    await page.click('text=Sign up')
    await expect(page).toHaveURL('/signup')
    await expect(page.locator('h1')).toContainText('Create Account')
    
    // Click login link
    await page.click('text=Sign in')
    await expect(page).toHaveURL('/login')
    await expect(page.locator('h1')).toContainText('Sign In')
  })

  test('should handle network errors gracefully', async ({ page }) => {
    await page.goto('/login')
    
    // Mock network failure
    await page.route('**/auth/v1/token**', route => {
      route.abort('failed')
    })
    
    // Fill form and submit
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    
    // Should show error message
    await expect(page.locator('text=Network error')).toBeVisible()
  })
})

test.describe('Authenticated User Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Mock successful authentication
    await page.addInitScript(() => {
      // Mock localStorage for auth state
      localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: 'mock-token',
        refresh_token: 'mock-refresh',
        user: {
          id: 'test-user-id',
          email: '<EMAIL>',
        },
      }))
    })
  })

  test('should access dashboard when authenticated', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/projects', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          projects: [
            {
              id: 'project-1',
              name: 'Test Project',
              builds: [],
              created_at: '2024-01-01T00:00:00Z',
            },
          ],
        }),
      })
    })

    await page.goto('/dashboard')
    
    // Should see dashboard content
    await expect(page.locator('h1')).toContainText('My Projects')
    await expect(page.locator('text=Test Project')).toBeVisible()
  })

  test('should logout successfully', async ({ page }) => {
    await page.goto('/dashboard')
    
    // Click logout button
    await page.click('button:has-text("Sign Out")')
    
    // Should be redirected to login
    await expect(page).toHaveURL('/login')
  })
})

test.describe('Admin Authentication', () => {
  test.beforeEach(async ({ page }) => {
    // Mock admin authentication
    await page.addInitScript(() => {
      localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: 'mock-admin-token',
        refresh_token: 'mock-refresh',
        user: {
          id: 'admin-user-id',
          email: '<EMAIL>',
        },
      }))
    })
  })

  test('should access admin panel when admin', async ({ page }) => {
    // Mock admin profile response
    await page.route('**/api/profile', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          profile: {
            id: 'admin-user-id',
            email: '<EMAIL>',
            role: 'platform_admin',
          },
        }),
      })
    })

    // Mock admin projects response
    await page.route('**/api/admin/projects', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          projects: [],
        }),
      })
    })

    await page.goto('/admin')
    
    // Should see admin panel
    await expect(page.locator('h1')).toContainText('Admin Panel')
    await expect(page.locator('text=Manage Users')).toBeVisible()
  })

  test('should deny access to admin panel for regular users', async ({ page }) => {
    // Mock regular user profile response
    await page.route('**/api/profile', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          profile: {
            id: 'user-id',
            email: '<EMAIL>',
            role: 'user',
          },
        }),
      })
    })

    await page.goto('/admin')
    
    // Should see access denied message
    await expect(page.locator('text=Access denied')).toBeVisible()
  })
})
