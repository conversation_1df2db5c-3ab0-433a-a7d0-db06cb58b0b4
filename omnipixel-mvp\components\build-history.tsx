'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import {
  History,
  Download,
  RotateCcw,
  CheckCircle,
  Clock,
  Archive,
  AlertCircle,
  Upload,
  Loader2,
  Calendar,
  HardDrive,
  Trash2,
  Play,
  Zap
} from 'lucide-react'
import { Build } from '@/lib/supabase'

interface BuildHistoryProps {
  projectId: string
  builds: Build[]
  onBuildRevert?: (buildId: string) => void
  onBuildDelete?: (buildId: string) => void
  onBuildActivate?: (buildId: string) => void
  onRefresh?: () => void
  isAdmin?: boolean
  className?: string
}

export function BuildHistory({
  projectId,
  builds,
  onBuildRevert,
  onBuildDelete,
  onBuildActivate,
  onRefresh,
  isAdmin = false,
  className = ''
}: BuildHistoryProps) {
  const [isReverting, setIsReverting] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState<string | null>(null)
  const [isActivating, setIsActivating] = useState<string | null>(null)
  const [selectedBuild, setSelectedBuild] = useState<Build | null>(null)

  const getStatusIcon = (status: Build['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'uploading':
        return <Upload className="h-4 w-4 text-blue-500" />
      case 'processing':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'inactive':
        return <Play className="h-4 w-4 text-gray-500" />
      case 'archived':
        return <Archive className="h-4 w-4 text-gray-500" />
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: Build['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'uploading':
        return 'bg-blue-100 text-blue-800'
      case 'processing':
        return 'bg-yellow-100 text-yellow-800'
      case 'inactive':
        return 'bg-gray-100 text-gray-600'
      case 'archived':
        return 'bg-gray-100 text-gray-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size'
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // Check if a build has been processing for more than 15 minutes since activation
  const isProcessingTimedOut = (build: Build) => {
    if (build.status !== 'processing' || !build.activated_at) return false

    const activatedAt = new Date(build.activated_at)
    const now = new Date()
    const diffMinutes = (now.getTime() - activatedAt.getTime()) / (1000 * 60)

    return diffMinutes > 15
  }

  const handleRevert = async (build: Build) => {
    if (!onBuildRevert || build.is_current) return

    const confirmRevert = confirm(
      `Are you sure you want to revert to version ${build.version} (${build.original_filename || build.filename})? This will make it the current active build.`
    )

    if (!confirmRevert) return

    try {
      setIsReverting(build.id)
      await onBuildRevert(build.id)
      onRefresh?.()
    } catch (error) {
      console.error('Error reverting build:', error)
      alert('Failed to revert build. Please try again.')
    } finally {
      setIsReverting(null)
    }
  }

  const handleDelete = async (build: Build) => {
    if (!onBuildDelete) return

    // Allow deletion of all builds - users should have full control
    const confirmDelete = confirm(
      `Are you sure you want to delete version ${build.version} (${build.original_filename || build.filename})? This will permanently remove the ZIP file from storage and cannot be undone.`
    )

    if (!confirmDelete) return

    try {
      setIsDeleting(build.id)
      await onBuildDelete(build.id)
      onRefresh?.()
    } catch (error) {
      console.error('Error deleting build:', error)
      alert('Failed to delete build. Please try again.')
    } finally {
      setIsDeleting(null)
    }
  }

  const handleActivate = async (build: Build) => {
    if (!onBuildActivate || build.is_current) return

    const confirmActivate = confirm(
      `Are you sure you want to activate version ${build.version} (${build.original_filename || build.filename})? This will make it the current active build and upload it to the stream server.`
    )

    if (!confirmActivate) return

    try {
      setIsActivating(build.id)
      await onBuildActivate(build.id)
      onRefresh?.()
    } catch (error) {
      console.error('Error activating build:', error)
      alert('Failed to activate build. Please try again.')
    } finally {
      setIsActivating(null)
    }
  }

  const activeBuild = builds.find(build => build.is_current)
  const archivedBuilds = builds.filter(build => !build.is_current).slice(0, 10) // Show max 10 archived builds

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <History className="h-5 w-5" />
            <CardTitle>Builds</CardTitle>
            <Badge variant="secondary">{builds.length} total</Badge>
          </div>
          
          {onRefresh && (
            <Button size="sm" variant="outline" onClick={onRefresh}>
              Refresh
            </Button>
          )}
        </div>
        
        <CardDescription>
          View and manage build versions. Each project can have up to 2 active builds.
          {!isAdmin && " You can revert to any previous version."}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Current Build */}
        {activeBuild && (
          <div>
            
            <div className="border border-green-200 rounded-lg p-4 bg-green-50">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h5 className="font-medium text-gray-900">{activeBuild.original_filename || activeBuild.filename}</h5>
                    <Badge className={getStatusColor(activeBuild.status)}>
                      Version {activeBuild.version}
                    </Badge>
                    <Badge variant="outline" className="bg-green-100 text-green-800">
                      Current
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {formatDate(activeBuild.created_at)}
                    </div>
                    {activeBuild.file_size && (
                      <div className="flex items-center">
                        <HardDrive className="h-4 w-4 mr-1" />
                        {formatFileSize(activeBuild.file_size)}
                      </div>
                    )}
                    <div className="flex items-center">
                      {getStatusIcon(activeBuild.status)}
                      <span className="ml-1 capitalize">{activeBuild.status}</span>
                    </div>
                  </div>
                  
                  {activeBuild.streampixel_status && (
                    <div className="mt-2">
                      <Badge
                        variant="outline"
                        className={`text-xs ${
                          activeBuild.streampixel_status === 'uploaded' || activeBuild.streampixel_status === 'live' ? 'bg-green-50 text-green-700 border-green-200' :
                          activeBuild.streampixel_status === 'validation_failed' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                          activeBuild.streampixel_status === 'waiting_for_file' || activeBuild.streampixel_status === 'processing' || activeBuild.streampixel_status === 'stabilizing' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                          activeBuild.streampixel_status === 'failed' || activeBuild.streampixel_status === 'file_not_accessible' || activeBuild.streampixel_status === 'timeout' ? 'bg-red-50 text-red-700 border-red-200' :
                          ''
                        }`}
                      >
                        {activeBuild.streampixel_status === 'waiting_for_file' ? 'Waiting for file' :
                         activeBuild.streampixel_status === 'file_not_accessible' ? 'File not accessible' :
                         activeBuild.streampixel_status === 'stabilizing' ? 'Stabilizing file' :
                         activeBuild.streampixel_status === 'processing' ? 'Processing' :
                         activeBuild.streampixel_status === 'live' ? 'Live' :
                         activeBuild.streampixel_status === 'uploaded' ? 'Uploaded' :
                         activeBuild.streampixel_status === 'validation_failed' ? 'Validation failed' :
                         activeBuild.streampixel_status === 'timeout' ? 'Activation timeout' :
                         activeBuild.streampixel_status === 'failed' ? 'Failed' :
                         activeBuild.streampixel_status}
                      </Badge>
                    </div>
                  )}
                </div>
                
                <div className="flex space-x-2">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button size="sm" variant="outline" onClick={() => setSelectedBuild(activeBuild)}>
                        View Details
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Build Details - Version {activeBuild.version}</DialogTitle>
                        <DialogDescription>
                          Detailed information about this build
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium text-gray-700">Filename</label>
                          <p className="text-sm text-gray-900">{activeBuild.original_filename || activeBuild.filename}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-700">Status</label>
                          <div className="flex items-center mt-1">
                            {getStatusIcon(activeBuild.status)}
                            <span className="ml-2 text-sm capitalize">{activeBuild.status}</span>
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-700">Upload Date</label>
                          <p className="text-sm text-gray-900">{formatDate(activeBuild.created_at)}</p>
                        </div>
                        {activeBuild.file_size && (
                          <div>
                            <label className="text-sm font-medium text-gray-700">File Size</label>
                            <p className="text-sm text-gray-900">{formatFileSize(activeBuild.file_size)}</p>
                          </div>
                        )}
                        {activeBuild.streampixel_build_id && (
                          <div>
                            <label className="text-sm font-medium text-gray-700">Stream Service Build ID</label>
                            <p className="text-sm text-gray-900 font-mono">{activeBuild.streampixel_build_id}</p>
                          </div>
                        )}
                        {activeBuild.error_message && (
                          <div>
                            <label className="text-sm font-medium text-red-700">Error Message</label>
                            <p className="text-sm text-red-900 bg-red-50 p-2 rounded">{activeBuild.error_message}</p>
                          </div>
                        )}
                      </div>
                    </DialogContent>
                  </Dialog>

                  <Button size="sm" variant="outline">
                    <Download className="h-4 w-4" />
                  </Button>

                  {onBuildDelete && (
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDelete(activeBuild)}
                      disabled={isDeleting === activeBuild.id}
                      title="Delete build"
                    >
                      {isDeleting === activeBuild.id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

          <div>
           
            <div className="space-y-3">
              {archivedBuilds.map((build, index) => (
                <div key={build.id || `build-${index}-${build.original_filename || build.filename}-${build.version}`} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h5 className="font-medium text-gray-900">{build.original_filename || build.filename}</h5>
                        <Badge variant="outline">Version {build.version}</Badge>
                        <Badge className={getStatusColor(build.status)}>
                          {build.status}
                        </Badge>
                        {build.streampixel_status && (
                          <Badge
                            variant="outline"
                            className={`text-xs ${
                              build.streampixel_status === 'uploaded' || build.streampixel_status === 'live' ? 'bg-green-50 text-green-700 border-green-200' :
                              build.streampixel_status === 'validation_failed' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                              build.streampixel_status === 'waiting_for_file' || build.streampixel_status === 'processing' || build.streampixel_status === 'stabilizing' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                              build.streampixel_status === 'failed' || build.streampixel_status === 'file_not_accessible' || build.streampixel_status === 'timeout' ? 'bg-red-50 text-red-700 border-red-200' :
                              ''
                            }`}
                          >
                            {build.streampixel_status === 'waiting_for_file' ? 'Waiting for file' :
                             build.streampixel_status === 'file_not_accessible' ? 'File not accessible' :
                             build.streampixel_status === 'stabilizing' ? 'Stabilizing file' :
                             build.streampixel_status === 'processing' ? 'Processing' :
                             build.streampixel_status === 'live' ? 'Live' :
                             build.streampixel_status === 'uploaded' ? 'Uploaded' :
                             build.streampixel_status === 'validation_failed' ? 'Validation failed' :
                             build.streampixel_status === 'timeout' ? 'Activation timeout' :
                             build.streampixel_status === 'failed' ? 'Failed' :
                             build.streampixel_status}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          {formatDate(build.created_at)}
                        </div>
                        {build.file_size && (
                          <div className="flex items-center">
                            <HardDrive className="h-4 w-4 mr-1" />
                            {formatFileSize(build.file_size)}
                          </div>
                        )}
                      </div>
                      
                      {build.error_message && (
                        <div className="mt-2">
                          <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
                            {build.error_message}
                          </p>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex space-x-2">
                      {(build.status === 'archived' || build.status === 'inactive' || isProcessingTimedOut(build)) && onBuildActivate && (
                        <Button
                          size="sm"
                          variant="default"
                          onClick={() => handleActivate(build)}
                          disabled={isActivating === build.id}
                        >
                          {isActivating === build.id ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Zap className="h-4 w-4" />
                          )}
                          {isActivating === build.id ? 'Activating...' : 'Activate'}
                        </Button>
                      )}

                      <Button size="sm" variant="outline">
                        <Download className="h-4 w-4" />
                      </Button>

                      {onBuildDelete && (
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDelete(build)}
                          disabled={isDeleting === build.id}
                          title="Delete build"
                        >
                          {isDeleting === build.id ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

        {/* Empty State */}
        {builds.length === 0 && (
          <div className="text-center py-8">
            <History className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No builds yet</h3>
            <p className="text-gray-600">Upload your first build to get started.</p>
          </div>
        )}

        {/* Build Limit Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <AlertCircle className="h-5 w-5 text-blue-500" />
            </div>
            <div className="ml-3">
              <h4 className="text-sm font-medium text-blue-900">Build Management</h4>
              <div className="mt-1 text-sm text-blue-700">
                <p>• Each project can have <strong>maximum 2 builds</strong> (active, inactive, or archived)</p>
                <p>• New builds are <strong>inactive by default</strong> unless auto-release is enabled</p>
                <p>• Only <strong>ZIP files</strong> are accepted for game builds</p>
                <p>• You must <strong>delete a build</strong> to upload a new one when limit is reached</p>
                <p>• Activate an inactive/archived build to make it current and upload to the stream server</p>
                <p>• Activated builds show as <strong>processing</strong> until the stream serviceconfirms they are live</p>
                <p>• <strong>Any build can be deleted</strong> - you have full control over your builds</p>
                <p>• Deleting a build permanently removes the ZIP file from storage</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
