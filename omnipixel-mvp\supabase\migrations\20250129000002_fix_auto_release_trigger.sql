-- Fix Auto-Release Trigger to Prevent Immediate Activation
-- Builds should only become current after StreamPixel webhook confirms success

-- Drop existing trigger and function
DROP TRIGGER IF EXISTS trigger_set_first_build_current ON builds;
DROP FUNCTION IF EXISTS set_first_build_current();

-- Create updated function that respects the new flow
CREATE OR REPLACE FUNCTION set_first_build_current()
RETURNS TRIGGER AS $$
DECLARE
    current_build_count INTEGER;
    project_auto_release BOOLEAN;
BEGIN
    -- Count current builds for this project
    SELECT COUNT(*) INTO current_build_count
    FROM builds 
    WHERE project_id = NEW.project_id 
    AND is_current = true
    AND status NOT IN ('failed', 'archived');
    
    -- Get project's auto_release setting
    SELECT auto_release INTO project_auto_release
    FROM projects 
    WHERE id = NEW.project_id;
    
    -- If no current build exists
    IF current_build_count = 0 THEN
        -- Check if this is the first build ever for this project
        DECLARE
            total_builds INTEGER;
        BEGIN
            SELECT COUNT(*) INTO total_builds
            FROM builds 
            WHERE project_id = NEW.project_id;
            
            -- If first build ever OR auto_release is enabled, set to processing
            -- DON'T set as current until StreamPixel webhook confirms
            IF total_builds = 0 OR project_auto_release = true THEN
                NEW.is_current = false; -- Wait for StreamPixel webhook
                NEW.status = 'processing'; -- Will be sent to StreamPixel
            ELSE
                -- Otherwise, keep it inactive
                NEW.status = 'inactive';
                NEW.is_current = false;
            END IF;
        END;
    ELSE
        -- If there's already a current build, this one should be inactive by default
        -- unless auto_release is enabled
        IF project_auto_release = true THEN
            -- Auto-release: DON'T deactivate current build yet, wait for StreamPixel
            -- Set new build to processing, but not current until webhook confirms
            NEW.is_current = false; -- Wait for StreamPixel webhook
            NEW.status = 'processing'; -- Will be sent to StreamPixel
        ELSE
            -- Manual activation: keep this build inactive
            NEW.status = 'inactive';
            NEW.is_current = false;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate trigger with updated function
CREATE TRIGGER trigger_set_first_build_current
    BEFORE INSERT ON builds
    FOR EACH ROW
    EXECUTE FUNCTION set_first_build_current();

-- Also update the manage_current_build function to prevent auto-activation
DROP TRIGGER IF EXISTS trigger_manage_current_build ON builds;
DROP FUNCTION IF EXISTS manage_current_build();

CREATE OR REPLACE FUNCTION manage_current_build()
RETURNS TRIGGER AS $$
BEGIN
    -- If this build is being set as current
    IF NEW.is_current = true AND (OLD IS NULL OR OLD.is_current = false) THEN
        -- Unset all other builds in this project as current
        UPDATE builds 
        SET is_current = false, status = 'archived', updated_at = NOW()
        WHERE project_id = NEW.project_id 
        AND id != NEW.id 
        AND is_current = true;
        
        -- Set this build as active (only when explicitly set as current)
        NEW.status = 'active';
    END IF;
    
    -- If this build is being set as not current
    IF NEW.is_current = false AND OLD IS NOT NULL AND OLD.is_current = true THEN
        -- Only archive if not processing (processing builds can be not current)
        IF NEW.status != 'processing' THEN
            NEW.status = 'archived';
        END IF;
    END IF;
    
    -- Update the updated_at timestamp
    NEW.updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate trigger
CREATE TRIGGER trigger_manage_current_build
    BEFORE UPDATE ON builds
    FOR EACH ROW
    EXECUTE FUNCTION manage_current_build();
