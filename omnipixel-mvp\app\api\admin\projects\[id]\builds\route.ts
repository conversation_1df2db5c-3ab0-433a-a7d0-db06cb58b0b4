import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server';

// POST - Add build to project (admin only)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is platform admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'platform_admin') {
      return NextResponse.json(
        { error: 'Access denied. Platform admin role required.' },
        { status: 403 }
      )
    }

    const {id:projectId} = await params

    // Verify project exists
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, name')
      .eq('id', projectId)
      .single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { filename, s3_key, version } = body

    // Validate required fields
    if (!filename || !s3_key || version === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: filename, s3_key, version' },
        { status: 400 }
      )
    }

    // Check if version already exists for this project
    const { data: existingBuild } = await supabase
      .from('builds')
      .select('id')
      .eq('project_id', projectId)
      .eq('version', version)
      .single()

    if (existingBuild) {
      return NextResponse.json(
        { error: `Version ${version} already exists for this project` },
        { status: 400 }
      )
    }

    // Create the build
    const { data: build, error: createError } = await supabase
      .from('builds')
      .insert([
        {
          project_id: projectId,
          filename,
          s3_key,
          version,
        }
      ])
      .select()
      .single()

    if (createError) {
      console.error('Error creating build:', createError)
      return NextResponse.json(
        { error: 'Failed to create build' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Build created successfully',
      build
    })

  } catch (error: unknown) {
    console.error('Error in admin build creation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET - List builds for project (admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient()

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is platform admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'platform_admin') {
      return NextResponse.json(
        { error: 'Access denied. Platform admin role required.' },
        { status: 403 }
      )
    }

    const {id:projectId} = await params

    // Get all builds for the project
    const { data: builds, error: fetchError } = await supabase
      .from('builds')
      .select('*')
      .eq('project_id', projectId)
      .order('version', { ascending: false })

    if (fetchError) {
      console.error('Error fetching builds:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch builds' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      builds: builds || []
    })

  } catch (error: unknown) {
    console.error('Error in admin builds fetch:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE - Delete build (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient()

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is platform admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'platform_admin') {
      return NextResponse.json(
        { error: 'Access denied. Platform admin role required.' },
        { status: 403 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { build_id } = body

    if (!build_id) {
      return NextResponse.json(
        { error: 'Missing required field: build_id' },
        { status: 400 }
      )
    }

    const {id} = await params
    // Delete the build
    const { error: deleteError } = await supabase
      .from('builds')
      .delete()
      .eq('id', build_id)
      .eq('project_id', id) // Ensure build belongs to this project

    if (deleteError) {
      console.error('Error deleting build:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete build' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Build deleted successfully'
    })

  } catch (error: unknown) {
    console.error('Error in admin build deletion:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
