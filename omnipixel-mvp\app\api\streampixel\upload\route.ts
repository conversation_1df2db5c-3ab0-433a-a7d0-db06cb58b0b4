import { NextRequest, NextResponse } from 'next/server'
import { getPublicS3FileUrl, waitForFileAccessibility } from '@/lib/aws'
import { createClient } from '@/utils/supabase/server'

// Stream Service API configuration
const STREAMPIXEL_API_URL = 'https://api.streampixel.io/pixelStripeApi/projects/upload-file'

interface StreamPixelUploadRequest {
  apiKey: string
  userId: string
  fileUrl: string
  projectId: string
  autoRelease: boolean
}



// POST - Upload build to StreamPixel
export async function POST(request: NextRequest) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient()

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { buildId, projectId, forceAutoRelease } = body

    // Validate required fields
    if (!buildId || !projectId) {
      return NextResponse.json(
        { error: 'Missing required fields: buildId, projectId' },
        { status: 400 }
      )
    }

    // Validate Stream Service configuration
    if (!process.env.STREAMPIXEL_API_KEY || !process.env.STREAMPIXEL_USER_ID) {
      console.error('Stream Service configuration check:', {
        apiKey: process.env.STREAMPIXEL_API_KEY ? 'Present' : 'Missing',
        userId: process.env.STREAMPIXEL_USER_ID ? 'Present' : 'Missing'
      })
      return NextResponse.json(
        {
          error: 'Stream Service API configuration missing',
          details: {
            apiKey: process.env.STREAMPIXEL_API_KEY ? 'Present' : 'Missing',
            userId: process.env.STREAMPIXEL_USER_ID ? 'Present' : 'Missing'
          }
        },
        { status: 500 }
      )
    }

    // Verify user owns this project or is admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to verify user profile' },
        { status: 500 }
      )
    }

    const isAdmin = profile?.role === 'platform_admin'

    // Get project details
    let projectQuery = supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)

    if (!isAdmin) {
      projectQuery = projectQuery.eq('user_id', user.id)
    }

    const { data: project, error: projectError } = await projectQuery.single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    }

    // Verify project has Stream Service configuration
    if (!project.stream_project_id) {
      return NextResponse.json(
        { error: 'Project does not have Stream Service configuration' },
        { status: 400 }
      )
    }

    // Get build details
    const { data: build, error: buildError } = await supabase
      .from('builds')
      .select('*')
      .eq('id', buildId)
      .eq('project_id', projectId)
      .single()

    if (buildError || !build) {
      return NextResponse.json(
        { error: 'Build not found' },
        { status: 404 }
      )
    }

    // Check if build is already uploaded to StreamPixel
    if (build.streampixel_build_id && build.streampixel_status === 'uploaded') {
      return NextResponse.json(
        { error: 'Build is already uploaded to StreamPixel' },
        { status: 400 }
      )
    }

    // Make the specific Backblaze B2 object public for Stream Service access
    // console.log('Making Backblaze B2 object public for StreamPixel:', build.b2_key)
    // try {
    //   await makeS3ObjectPublic(build.b2_key)
    //   console.log('Successfully made Backblaze B2 object public')
    // } catch (aclError) {
    //   console.error('Failed to make Backblaze B2 object public:', aclError)
    //   return NextResponse.json(
    //     {
    //       error: 'Failed to make file publicly accessible',
    //       details: 'Could not set public read permissions on the file'
    //     },
    //     { status: 500 }
    //   )
    // }

    // Generate public Backblaze B2 URL for Stream Service (no expiration)
    const fileUrl = getPublicS3FileUrl(build.s3_key)
    console.log('Generated public Backblaze B2 URL for StreamPixel:', fileUrl)

    // Update build status to waiting for file accessibility
    await supabase
      .from('builds')
      .update({
        status: 'processing',
        streampixel_status: 'waiting_for_file',
        updated_at: new Date().toISOString()
      })
      .eq('id', buildId)

    // Update status to show we're checking file accessibility
    await supabase
      .from('builds')
      .update({
        streampixel_status: 'waiting_for_file',
        updated_at: new Date().toISOString()
      })
      .eq('id', buildId)

    // Wait for file to be accessible before sending to StreamPixel
    console.log('🔄 Waiting for file to become accessible...')

    let isFileAccessible = false
    try {
      isFileAccessible = await waitForFileAccessibility(fileUrl)
    } catch (pollingError) {
      console.error('❌ Error during file accessibility polling:', pollingError)

      const errorMessage = 'Failed to verify file accessibility - polling error occurred'

      await supabase
        .from('builds')
        .update({
          status: 'failed',
          streampixel_status: 'polling_error',
          error_message: errorMessage,
          updated_at: new Date().toISOString()
        })
        .eq('id', buildId)

      return NextResponse.json({
        error: errorMessage,
        build: {
          id: build.id,
          s3_key: build.s3_key,
          streampixel_status: 'polling_error'
        }
      }, { status: 500 })
    }

    if (!isFileAccessible) {
      // File is not accessible after polling timeout
      const errorMessage = 'File not accessible after 5 minutes of polling - Backblaze may need more time to process the file. Please try activating the build manually later.'

      await supabase
        .from('builds')
        .update({
          status: 'uploaded', // Keep as uploaded since the file exists, just not accessible yet
          streampixel_status: 'file_not_accessible',
          error_message: errorMessage,
          updated_at: new Date().toISOString()
        })
        .eq('id', buildId)

      return NextResponse.json({
        error: errorMessage,
        build: {
          id: build.id,
          s3_key: build.s3_key,
          streampixel_status: 'file_not_accessible'
        }
      }, { status: 408 }) // 408 Request Timeout
    }

    console.log('✅ File is now accessible, checking if additional stabilization time is needed...')

    // Check how long ago the file was uploaded
    const { data: buildData, error: buildFetchError } = await supabase
      .from('builds')
      .select('created_at, activated_at')
      .eq('id', buildId)
      .single()

    if (buildFetchError || !buildData) {
      console.error('Error fetching build data:', buildFetchError)
      throw new Error('Failed to fetch build data')
    }

    // Calculate time since upload - for manual activation, always use created_at (original upload time)
    // For auto-release, use activated_at if available (when auto-activation was triggered)
    const isManualActivation = forceAutoRelease !== undefined
    const referenceTime = isManualActivation ? buildData.created_at : (buildData.activated_at || buildData.created_at)
    const timeSinceReference = Date.now() - new Date(referenceTime).getTime()
    const minStabilizationTime = 60000 // 60 seconds minimum

    // For manual activation, if file has been around for a while, just check accessibility
    const shouldSkipStabilization = isManualActivation && timeSinceReference > minStabilizationTime

    console.log(`📊 Timing analysis: ${isManualActivation ? 'Manual activation' : 'Auto-release'}, file uploaded ${Math.round(timeSinceReference / 1000)}s ago (reference: ${isManualActivation ? 'created_at' : 'activated_at || created_at'})`)

    let waitTime = 0
    if (!shouldSkipStabilization && timeSinceReference < minStabilizationTime) {
      waitTime = minStabilizationTime - timeSinceReference
      console.log(`⏳ File needs stabilization: waiting additional ${Math.round(waitTime / 1000)}s...`)

      // Update build status to indicate we're waiting for file stabilization
      await supabase
        .from('builds')
        .update({
          status: 'processing',
          streampixel_status: 'stabilizing',
          updated_at: new Date().toISOString()
        })
        .eq('id', buildId)

      await new Promise(resolve => setTimeout(resolve, waitTime))
    } else {
      if (shouldSkipStabilization) {
        console.log(`✅ Manual activation: File uploaded ${Math.round(timeSinceReference / 1000)}s ago, skipping stabilization wait`)

        // For manual activation of older files, do a quick accessibility check
        console.log('🔄 Performing quick accessibility check for manual activation...')

        // Update status to show we're doing a quick check
        await supabase
          .from('builds')
          .update({
            status: 'processing',
            streampixel_status: 'processing',
            updated_at: new Date().toISOString()
          })
          .eq('id', buildId)

        // Quick accessibility check (just one attempt)
        try {
          const response = await fetch(fileUrl, { method: 'HEAD' })
          if (!response.ok) {
            throw new Error(`File not accessible: ${response.status}`)
          }
          console.log('✅ Quick accessibility check passed')
        } catch (error) {
          console.error('❌ Quick accessibility check failed:', error)

          await supabase
            .from('builds')
            .update({
              status: 'failed',
              streampixel_status: 'file_not_accessible',
              error_message: 'File not accessible for manual activation',
              updated_at: new Date().toISOString()
            })
            .eq('id', buildId)

          return NextResponse.json({
            error: 'File not accessible for manual activation',
            build: {
              id: build.id,
              s3_key: build.s3_key,
              streampixel_status: 'file_not_accessible'
            }
          }, { status: 400 })
        }
      } else {
        console.log(`✅ File uploaded ${Math.round(timeSinceReference / 1000)}s ago, sufficient time has passed`)
      }
    }

    console.log('✅ File ready, proceeding with StreamPixel upload')

    // Determine if we should auto-release:
    // 1. If forceAutoRelease is explicitly set (manual activation), use that
    // 2. Otherwise, use the project's auto_release setting
    const shouldAutoRelease = forceAutoRelease !== undefined ? forceAutoRelease : project.auto_release

    console.log(`🔄 StreamPixel upload - Project auto_release: ${project.auto_release}, forceAutoRelease: ${forceAutoRelease}, final autoRelease: ${shouldAutoRelease}`)

    // Prepare Stream Service API request
    const streamPixelRequest: StreamPixelUploadRequest = {
      apiKey: process.env.STREAMPIXEL_API_KEY!,
      userId: process.env.STREAMPIXEL_USER_ID!,
      fileUrl: fileUrl,
      projectId: project.stream_project_id,
      autoRelease: shouldAutoRelease
    }

    console.log('Stream Service request details:', {
      apiKey: process.env.STREAMPIXEL_API_KEY ? 'Present' : 'Missing',
      userId: process.env.STREAMPIXEL_USER_ID,
      fileUrl: fileUrl,
      projectId: project.stream_project_id,
      autoRelease: shouldAutoRelease
    })

    // Update build status to uploading
    await supabase
      .from('builds')
      .update({
        status: 'processing',
        streampixel_status: 'uploading',
        updated_at: new Date().toISOString()
      })
      .eq('id', buildId)

    try {
      // Call Stream Service API with correct endpoint
      console.log('Stream Service upload request:', streamPixelRequest)
      console.log('Stream Service API URL:', STREAMPIXEL_API_URL)

      const streamPixelResponse = await fetch(STREAMPIXEL_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(streamPixelRequest),
      })

      let streamPixelData
      try {
        const responseText = await streamPixelResponse.text()
        console.log('Stream Service API response:', responseText)

        if (responseText.startsWith('<!DOCTYPE') || responseText.startsWith('<html')) {
          throw new Error('Stream Service API returned HTML instead of JSON - likely an error page')
        }

        streamPixelData = JSON.parse(responseText)
      } catch (parseError) {
        console.error('Failed to parse Stream Service response:', parseError)
        throw new Error('Invalid response from Stream Service API')
      }

      if (streamPixelResponse.ok && streamPixelData.success) {
        // Update build with successful upload - but not live yet, waiting for webhook
        const { data: updatedBuild, error: updateError } = await supabase
          .from('builds')
          .update({
            status: 'processing', // Processing by StreamPixel, not live yet
            streampixel_build_id: streamPixelData.uploadId || 'pending',
            streampixel_status: 'processing', // Processing, not uploaded yet
            error_message: null, // Clear any previous errors
            updated_at: new Date().toISOString()
          })
          .eq('id', buildId)
          .select()
          .single()

        if (updateError) {
          console.error('Error updating build after Stream Service upload:', updateError)
        }

        return NextResponse.json({
          message: 'Build sent to Stream Service for processing. Final status will be updated via webhook.',
          build: updatedBuild,
          streamPixelResponse: streamPixelData
        })

      } else {
        // Handle Stream Service API errors (including validation failures)
        let errorMessage = streamPixelData.error || streamPixelData.message || 'Stream Service upload failed'
        let statusType = 'failed'
        let buildStatus = 'failed'

        // Check if it's a validation error (URL access issue)
        if (errorMessage.includes('Validation failed for URL')) {
          statusType = 'validation_failed'
          buildStatus = 'uploaded' // Build uploaded successfully, but Stream Service validation failed
          errorMessage = 'File URL validation failed - Stream Service cannot access the file. Please ensure S3 bucket has public read access.'
        }

        // Update build with error details
        await supabase
          .from('builds')
          .update({
            status: buildStatus,
            error_message: errorMessage,
            streampixel_status: statusType,
            is_current: false, // Ensure build is not marked as current if validation fails
            updated_at: new Date().toISOString()
          })
          .eq('id', buildId)

        return NextResponse.json(
          {
            error: errorMessage,
            type: statusType,
            details: streamPixelData
          },
          { status: 500 }
        )
      }

    } catch (streamPixelError: unknown) {
      console.error('Error calling Stream Service API:', streamPixelError)

      // Determine error type and message
      let errorMessage = streamPixelError instanceof Error ? streamPixelError.message : 'Failed to connect to StreamPixel'
      let statusType = 'failed'
      let buildStatus = 'failed'

      // Check if it's a validation error (URL access issue)
      if (errorMessage.includes('Validation failed for URL')) {
        statusType = 'validation_failed'
        buildStatus = 'uploaded' // Build uploaded successfully, but Stream Service validation failed
        errorMessage = 'File URL validation failed - Stream Service cannot access the file. Please ensure S3 bucket has public read access.'
      }

      // Update build with specific error details
      await supabase
        .from('builds')
        .update({
          status: buildStatus,
          error_message: errorMessage,
          streampixel_status: statusType,
          is_current: false, // Ensure build is not marked as current if validation fails
          updated_at: new Date().toISOString()
        })
        .eq('id', buildId)

      return NextResponse.json(
        {
          error: 'Stream Service upload failed',
          details: errorMessage,
          type: statusType
        },
        { status: 500 }
      )
    }

  } catch (error: unknown) {
    console.error('Error in Stream Service upload:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
