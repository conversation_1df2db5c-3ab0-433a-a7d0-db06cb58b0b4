import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

// POST - Revert to a specific build version
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const projectId = params.id

    // Verify user owns this project or is admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to verify user profile' },
        { status: 500 }
      )
    }

    const isAdmin = profile?.role === 'platform_admin'

    // If not admin, verify project ownership
    if (!isAdmin) {
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('id')
        .eq('id', projectId)
        .eq('user_id', user.id)
        .single()

      if (projectError || !project) {
        return NextResponse.json(
          { error: 'Project not found or access denied' },
          { status: 404 }
        )
      }
    }

    // Parse request body
    const body = await request.json()
    const { build_id } = body

    if (!build_id) {
      return NextResponse.json(
        { error: 'Missing required field: build_id' },
        { status: 400 }
      )
    }

    // Verify the build exists and belongs to this project
    const { data: targetBuild, error: buildError } = await supabase
      .from('builds')
      .select('*')
      .eq('id', build_id)
      .eq('project_id', projectId)
      .single()

    if (buildError || !targetBuild) {
      return NextResponse.json(
        { error: 'Build not found' },
        { status: 404 }
      )
    }

    // Check if build is already current
    if (targetBuild.is_current) {
      return NextResponse.json(
        { error: 'This build is already the current active build' },
        { status: 400 }
      )
    }

    // Check if build can be reverted (not failed)
    if (targetBuild.status === 'failed') {
      return NextResponse.json(
        { error: 'Cannot revert to a failed build' },
        { status: 400 }
      )
    }

    // Start transaction to revert build
    // First, unset current build
    const { error: unsetError } = await supabase
      .from('builds')
      .update({ 
        is_current: false, 
        status: 'archived',
        updated_at: new Date().toISOString()
      })
      .eq('project_id', projectId)
      .eq('is_current', true)

    if (unsetError) {
      console.error('Error unsetting current build:', unsetError)
      return NextResponse.json(
        { error: 'Failed to unset current build' },
        { status: 500 }
      )
    }

    // Set target build as current
    const { data: updatedBuild, error: setError } = await supabase
      .from('builds')
      .update({ 
        is_current: true, 
        status: 'active',
        updated_at: new Date().toISOString()
      })
      .eq('id', build_id)
      .select()
      .single()

    if (setError) {
      console.error('Error setting build as current:', setError)
      return NextResponse.json(
        { error: 'Failed to set build as current' },
        { status: 500 }
      )
    }

    // Get updated project with all builds for response
    const { data: projectWithBuilds, error: fetchError } = await supabase
      .from('projects')
      .select(`
        *,
        builds (
          id,
          filename,
          s3_key,
          version,
          status,
          is_current,
          file_size,
          streampixel_build_id,
          streampixel_status,
          error_message,
          created_at,
          updated_at
        )
      `)
      .eq('id', projectId)
      .single()

    if (fetchError) {
      console.error('Error fetching updated project:', fetchError)
      // Still return success since the revert worked
    }

    return NextResponse.json({
      message: `Successfully reverted to version ${targetBuild.version}`,
      build: updatedBuild,
      project: projectWithBuilds || null
    })

  } catch (error: unknown) {
    console.error('Error reverting build:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET - Get build revert history/options
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient()

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const projectId = params.id

    // Verify user owns this project or is admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to verify user profile' },
        { status: 500 }
      )
    }

    const isAdmin = profile?.role === 'platform_admin'

    // If not admin, verify project ownership
    if (!isAdmin) {
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('id')
        .eq('id', projectId)
        .eq('user_id', user.id)
        .single()

      if (projectError || !project) {
        return NextResponse.json(
          { error: 'Project not found or access denied' },
          { status: 404 }
        )
      }
    }

    // Get all builds that can be reverted to (not current, not failed)
    const { data: revertableBuilds, error: fetchError } = await supabase
      .from('builds')
      .select('*')
      .eq('project_id', projectId)
      .eq('is_current', false)
      .neq('status', 'failed')
      .order('version', { ascending: false })

    if (fetchError) {
      console.error('Error fetching revertable builds:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch build history' },
        { status: 500 }
      )
    }

    // Get current build info
    const { data: currentBuild, error: currentError } = await supabase
      .from('builds')
      .select('*')
      .eq('project_id', projectId)
      .eq('is_current', true)
      .single()

    if (currentError) {
      console.error('Error fetching current build:', currentError)
    }

    return NextResponse.json({
      revertableBuilds: revertableBuilds || [],
      currentBuild: currentBuild || null,
      canRevert: (revertableBuilds?.length || 0) > 0
    })

  } catch (error: unknown) {
    console.error('Error fetching build revert options:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
