# Deployment Guide

This document outlines the deployment process for the OmniPixel MVP application.

## Architecture Overview

The OmniPixel application uses a hybrid deployment architecture:

- **Frontend**: Next.js application deployed on Vercel
- **Backend APIs**: Next.js API routes on Vercel
- **File Storage**: AWS S3 with Lambda functions
- **Database**: Supabase (hosted)
- **Streaming**: StreamPixel integration

## Prerequisites

### Required Accounts
1. **Vercel Account**: For frontend deployment
2. **AWS Account**: For S3 storage and Lambda functions
3. **Supabase Account**: For database and authentication
4. **StreamPixel Account**: For streaming services
5. **GitHub Account**: For CI/CD pipeline (optional)

### Required Tools
- Node.js 18+
- AWS CLI
- SAM CLI
- Vercel CLI (optional)

## 🚀 Quick Start (Easiest Method)

### 1. Environment Setup
```bash
# Copy the environment template
npm run setup:env

# Edit .env.local with your actual values
# Required variables:
#   NEXT_PUBLIC_SUPABASE_URL
#   SUPABASE_SERVICE_ROLE_KEY
#   STREAMPIXEL_API_KEY
```

### 2. AWS Deployment (One Command)

#### Using npm scripts (Cross-platform):
```bash
# Setup environment and check prerequisites
npm run setup:aws

# Deploy to AWS (works on all platforms)
npm run deploy:aws

# Environment-specific deployments
npm run deploy:aws:staging
npm run deploy:aws:prod

# Show all deployment options
npm run deploy:aws:help
```

That's it! The deployment script automatically:
- ✅ Loads your .env.local configuration
- ✅ Validates all required variables
- ✅ Deploys AWS infrastructure
- ✅ Configures S3, Lambda, and API Gateway
- ✅ Provides deployment URLs and bucket names

## Environment Configuration

### Production Environment Variables

#### Vercel Environment Variables
Set these in your Vercel project settings:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY=your_publishable_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET_NAME=omnipixel-uploads-prod

# StreamPixel Configuration
STREAMPIXEL_API_KEY=your_streampixel_api_key
```

#### GitHub Secrets
Set these in your GitHub repository settings:

```bash
# Vercel
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_vercel_org_id
VERCEL_PROJECT_ID=your_vercel_project_id

# AWS
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# StreamPixel
STREAMPIXEL_API_KEY=your_streampixel_api_key
```

## Deployment Process

## 🛠️ Manual Deployment Process

### 1. AWS Infrastructure Deployment

#### Easy Method (Recommended)
```bash
cd aws
./deploy.sh -e prod  # Automatically uses .env.local
```

#### Manual Method
```bash
cd aws
sam build --template-file template.yaml
sam deploy \
  --template-file .aws-sam/build/template.yaml \
  --stack-name omnipixel-prod \
  --region us-east-1 \
  --capabilities CAPABILITY_IAM \
  --parameter-overrides \
    Environment=prod \
    SupabaseUrl=https://your-project.supabase.co \
    SupabaseServiceRoleKey=your_service_role_key \
    StreamPixelApiKey=your_streampixel_api_key \
  --confirm-changeset \
  --resolve-s3
```

### 2. Database Setup

Run the Supabase migrations:

```bash
# Install Supabase CLI
npm install -g supabase

# Link to your project
supabase link --project-ref your-project-ref

# Run migrations
supabase db push
```

### 3. Vercel Deployment

#### Manual Deployment
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy to production
vercel --prod
```

#### Automatic Deployment
Push to the `main` branch to trigger automatic deployment via GitHub Actions.

## 🎯 Deployment Options

### Local Development Deployment
Perfect for testing and development:

```bash
npm run deploy:aws                    # Deploy to dev environment
npm run deploy:aws:staging           # Deploy to staging environment
```

### Production Deployment
For production deployments:

```bash
npm run deploy:aws:prod              # Deploy to production environment
```

### Advanced Options
Override specific settings:

```bash
# Different region
npm run deploy:aws -- -e prod -r eu-west-1

# Custom stack name
npm run deploy:aws -- -e prod -s my-custom-stack

# AWS profile for multiple accounts
npm run deploy:aws -- -e prod -p production-profile

# Show all available options
npm run deploy:aws:help
```

### Environment Variable Sources
The deployment script automatically detects and uses:

1. **Local Development**: Loads from `.env.local` file
2. **CI/CD Pipeline**: Uses GitHub Actions environment variables
3. **Manual Override**: Command line arguments take precedence

## CI/CD Pipeline

### GitHub Actions Workflow

The deployment pipeline includes:

1. **Test Stage**
   - Linting
   - Unit tests with coverage
   - End-to-end tests

2. **Build Stage**
   - Next.js application build
   - Artifact upload

3. **Deploy AWS Stage**
   - SAM application deployment
   - Infrastructure provisioning

4. **Deploy Vercel Stage**
   - Frontend deployment
   - Environment variable injection

5. **Notification Stage**
   - Deployment status notification

### Pipeline Triggers
- **Push to main**: Full deployment to production
- **Pull requests**: Test and build only
- **Manual trigger**: Available for emergency deployments

## Environment-Specific Configurations

### Development
```bash
# Local development
npm run dev

# Environment: .env.local
NODE_ENV=development
NEXT_PUBLIC_SUPABASE_URL=https://dev-project.supabase.co
```

### Staging
```bash
# Staging deployment
vercel --target staging

# Environment: Vercel staging environment
NODE_ENV=staging
NEXT_PUBLIC_SUPABASE_URL=https://staging-project.supabase.co
```

### Production
```bash
# Production deployment
vercel --prod

# Environment: Vercel production environment
NODE_ENV=production
NEXT_PUBLIC_SUPABASE_URL=https://prod-project.supabase.co
```

## Monitoring & Health Checks

### Application Monitoring
- **Vercel Analytics**: Performance monitoring
- **Supabase Dashboard**: Database metrics
- **AWS CloudWatch**: Lambda function logs
- **Error Tracking**: Built-in error boundaries

### Health Check Endpoints
```bash
# Application health
GET /api/health

# Database connectivity
GET /api/health/database

# AWS services
GET /api/health/aws
```

## Rollback Procedures

### Vercel Rollback
```bash
# List deployments
vercel ls

# Rollback to previous deployment
vercel rollback [deployment-url]
```

### AWS Rollback
```bash
# Rollback CloudFormation stack
aws cloudformation cancel-update-stack --stack-name omnipixel-prod

# Or deploy previous version
sam deploy --template-file previous-template.yaml
```

### Database Rollback
```bash
# Rollback Supabase migration
supabase migration down
```

## Security Considerations

### Environment Variables
- Never commit secrets to version control
- Use GitHub Secrets for CI/CD
- Rotate keys regularly
- Use least-privilege IAM policies

### Network Security
- HTTPS only in production
- CORS configuration
- API rate limiting
- Input validation

### Data Protection
- Encrypted data at rest (S3, Supabase)
- Encrypted data in transit (TLS)
- User data isolation (RLS policies)
- Regular security audits

## Performance Optimization

### Frontend Optimization
- Next.js static generation
- Image optimization
- Code splitting
- CDN distribution (Vercel Edge Network)

### Backend Optimization
- API response caching
- Database query optimization
- Lambda cold start mitigation
- S3 transfer acceleration

### Monitoring Performance
- Core Web Vitals tracking
- API response time monitoring
- Database query performance
- File upload speed metrics

## Troubleshooting

### Common Issues

#### Deployment Failures
```bash
# Check Vercel logs
vercel logs

# Check AWS CloudFormation events
aws cloudformation describe-stack-events --stack-name omnipixel-prod

# Check GitHub Actions logs
# View in GitHub Actions tab
```

#### Database Connection Issues
```bash
# Test Supabase connection
curl -H "apikey: your-anon-key" https://your-project.supabase.co/rest/v1/

# Check RLS policies
# Review in Supabase dashboard
```

#### File Upload Issues
```bash
# Check S3 bucket permissions
aws s3api get-bucket-policy --bucket omnipixel-uploads-prod

# Test Lambda functions
aws lambda invoke --function-name omnipixel-presigned-url-prod response.json
```

### Debug Commands
```bash
# Local debugging
npm run dev
npm run test:watch

# Production debugging
vercel logs --follow
aws logs tail /aws/lambda/omnipixel-presigned-url-prod --follow
```

## Maintenance

### Regular Tasks
- **Weekly**: Review error logs and performance metrics
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Review and rotate API keys
- **Annually**: Conduct security audit and penetration testing

### Backup Procedures
- **Database**: Automated Supabase backups
- **Files**: S3 versioning and cross-region replication
- **Code**: Git repository with multiple remotes
- **Configuration**: Infrastructure as Code (SAM templates)

## Cost Optimization

### Vercel Costs
- Monitor function execution time
- Optimize bundle size
- Use static generation where possible

### AWS Costs
- Monitor S3 storage and transfer costs
- Optimize Lambda memory allocation
- Use S3 lifecycle policies for old files

### Supabase Costs
- Monitor database size and queries
- Optimize RLS policies
- Use connection pooling

## Future Enhancements

### Planned Improvements
- Multi-region deployment
- Blue-green deployment strategy
- Automated performance testing
- Enhanced monitoring and alerting
- Disaster recovery procedures

### Scaling Considerations
- Database read replicas
- CDN optimization
- Lambda concurrency limits
- S3 request rate optimization
