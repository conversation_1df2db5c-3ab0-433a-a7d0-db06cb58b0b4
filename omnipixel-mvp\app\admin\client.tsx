'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Navigation } from '@/components/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Card, CardContent, CardDescription, CardHeader, CardTitle,
} from '@/components/ui/card'
import {
  Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import {
  Plus, Users, Package, Loader2, ArrowLeft, Calendar, User as UserIcon, Edit2, Trash2, Eye,
} from 'lucide-react'
import { User } from '@/types/supabase'
import { StreamPixelConfig } from 'streampixelsdk'
import { Profile } from '@/lib/supabase'

interface AdminProject {
  id: string
  name: string
  stream_project_id: string
  user_id: string
  config: StreamPixelConfig // You can import StreamPixelConfig if needed
  created_at: string
  updated_at: string
  profiles: {
    email: string
    role: string
  }
  builds: Array<{
    id: string
    filename: string
    original_filename?: string
    version: number
    created_at: string
  }>
}

export default function AdminClient({
  user,
  profile,
  initialProjects,
}: {
  user: User
  profile: Profile
  initialProjects: AdminProject[]
}) {
  const router = useRouter()
  const [projects, setProjects] = useState<AdminProject[]>(initialProjects)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [newProject, setNewProject] = useState({
    name: '',
    stream_project_id: '',
    user_email: '',
  })

  // Fetch projects from API for mutations
  const fetchProjects = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch('/api/admin/projects')
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch projects')
      }
      const data = await response.json()
      setProjects(data.projects)
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'Unknown Error')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateProject = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newProject.name || !newProject.stream_project_id || !newProject.user_email) return
    try {
      setIsCreating(true)
      const response = await fetch('/api/admin/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newProject),
      })
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create project')
      }
      const data = await response.json()
      setNewProject({ name: '', stream_project_id: '', user_email: '' })
      setIsCreateDialogOpen(false)
      await fetchProjects()
      alert(data.message)
    } catch (err: unknown) {
      alert('Failed to create project: ' + (err instanceof Error ? err.message : 'Unknown error'))
    } finally {
      setIsCreating(false)
    }
  }

  const handleDeleteProject = async (projectId: string, projectName: string) => {
    const confirmDelete = confirm(
      `Are you sure you want to delete "${projectName}"? This action cannot be undone and will delete all associated builds.`
    )
    if (!confirmDelete) return
    try {
      const response = await fetch('/api/admin/projects', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ project_id: projectId }),
      })
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete project')
      }
      alert('Project deleted successfully!')
      await fetchProjects()
    } catch (err: unknown) {
      alert('Failed to delete project: ' + (err instanceof Error ? err.message : 'Unknown error'))
    }
  }

  const formatDate = (dateString: string) =>
    new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit',
    })

  // (Optional) Could fetch projects if you want real-time up-to-date, but SSR takes care of initial load
  // useEffect(() => { setProjects(initialProjects) }, [initialProjects])

  if (!user || profile?.role !== 'platform_admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card>
          <CardContent className="py-8">
            <div className="text-center text-red-600">
              <p>Access denied. Platform admin role required.</p>
              <Button
                variant="outline"
                onClick={() => router.push('/dashboard')}
                className="mt-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation user={user} profile={profile} />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => router.push('/dashboard')}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Admin Panel</h1>
              <p className="text-gray-600 mt-1">
                Manage projects and users across the platform
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => router.push('/admin/users')}
              >
                <Users className="h-4 w-4 mr-2" />
                Manage Users
              </Button>
              <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Project
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Create New Project</DialogTitle>
                    <DialogDescription>
                      Create a new project and assign it to a user. The user will be able to upload builds and manage the project.
                    </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleCreateProject} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Project Name</Label>
                      <Input
                        id="name"
                        value={newProject.name}
                        onChange={(e) => setNewProject({ ...newProject, name: e.target.value })}
                        placeholder="My Awesome Game"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="stream_project_id">Stream Project ID</Label>
                      <Input
                        id="stream_project_id"
                        value={newProject.stream_project_id}
                        onChange={(e) => setNewProject({ ...newProject, stream_project_id: e.target.value })}
                        placeholder="stream_project_123"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="user_email">User Email</Label>
                      <Input
                        id="user_email"
                        type="email"
                        value={newProject.user_email}
                        onChange={(e) => setNewProject({ ...newProject, user_email: e.target.value })}
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsCreateDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button type="submit" disabled={isCreating}>
                        {isCreating ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Creating...
                          </>
                        ) : (
                          'Create Project'
                        )}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Package className="h-8 w-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Projects</p>
                  <p className="text-2xl font-bold text-gray-900">{projects.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Users</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {new Set(projects.map(p => p.profiles.email)).size}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Package className="h-8 w-8 text-purple-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Builds</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {projects.reduce((sum, p) => sum + p.builds.length, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Projects List */}
        <Card>
          <CardHeader>
            <CardTitle>All Projects</CardTitle>
            <CardDescription>
              Manage all projects across the platform
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error ? (
              <div className="text-center text-red-600 py-8">
                <p>Error loading projects: {error}</p>
                <Button
                  variant="outline"
                  onClick={fetchProjects}
                  className="mt-4"
                >
                  Retry
                </Button>
              </div>
            ) : projects.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Package className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No projects created yet</p>
                <p className="text-sm">Create your first project to get started</p>
              </div>
            ) : (
              <div className="space-y-4">
                {projects.map((project) => (
                  <div
                    key={project.id}
                    className="border rounded-lg p-6 hover:bg-gray-50"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-medium text-gray-900">
                            {project.name}
                          </h3>
                          <Badge variant="outline">
                            {project.stream_project_id}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-6 text-sm text-gray-600 mb-3">
                          <div className="flex items-center">
                            <UserIcon className="h-4 w-4 mr-1" />
                            {project.profiles.email}
                          </div>
                          <div className="flex items-center">
                            <Package className="h-4 w-4 mr-1" />
                            {project.builds.length} build{project.builds.length !== 1 ? 's' : ''}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            Created {formatDate(project.created_at)}
                          </div>
                        </div>
                        {project.builds.length > 0 && (
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <p className="text-sm font-medium text-gray-900 mb-1">
                              Latest Build: {project.builds[0].original_filename || project.builds[0].filename}
                            </p>
                            <p className="text-xs text-gray-600">
                              Version {project.builds[0].version} • {formatDate(project.builds[0].created_at)}
                            </p>
                          </div>
                        )}
                      </div>
                      {/* Action Buttons */}
                      <div className="flex flex-col space-y-2 ml-4">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => router.push(`/admin/projects/${project.id}`)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => router.push(`/admin/projects/${project.id}`)}
                        >
                          <Edit2 className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDeleteProject(project.id, project.name)}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
