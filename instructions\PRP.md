Here’s the **complete Product Requirements Plan (PRP)** for Omnipixel, in Markdown format with full detail.

---

```markdown
# 🛠 Omnipixel PRP

## 1. 🎯 Objectives

Build a branded, serverless dashboard (Supabase, Next.js, AWS SAM/S3/Lambda) enabling:

- Users to **log in** and view **only their assigned projects**
- Project details editing (name & config)
- Uploading ZIPs up to **24 GB** via S3 multipart
- Lambda-triggered **post-upload logic** (Streampixel API + retention cleanup)
- Streaming via embedded SDK (Omnipixel UI only)
- A **Platform Manager** (Super Admin) dashboard to manage users and project assignments

---

## 2. 👥 Roles & Permissions

### User
- View/edit their projects
- Upload builds
- Stream their projects

### Platform Manager
- CRUD users
- Assign/unassign projects
- CRUD projects
- Elevated access via Supabase Admin API

---

## 3. 🛠 Tech Stack

| Component       | Tech                                |
|----------------|-------------------------------------|
| Frontend       | Next.js 15 + TypeScript (App Router) |
| Styling/UI     | Tailwind CSS + shadcn/ui           |
| Auth & DB      | Supabase (Auth, Postgres, RLS)     |
| Storage & Logic| AWS S3 + Lambda (via AWS SAM)      |
| Hosting        | Vercel for frontend                |
| Infrastructure | AWS SAM (`template.yaml`)          |

---

## 4. 🛠 Data Model & Access Control

### Tables / Fields

- **profiles**: id, email, role (`user | platform_admin`)
- **projects**: id, user_id, name, stream_project_id
- **builds**: id, project_id, filename, s3_key, version, created_at

### RLS Policies

- Users can only access their own projects & builds
- Platform managers have full access

---

## 5. 🔁 User Workflows

1. **Login** → redirect to `/dashboard`
2. **Dashboard** → list of projects
3. **Project page**:
   - Edit `name` and config
   - Show up to 2 latest builds
   - Upload ZIP via presign + multipart + progress bar
4. **Upload completion** → `notify-upload` triggers Lambda:
   - Calls Streampixel Upload API
   - Deletes oldest build if >2
   - Inserts new build record in Supabase
5. **Streaming** → `/projects/[id]/stream` with Omnipixel-branded SDK

---

## 6. 🔁 Platform Manager Workflow

1. Access `/admin`
2. Manage Users:
   - List/create/edit/delete users
   - Control roles and credentials
3. Manage Projects:
   - List/create/edit/delete projects
   - Assign/unassign to users

---

## 7. ⚙️ Architecture Overview

```

\[ Next.js (Vercel) ]
• Auth UI
• Project & admin pages
• Upload page logic
• `get-presign` & `notify-upload` API routes
→ Supabase (Auth + Postgres + RLS + Admin APIs)
→ AWS SAM:
• S3 bucket (multipart, versioning, CORS)
• Lambdas:
\- `getPresignUrl`
\- `handleUploadComplete`
→ Streampixel Upload API (via Lambda)
→ S3 cleanup logic + DB inserts

```

---

## 8. 📅 Timeline & Milestones

| Phase                         | Duration     |
|------------------------------|--------------|
| Setup (Beam)                 | 2 days       |
| Auth, Dashboard, RLS        | 3 days       |
| S3 + SAM Presign Setup      | 2 days       |
| Upload Flow & Progress      | 3 days       |
| Lambda Upload Logic         | 3 days       |
| Streaming SDK Integration   | 2 days       |
| Platform Manager UI         | 3 days       |
| Testing & Security          | 2 days       |
| **Total**                   | **20 days**  |

---

## 9. 🔐 Security Considerations

- Protect admin routes via Supabase user role check
- Use least-privilege IAM roles for Lambdas (S3 + CloudWatch only)
- Admin key stored securely in Vercel
- CORS locked to production domains

---

## 10. 🎯 Success Metrics

- Onboard X users in pilot
- Y ZIP uploads per user/project over 30 days
- >95% of uploads succeed without errors
- ZIP cleanup enforces max-2 versions
- Streaming works across browsers/devices
- No trace of Streampixel branding; all Omnipixel

---

## 11. 📌 Next Steps

1. Generate SAM `template.yaml` with S3 and Lambdas
2. Set up Supabase schema + RLS + roles
3. Scaffold Next.js frontend (login, dashboard, uploads)
4. Implement upload logic & backend integrations
5. Build Platform Manager UI
6. Execute testing & CI/CD deploy
7. Launch MVP & monitor metrics

---

*Note: This PRP works as your structured foundation. Once you’re ready, we can generate/configure code scaffolds and SAM infra next!*
```
