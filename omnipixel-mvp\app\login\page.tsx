import type { ReactNode } from 'react'
import LoginForm from './LoginForm'
import { supabase } from '@/lib/supabase'
import { redirect } from 'next/navigation'

export default async function LoginPage(): Promise<ReactNode> {
  const { data } = await supabase.auth.getUser()
  if (data.user) redirect('/dashboard')

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <LoginForm />
    </div>
  )
}
