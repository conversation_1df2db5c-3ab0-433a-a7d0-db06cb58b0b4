import { createBrowserClient } from '@supabase/ssr'
import { StreamPixelConfig } from 'streampixelsdk'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabasePublishableKey = process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!

export const supabase = createBrowserClient(supabaseUrl, supabasePublishableKey)

// Types for our database tables
export interface Profile {
  id: string
  email: string
  role: 'user' | 'platform_admin'
  created_at: string
  updated_at: string
}

export interface Project {
  id: string
  user_id: string
  name: string
  stream_project_id: string
  auto_release: boolean
  config?: StreamPixelConfig
  created_at: string
  updated_at: string
}

export interface Build {
  id: string
  project_id: string
  filename: string
  original_filename?: string
  s3_key: string
  version: number
  status: 'uploading' | 'processing' | 'active' | 'archived' | 'failed' | 'inactive'
  is_current: boolean
  file_size?: number
  streampixel_build_id?: string
  streampixel_status?: string
  error_message?: string
  created_at: string
  updated_at: string
  activated_at?: string
}
