-- Add public read access for embed functionality
-- This allows non-authenticated users to view project data for embed pages

-- Add public read policy for projects table (for embed access)
CREATE POLICY "Public can view projects for embed" ON projects
    FOR SELECT USING (true);

-- Add public read policy for builds table (for embed access)  
CREATE POLICY "Public can view builds for embed" ON builds
    FOR SELECT USING (true);

-- Note: This enables public read access to all projects and builds
-- If you need more granular control, you could add a column like 'is_public' 
-- to the projects table and modify the policy to:
-- FOR SELECT USING (is_public = true);
