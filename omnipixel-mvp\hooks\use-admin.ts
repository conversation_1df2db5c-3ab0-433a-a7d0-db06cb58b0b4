'use client'

import { useState } from 'react'
import { StreamPixelConfig } from 'streampixelsdk'

export interface AdminProject {
  id: string
  name: string
  stream_project_id: string
  user_id: string
  config: StreamPixelConfig
  created_at: string
  updated_at: string
  profiles: {
    email: string
    role: string
  }
  builds: Array<{
    id: string
    filename: string
    version: number
    created_at: string
    is_current: boolean
    original_filename?: string
  }>
}

export interface CreateProjectData {
  name: string
  stream_project_id: string
  user_email: string
  config?: Record<string, unknown>
}

export function useAdmin() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createProject = async (projectData: CreateProjectData) => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/admin/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(projectData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create project')
      }

      const data = await response.json()
      return data
    } catch (err: unknown) {
      setError(err instanceof Error? err.message : 'An unknown error occurred')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const fetchAllProjects = async (): Promise<AdminProject[]> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/admin/projects')
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch projects')
      }

      const data = await response.json()
      return data.projects
    } catch (err: unknown) {
      setError(err instanceof Error? err.message : 'An unknown error occurred')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const updateProjectStreamId = async (projectId: string, streamProjectId: string) => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/admin/projects/${projectId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ stream_project_id: streamProjectId }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update project')
      }

      const data = await response.json()
      return data
    } catch (err: unknown) {
      setError(err instanceof Error? err.message : 'An unknown error occurred')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const deleteProject = async (projectId: string) => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/admin/projects/${projectId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete project')
      }

      return true
    } catch (err: unknown) {
      setError(err instanceof Error? err.message : 'An unknown error occurred')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const clearError = () => setError(null)

  return {
    createProject,
    fetchAllProjects,
    updateProjectStreamId,
    deleteProject,
    loading,
    error,
    clearError,
  }
}
