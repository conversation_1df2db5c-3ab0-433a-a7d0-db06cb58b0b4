# Database Schema Documentation v1.0

## Overview
The Omnipixel MVP uses Supabase (PostgreSQL) as the primary database with Row Level Security (RLS) policies for data protection.

## Tables

### profiles
User profile information and role management.

```sql
CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    role user_role NOT NULL DEFAULT 'user',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Fields:**
- `id`: UUID, references auth.users(id), primary key
- `email`: User's email address
- `role`: Enum ('user', 'platform_admin')
- `created_at`: Timestamp of record creation
- `updated_at`: Timestamp of last update

### projects
Project information and configuration.

```sql
CREATE TABLE projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    stream_project_id TEXT NOT NULL,
    config J<PERSON>NB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Fields:**
- `id`: UUID, auto-generated primary key
- `user_id`: References profiles(id), project owner
- `name`: Human-readable project name
- `stream_project_id`: Streampixel API project identifier
- `config`: JSON configuration object
- `created_at`: Timestamp of record creation
- `updated_at`: Timestamp of last update

### builds
Build/upload information for projects.

```sql
CREATE TABLE builds (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    filename TEXT NOT NULL,
    s3_key TEXT NOT NULL,
    version INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Fields:**
- `id`: UUID, auto-generated primary key
- `project_id`: References projects(id), parent project
- `filename`: Original filename of uploaded build
- `s3_key`: AWS S3 object key for the uploaded file
- `version`: Build version number
- `created_at`: Timestamp of upload

## Custom Types

### user_role
Enumeration for user roles.

```sql
CREATE TYPE user_role AS ENUM ('user', 'platform_admin');
```

## Indexes

Performance optimization indexes:

```sql
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_builds_project_id ON builds(project_id);
CREATE INDEX idx_builds_created_at ON builds(created_at);
```

## Row Level Security (RLS) Policies

### profiles table

**Users can view their own profile:**
```sql
CREATE POLICY "Users can view their own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);
```

**Users can update their own profile:**
```sql
CREATE POLICY "Users can update their own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);
```

**Platform admins can view all profiles:**
```sql
CREATE POLICY "Platform admins can view all profiles" ON profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'platform_admin'
        )
    );
```

### projects table

**Users can view their own projects:**
```sql
CREATE POLICY "Users can view their own projects" ON projects
    FOR SELECT USING (auth.uid() = user_id);
```

**Platform admins can view all projects:**
```sql
CREATE POLICY "Platform admins can view all projects" ON projects
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'platform_admin'
        )
    );
```

### builds table

**Users can view builds for their projects:**
```sql
CREATE POLICY "Users can view builds for their projects" ON builds
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE id = builds.project_id AND user_id = auth.uid()
        )
    );
```

## Database Functions

### handle_new_user()
Automatically creates a profile when a user signs up.

```sql
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, role)
    VALUES (NEW.id, NEW.email, 'user');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### update_updated_at_column()
Updates the updated_at timestamp on record changes.

```sql
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## Triggers

### on_auth_user_created
Creates profile on user signup:
```sql
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

### update_profiles_updated_at
Updates timestamp on profile changes:
```sql
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
```

## Migration Files
- `supabase/migrations/20240101000000_initial_schema.sql`: Initial schema setup
- `supabase/seed.sql`: Sample data for development

## Security Considerations
- All tables have RLS enabled
- Users can only access their own data unless they're platform admins
- Platform admins have full access to all data
- Service role bypasses RLS for backend operations
- Automatic profile creation prevents orphaned auth users
