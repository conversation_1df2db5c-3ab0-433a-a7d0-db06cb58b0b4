-- Update build management to enforce 1 active + 1 archived build per project
-- Users must manually delete builds to upload new ones

-- Drop existing triggers
DROP TRIGGER IF EXISTS trigger_enforce_build_limit ON builds;
DROP TRIGGER IF EXISTS trigger_set_current_build ON builds;
DROP TRIGGER IF EXISTS trigger_set_first_build_current ON builds;

-- Drop existing functions
DROP FUNCTION IF EXISTS enforce_build_limit();
DROP FUNCTION IF EXISTS set_current_build();
DROP FUNCTION IF EXISTS set_first_build_current();

-- Create new function to enforce 1 active + 1 archived build limit
CREATE OR REPLACE FUNCTION enforce_corrected_build_limit()
RETURNS TRIGGER AS $$
DECLARE
    active_count INTEGER;
    archived_count INTEGER;
    total_count INTEGER;
BEGIN
    -- Count current builds for this project (excluding failed builds)
    SELECT 
        COUNT(*) FILTER (WHERE status = 'active' AND is_current = true),
        COUNT(*) FILTER (WHERE status = 'archived' AND is_current = false),
        COUNT(*)
    INTO active_count, archived_count, total_count
    FROM builds 
    WHERE project_id = NEW.project_id 
    AND status NOT IN ('failed', 'uploading');
    
    -- If we already have 1 active + 1 archived build, prevent new uploads
    IF total_count >= 2 THEN
        RAISE EXCEPTION 'Project already has maximum builds (1 active + 1 archived). Please delete a build before uploading a new one.';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to enforce corrected build limit
CREATE TRIGGER trigger_enforce_corrected_build_limit
    BEFORE INSERT ON builds
    FOR EACH ROW
    EXECUTE FUNCTION enforce_corrected_build_limit();

-- Create function to manage current build status
CREATE OR REPLACE FUNCTION manage_current_build()
RETURNS TRIGGER AS $$
BEGIN
    -- If this build is being set as current
    IF NEW.is_current = true AND (OLD IS NULL OR OLD.is_current = false) THEN
        -- Unset all other builds in this project as current
        UPDATE builds 
        SET is_current = false, status = 'archived', updated_at = NOW()
        WHERE project_id = NEW.project_id 
        AND id != NEW.id 
        AND is_current = true;
        
        -- Set this build as active
        NEW.status = 'active';
    END IF;
    
    -- If this build is being set as not current
    IF NEW.is_current = false AND OLD.is_current = true THEN
        NEW.status = 'archived';
    END IF;
    
    -- Update the updated_at timestamp
    NEW.updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to manage current build status
CREATE TRIGGER trigger_manage_current_build
    BEFORE UPDATE ON builds
    FOR EACH ROW
    EXECUTE FUNCTION manage_current_build();

-- Create function to set first build as current automatically
CREATE OR REPLACE FUNCTION set_first_build_current()
RETURNS TRIGGER AS $$
DECLARE
    current_build_count INTEGER;
BEGIN
    -- Count current builds for this project
    SELECT COUNT(*) INTO current_build_count
    FROM builds 
    WHERE project_id = NEW.project_id 
    AND is_current = true
    AND status NOT IN ('failed', 'uploading');
    
    -- If no current build exists, make this one current
    IF current_build_count = 0 THEN
        NEW.is_current = true;
        NEW.status = 'active';
    ELSE
        -- Otherwise, make it archived
        NEW.is_current = false;
        NEW.status = 'archived';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to set first build as current
CREATE TRIGGER trigger_set_first_build_current
    BEFORE INSERT ON builds
    FOR EACH ROW
    EXECUTE FUNCTION set_first_build_current();

-- Add function to clean up S3 files when builds are deleted
CREATE OR REPLACE FUNCTION cleanup_build_s3_file()
RETURNS TRIGGER AS $$
BEGIN
    -- Note: The actual S3 deletion will be handled by the API
    -- This function is for future extensibility
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for S3 cleanup (placeholder for API integration)
CREATE TRIGGER trigger_cleanup_build_s3_file
    AFTER DELETE ON builds
    FOR EACH ROW
    EXECUTE FUNCTION cleanup_build_s3_file();

-- Update existing builds to follow new rules
-- Set the latest build as active, others as archived
WITH ranked_builds AS (
    SELECT 
        id,
        project_id,
        ROW_NUMBER() OVER (PARTITION BY project_id ORDER BY version DESC) as rn
    FROM builds
    WHERE status NOT IN ('failed')
)
UPDATE builds 
SET 
    is_current = CASE WHEN rb.rn = 1 THEN true ELSE false END,
    status = CASE WHEN rb.rn = 1 THEN 'active' ELSE 'archived' END,
    updated_at = NOW()
FROM ranked_builds rb
WHERE builds.id = rb.id;

-- Add constraint to ensure only ZIP files are allowed
ALTER TABLE builds ADD CONSTRAINT check_zip_file 
CHECK (filename ILIKE '%.zip');

-- Add index for efficient build limit checking
CREATE INDEX IF NOT EXISTS idx_builds_project_status_current 
ON builds(project_id, status, is_current);

-- Update RLS policies to allow users to delete their own builds
CREATE POLICY "Users can delete builds for their projects" ON builds
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = builds.project_id 
            AND projects.user_id = auth.uid()
        )
    );
