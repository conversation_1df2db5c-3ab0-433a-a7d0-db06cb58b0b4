# Migration Guide - Package.json Script Cleanup

## 📋 Overview

We've simplified the npm scripts in package.json to reduce redundancy while maintaining full functionality. The new scripts are cross-platform and easier to use.

## 🔄 Script Changes

### ✅ **Kept (Core Scripts)**
These scripts remain unchanged and are used by GitHub Actions:

```json
{
  "dev": "next dev --turbopack",
  "build": "next build", 
  "start": "next start",
  "lint": "next lint",
  "test": "jest",
  "test:watch": "jest --watch",
  "test:coverage": "jest --coverage",
  "test:e2e": "playwright test",
  "test:e2e:ui": "playwright test --ui",
  "test:all": "npm run test && npm run test:e2e"
}
```

### ✅ **Simplified (Deployment Scripts)**
Consolidated to essential cross-platform scripts:

```json
{
  "setup:env": "node scripts/setup-env.js",
  "setup:aws": "node scripts/setup-aws.js",
  "deploy:aws": "node scripts/deploy-aws.js",
  "deploy:aws:staging": "node scripts/deploy-aws.js -e staging",
  "deploy:aws:prod": "node scripts/deploy-aws.js -e prod",
  "deploy:aws:help": "node scripts/deploy-aws.js -h"
}
```

### ❌ **Removed (Redundant Scripts)**
These platform-specific scripts were removed but functionality is preserved:

```json
{
  "setup:db": "echo 'Please run Supabase migrations manually for now'",
  "setup:aws:legacy": "cd aws && ./setup.sh",
  "setup:aws:windows": "cd aws && setup.bat", 
  "setup:aws:powershell": "cd aws && powershell -ExecutionPolicy Bypass -File setup.ps1",
  "deploy:aws:legacy": "cd aws && ./deploy.sh",
  "deploy:aws:windows": "cd aws && deploy.bat",
  "deploy:aws:powershell": "cd aws && powershell -ExecutionPolicy Bypass -File deploy.ps1",
  "deploy:aws:staging:legacy": "cd aws && ./deploy.sh -e staging",
  "deploy:aws:prod:legacy": "cd aws && ./deploy.sh -e prod",
  "deploy:aws:staging:windows": "cd aws && deploy.bat -e staging",
  "deploy:aws:prod:windows": "cd aws && deploy.bat -e prod",
  "deploy:aws:staging:powershell": "cd aws && powershell -ExecutionPolicy Bypass -File deploy.ps1 -Environment staging",
  "deploy:aws:prod:powershell": "cd aws && powershell -ExecutionPolicy Bypass -File deploy.ps1 -Environment prod",
  "test:deploy": "node scripts/test-error-handling.js"
}
```

## 🔄 Migration Instructions

### **If you were using old scripts:**

#### **Old Way → New Way**

```bash
# Setup
npm run setup:aws:windows     → npm run setup:aws
npm run setup:aws:powershell  → npm run setup:aws
npm run setup:aws:legacy      → npm run setup:aws

# Basic deployment
npm run deploy:aws:windows     → npm run deploy:aws
npm run deploy:aws:powershell  → npm run deploy:aws
npm run deploy:aws:legacy      → npm run deploy:aws

# Environment-specific deployment
npm run deploy:aws:staging:windows     → npm run deploy:aws:staging
npm run deploy:aws:staging:powershell  → npm run deploy:aws:staging
npm run deploy:aws:staging:legacy      → npm run deploy:aws:staging

npm run deploy:aws:prod:windows        → npm run deploy:aws:prod
npm run deploy:aws:prod:powershell     → npm run deploy:aws:prod
npm run deploy:aws:prod:legacy         → npm run deploy:aws:prod
```

### **If you need platform-specific scripts:**

The original bash, batch, and PowerShell scripts are still available in the `aws/` directory:

```bash
# Linux/macOS/WSL
cd aws && ./setup.sh && ./deploy.sh

# Windows Command Prompt
cd aws && setup.bat && deploy.bat

# Windows PowerShell
cd aws && .\setup.ps1; .\deploy.ps1
```

## 🎯 Benefits of the New Approach

### **1. Simplified Commands**
- **Before**: 15+ deployment scripts for different platforms
- **After**: 6 essential scripts that work everywhere

### **2. Cross-Platform Compatibility**
- **Single command** works on Windows, macOS, and Linux
- **No need to remember** platform-specific script names
- **Consistent behavior** across all environments

### **3. Better Maintainability**
- **Fewer scripts** to maintain and document
- **Single source of truth** for deployment logic
- **Easier to add new features** without platform variations

### **4. Enhanced Functionality**
- **AWS profile support**: `npm run deploy:aws -- -p my-profile`
- **Better error handling** with helpful messages
- **Real-time progress** updates
- **Comprehensive help**: `npm run deploy:aws:help`

## 🔧 Advanced Usage

### **AWS Profiles (Multiple Accounts)**
```bash
# Deploy with specific AWS profile
npm run deploy:aws -- -p dev-account
npm run deploy:aws:prod -- -p production-account
```

### **Custom Regions and Stack Names**
```bash
# Deploy to different region
npm run deploy:aws -- -e prod -r eu-west-1

# Custom stack name
npm run deploy:aws -- -e dev -s my-custom-stack

# Combined options
npm run deploy:aws -- -e prod -p prod-profile -r eu-west-1 -s custom-stack
```

### **Help and Documentation**
```bash
# Show all available options
npm run deploy:aws:help

# Show help for specific command
npm run deploy:aws -- -h
```

## 🚨 Breaking Changes

### **None for Core Functionality**
- ✅ **GitHub Actions**: No changes needed
- ✅ **CI/CD pipelines**: Continue to work
- ✅ **Core deployment**: Same functionality
- ✅ **Environment variables**: Same behavior

### **Only Affects Custom Scripts**
If you have custom scripts or documentation that reference the removed npm scripts, update them to use the new simplified commands.

## 🔍 Verification

### **Test the Migration**
```bash
# Verify core scripts work
npm run lint
npm run test
npm run build

# Verify deployment scripts work
npm run deploy:aws:help
npm run setup:aws

# Test with your AWS profile
npm run deploy:aws -- -p your-profile -h
```

### **Check GitHub Actions**
The GitHub Actions workflow continues to use:
- `npm run lint`
- `npm run test:coverage`
- `npm run test:e2e`
- `npm run build`

These scripts are unchanged and will continue to work.

## 📚 Updated Documentation

The following documentation has been updated to reflect the new scripts:

- **README.md**: Updated script examples
- **aws/README.md**: Simplified deployment instructions
- **docs/deployment.md**: Cross-platform deployment guide
- **docs/aws-profiles.md**: AWS profile usage examples

## 🆘 Need Help?

### **If something doesn't work:**

1. **Check the new script names** in this migration guide
2. **Use the help command**: `npm run deploy:aws:help`
3. **Fall back to direct scripts** if needed:
   ```bash
   cd aws && ./deploy.sh -h  # Linux/macOS
   cd aws && deploy.bat -h   # Windows
   ```
4. **Check the documentation** for updated examples

### **Common Issues:**

#### **"Script not found" error**
```bash
npm ERR! missing script: deploy:aws:windows
```
**Solution**: Use `npm run deploy:aws` instead

#### **"Command not recognized" error**
**Solution**: The new scripts work on all platforms, use the simplified commands

---

**The migration maintains all functionality while providing a cleaner, more maintainable script structure! 🚀**
