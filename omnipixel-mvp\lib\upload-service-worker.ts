'use client'

// Service Worker Manager for Background Uploads
export class UploadServiceWorker {
  private registration: ServiceWorkerRegistration | null = null
  private messageHandlers = new Map<string, (data: any) => void>()

  async initialize(): Promise<boolean> {
    // Service worker disabled - Uppy handles background uploads natively
    console.log('Upload Service Worker disabled - using Uppy native background support')

    // Unregister any existing service workers that might interfere
    if (typeof navigator !== 'undefined' && 'serviceWorker' in navigator) {
      try {
        const registrations = await navigator.serviceWorker.getRegistrations()
        for (const registration of registrations) {
          if (registration.scope.includes('upload-worker')) {
            await registration.unregister()
            console.log('Unregistered interfering upload service worker')
          }
        }
      } catch (error) {
        console.warn('Failed to unregister service workers:', error)
      }
    }

    return false
  }

  async startBackgroundUpload(uploadData: {
    uploadId: string
    file: File
    projectId: string
    s3Key?: string
    totalParts: number
    partSize: number
  }): Promise<void> {
    if (!this.registration?.active) {
      throw new Error('Service worker not ready')
    }

    this.registration.active.postMessage({
      type: 'START_UPLOAD',
      data: uploadData
    })
  }

  async cancelUpload(uploadId: string): Promise<void> {
    if (!this.registration?.active) {
      return
    }

    this.registration.active.postMessage({
      type: 'CANCEL_UPLOAD',
      data: { uploadId }
    })
  }

  async getUploadStatus(uploadId: string): Promise<any> {
    if (!this.registration?.active) {
      return { status: 'service_worker_not_ready' }
    }

    return new Promise((resolve) => {
      const handler = (data: any) => {
        this.messageHandlers.delete(`UPLOAD_STATUS_${uploadId}`)
        resolve(data.status)
      }

      this.messageHandlers.set(`UPLOAD_STATUS_${uploadId}`, handler)

      this.registration!.active!.postMessage({
        type: 'GET_UPLOAD_STATUS',
        data: { uploadId }
      })

      // Timeout after 5 seconds
      setTimeout(() => {
        this.messageHandlers.delete(`UPLOAD_STATUS_${uploadId}`)
        resolve({ status: 'timeout' })
      }, 5000)
    })
  }

  onUploadProgress(uploadId: string, callback: (progress: any) => void): () => void {
    const handler = (data: any) => callback(data)
    this.messageHandlers.set(`UPLOAD_PROGRESS_${uploadId}`, handler)

    return () => {
      this.messageHandlers.delete(`UPLOAD_PROGRESS_${uploadId}`)
    }
  }

  onUploadComplete(uploadId: string, callback: (result: any) => void): () => void {
    const handler = (data: any) => callback(data)
    this.messageHandlers.set(`UPLOAD_COMPLETE_${uploadId}`, handler)

    return () => {
      this.messageHandlers.delete(`UPLOAD_COMPLETE_${uploadId}`)
    }
  }

  onUploadError(uploadId: string, callback: (error: string) => void): () => void {
    const handler = (data: any) => callback(data.error)
    this.messageHandlers.set(`UPLOAD_ERROR_${uploadId}`, handler)

    return () => {
      this.messageHandlers.delete(`UPLOAD_ERROR_${uploadId}`)
    }
  }

  isSupported(): boolean {
    return typeof navigator !== 'undefined' && 'serviceWorker' in navigator
  }

  isReady(): boolean {
    return !!this.registration?.active
  }
}

// Singleton instance - only create on client side
export const uploadServiceWorker = typeof window !== 'undefined' ? new UploadServiceWorker() : null

// Page Visibility API integration
export class BackgroundUploadManager {
  private isTabVisible = true
  private uploadServiceWorker: UploadServiceWorker
  private activeUploads = new Set<string>()

  constructor(serviceWorker: UploadServiceWorker) {
    this.uploadServiceWorker = serviceWorker
    // Only setup visibility handling on client side
    if (typeof window !== 'undefined') {
      this.setupVisibilityHandling()
    }
  }

  private setupVisibilityHandling() {
    // Only run on client side
    if (typeof document === 'undefined' || typeof window === 'undefined') {
      return
    }

    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
      this.isTabVisible = !document.hidden

      if (document.hidden && this.activeUploads.size > 0) {
        console.log('Tab hidden - uploads transferred to service worker')
        this.showBackgroundUploadNotification()
      } else if (!document.hidden) {
        console.log('Tab visible - checking upload status')
        this.checkActiveUploads()
      }
    })

    // Handle page unload
    window.addEventListener('beforeunload', (event) => {
      if (this.activeUploads.size > 0) {
        event.preventDefault()
        event.returnValue = 'Uploads are in progress. Are you sure you want to leave?'
        return event.returnValue
      }
    })
  }

  async transferToBackground(uploadId: string, uploadData: any): Promise<boolean> {
    if (!this.uploadServiceWorker.isReady()) {
      console.warn('Service worker not ready, cannot transfer upload to background')
      return false
    }

    try {
      await this.uploadServiceWorker.startBackgroundUpload({
        uploadId,
        ...uploadData
      })
      
      this.activeUploads.add(uploadId)
      return true
    } catch (error) {
      console.error('Failed to transfer upload to background:', error)
      return false
    }
  }

  removeActiveUpload(uploadId: string) {
    this.activeUploads.delete(uploadId)
  }

  private showBackgroundUploadNotification() {
    // Only show notifications on client side
    if (typeof window === 'undefined') return

    // Show a toast or notification that uploads continue in background
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('OmniPixel Upload', {
        body: 'Your file upload continues in the background',
        icon: '/favicon.ico'
      })
    }
  }

  private async checkActiveUploads() {
    // Check status of active uploads when tab becomes visible
    for (const uploadId of this.activeUploads) {
      const status = await this.uploadServiceWorker.getUploadStatus(uploadId)
      console.log(`Upload ${uploadId} status:`, status)
      
      if (status.status === 'completed' || status.status === 'failed') {
        this.activeUploads.delete(uploadId)
      }
    }
  }

  async requestNotificationPermission(): Promise<boolean> {
    // Only request permissions on client side
    if (typeof window === 'undefined' || !('Notification' in window)) {
      return false
    }

    if (Notification.permission === 'granted') {
      return true
    }

    if (Notification.permission === 'denied') {
      return false
    }

    const permission = await Notification.requestPermission()
    return permission === 'granted'
  }

  getActiveUploadCount(): number {
    return this.activeUploads.size
  }

  isTabVisible(): boolean {
    return this.isTabVisible
  }
}

// Export singleton - only create on client side
export const backgroundUploadManager = typeof window !== 'undefined' && uploadServiceWorker
  ? new BackgroundUploadManager(uploadServiceWorker)
  : null
