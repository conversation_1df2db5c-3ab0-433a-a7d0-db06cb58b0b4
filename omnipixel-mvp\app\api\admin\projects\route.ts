import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'
import { Build } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient()

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    console.log('Admin API - user:', user?.email)

    if (authError || !user) {
      console.log('Admin API - No session or auth error')
      return NextResponse.json(
        { error: 'Unauthorized', details: authError?.message || 'No session' },
        { status: 401 }
      )
    }

    // Check if user is platform admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    console.log('Admin API - profile:', profile, 'profileError:', profileError?.message)

    if (profileError || !profile || profile.role !== 'platform_admin') {
      console.log('Admin API - Access denied, not admin')
      return NextResponse.json(
        { error: 'Access denied. Platform admin role required.', details: profileError?.message },
        { status: 403 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { name, stream_project_id, user_email, config = {} } = body

    // Validate required fields
    if (!name || !stream_project_id || !user_email) {
      return NextResponse.json(
        { error: 'Missing required fields: name, stream_project_id, user_email' },
        { status: 400 }
      )
    }

    // Find the target user by email
    const { data: targetProfile, error: targetError } = await supabase
      .from('profiles')
      .select('id, email')
      .eq('email', user_email)
      .single()

    if (targetError || !targetProfile) {
      return NextResponse.json(
        { error: 'User not found with the provided email' },
        { status: 404 }
      )
    }

    // Check if stream_project_id is already in use
    const { data: existingProject, error: checkError } = await supabase
      .from('projects')
      .select('id')
      .eq('stream_project_id', stream_project_id)
      .single()

    if (existingProject) {
      return NextResponse.json(
        { error: 'Stream Project ID is already in use' },
        { status: 409 }
      )
    }

    // Create the project
    const { data: project, error: createError } = await supabase
      .from('projects')
      .insert([
        {
          name,
          stream_project_id,
          user_id: targetProfile.id,
          config,
        }
      ])
      .select(`
        *,
        profiles!inner(email, role)
      `)
      .single()

    if (createError) {
      console.error('Error creating project:', createError)
      return NextResponse.json(
        { error: 'Failed to create project' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      project,
      message: `Project "${name}" created successfully for ${user_email}`,
    })

  } catch (error) {
    console.error('Error in admin project creation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient()

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    console.log('Admin GET API - user:', user?.email)

    if (authError || !user) {
      console.log('Admin GET API - No session or auth error')
      return NextResponse.json(
        { error: 'Unauthorized', details: authError?.message || 'No session' },
        { status: 401 }
      )
    }

    // Check if user is platform admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    console.log('Admin GET API - profile:', profile, 'profileError:', profileError?.message)

    if (profileError || !profile || profile.role !== 'platform_admin') {
      console.log('Admin GET API - Access denied, not admin')
      return NextResponse.json(
        { error: 'Access denied. Platform admin role required.', details: profileError?.message },
        { status: 403 }
      )
    }

    // Get all projects with user information
    const { data: projects, error: fetchError } = await supabase
      .from('projects')
      .select(`
        *,
        profiles!inner(email, role),
        builds(id, filename, original_filename, version, status, is_current, file_size, streampixel_build_id, streampixel_status, error_message, created_at, updated_at)
      `)
      .order('created_at', { ascending: false })

    if (fetchError) {
      console.error('Error fetching projects:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch projects' },
        { status: 500 }
      )
    }

    // Sort builds by version descending for each project
    const projectsWithSortedBuilds = projects?.map(project => ({
      ...project,
      builds: project.builds
        ?.sort((a:Build, b:Build) => b.version - a.version)
        .slice(0, 5) || [] // Show only latest 5 builds
    })) || []

    return NextResponse.json({
      projects: projectsWithSortedBuilds,
    })

  } catch (error) {
    console.error('Error in admin projects fetch:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH - Update project (admin only)
export async function PATCH(request: NextRequest) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is platform admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'platform_admin') {
      return NextResponse.json(
        { error: 'Access denied. Platform admin role required.' },
        { status: 403 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { project_id, name, stream_project_id, config, user_email } = body

    // Validate required fields
    if (!project_id) {
      return NextResponse.json(
        { error: 'Missing required field: project_id' },
        { status: 400 }
      )
    }

    interface ProjectUpdateData {
      name?: string;
      stream_project_id?: string;
      config?: Record<string, unknown>;
      user_id?: string;
    }

    // Prepare update data
    const updateData: ProjectUpdateData = {}
    if (name) updateData.name = name
    if (stream_project_id) updateData.stream_project_id = stream_project_id
    if (config !== undefined) updateData.config = config

    // If user_email is provided, find the user and update user_id
    if (user_email) {
      const { data: targetProfile, error: targetProfileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('email', user_email)
        .single()

      if (targetProfileError || !targetProfile) {
        return NextResponse.json(
          { error: 'User not found with the provided email' },
          { status: 400 }
        )
      }

      updateData.user_id = targetProfile.id
    }

    // Check if stream_project_id is already in use (if being updated)
    if (stream_project_id) {
      const { data: existingProject } = await supabase
        .from('projects')
        .select('id')
        .eq('stream_project_id', stream_project_id)
        .neq('id', project_id)
        .single()

      if (existingProject) {
        return NextResponse.json(
          { error: 'Stream Project ID is already in use by another project' },
          { status: 400 }
        )
      }
    }

    // Update the project
    const { data: updatedProject, error: updateError } = await supabase
      .from('projects')
      .update(updateData)
      .eq('id', project_id)
      .select(`
        *,
        profiles!projects_user_id_fkey (
          email,
          role
        )
      `)
      .single()

    if (updateError) {
      console.error('Error updating project:', updateError)
      return NextResponse.json(
        { error: 'Failed to update project' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Project updated successfully',
      project: updatedProject
    })

  } catch (error: unknown) {
    console.error('Error updating project:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE - Delete project (admin only)
export async function DELETE(request: NextRequest) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is platform admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'platform_admin') {
      return NextResponse.json(
        { error: 'Access denied. Platform admin role required.' },
        { status: 403 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { project_id } = body

    // Validate required fields
    if (!project_id) {
      return NextResponse.json(
        { error: 'Missing required field: project_id' },
        { status: 400 }
      )
    }

    // First, delete all builds associated with the project
    const { error: buildsDeleteError } = await supabase
      .from('builds')
      .delete()
      .eq('project_id', project_id)

    if (buildsDeleteError) {
      console.error('Error deleting builds:', buildsDeleteError)
      return NextResponse.json(
        { error: 'Failed to delete project builds' },
        { status: 500 }
      )
    }

    // Then delete the project
    const { error: projectDeleteError } = await supabase
      .from('projects')
      .delete()
      .eq('id', project_id)

    if (projectDeleteError) {
      console.error('Error deleting project:', projectDeleteError)
      return NextResponse.json(
        { error: 'Failed to delete project' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Project deleted successfully'
    })

  } catch (error: unknown) {
    console.error('Error deleting project:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
