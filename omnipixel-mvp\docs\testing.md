# Testing Guide

This document outlines the testing strategy and setup for the OmniPixel MVP application.

## Testing Stack

### Unit & Integration Tests
- **Jest**: JavaScript testing framework
- **React Testing Library**: Testing utilities for React components
- **@testing-library/jest-dom**: Custom Jest matchers for DOM elements

### End-to-End Tests
- **Playwright**: Cross-browser testing framework
- **Multiple browsers**: Chrome, Firefox, Safari, Mobile

## Test Structure

```
omnipixel-mvp/
├── __tests__/                 # Unit and integration tests
│   ├── components/            # Component tests
│   ├── hooks/                 # Custom hook tests
│   ├── api/                   # API route tests
│   └── lib/                   # Utility function tests
├── e2e/                       # End-to-end tests
│   ├── auth.spec.ts          # Authentication flows
│   ├── dashboard.spec.ts     # Dashboard functionality
│   ├── upload.spec.ts        # File upload workflows
│   └── admin.spec.ts         # Admin panel tests
├── jest.config.js            # Jest configuration
├── jest.setup.js             # Test setup and mocks
└── playwright.config.ts      # Playwright configuration
```

## Running Tests

### Unit Tests
```bash
# Run all unit tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### End-to-End Tests
```bash
# Run all E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Run all tests (unit + E2E)
npm run test:all
```

## Test Coverage Goals

- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

## Key Test Areas

### 1. Authentication & Authorization
- ✅ Login/logout flows
- ✅ User registration
- ✅ Role-based access control
- ✅ Session management
- ✅ Protected routes

### 2. Project Management
- ✅ Project creation
- ✅ Project listing
- ✅ Project editing
- ✅ Project deletion (admin)
- ✅ User permissions

### 3. Build Management
- ✅ File upload (single & multipart)
- ✅ Build versioning
- ✅ Build activation
- ✅ Build deletion
- ✅ Build history

### 4. Admin Features
- ✅ User management
- ✅ Role assignment
- ✅ Project oversight
- ✅ System administration

### 5. Streaming Integration
- ✅ StreamPixel API integration
- ✅ Stream player component
- ✅ Build deployment
- ✅ Error handling

## Mock Strategy

### API Mocks
- Supabase client methods
- AWS S3 operations
- StreamPixel API calls
- Next.js router functions

### Data Mocks
- User profiles and sessions
- Project and build data
- File upload responses
- Error scenarios

## Test Utilities

### Custom Render Function
```typescript
// __tests__/utils/test-utils.tsx
import { render } from '@testing-library/react'
import { AuthProvider } from '@/lib/auth-context'

export function renderWithAuth(ui: React.ReactElement, options = {}) {
  return render(ui, {
    wrapper: ({ children }) => (
      <AuthProvider>{children}</AuthProvider>
    ),
    ...options,
  })
}
```

### Mock Data Factories
```typescript
// __tests__/utils/mock-data.ts
export const createMockUser = (overrides = {}) => ({
  id: 'user-1',
  email: '<EMAIL>',
  role: 'user',
  ...overrides,
})

export const createMockProject = (overrides = {}) => ({
  id: 'project-1',
  name: 'Test Project',
  user_id: 'user-1',
  builds: [],
  ...overrides,
})
```

## Continuous Integration

### GitHub Actions Workflow
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:coverage
      - run: npx playwright install
      - run: npm run test:e2e
```

## Test Best Practices

### 1. Test Structure
- **Arrange**: Set up test data and mocks
- **Act**: Execute the functionality being tested
- **Assert**: Verify the expected outcomes

### 2. Component Testing
- Test user interactions, not implementation details
- Use semantic queries (getByRole, getByLabelText)
- Test accessibility features
- Mock external dependencies

### 3. API Testing
- Test both success and error scenarios
- Validate request/response formats
- Test authentication and authorization
- Mock database operations

### 4. E2E Testing
- Test critical user journeys
- Use page object models for complex flows
- Test across different browsers and devices
- Include visual regression testing

## Debugging Tests

### Jest Debugging
```bash
# Run specific test file
npm test -- build-history.test.tsx

# Run tests matching pattern
npm test -- --testNamePattern="upload"

# Debug with Node inspector
node --inspect-brk node_modules/.bin/jest --runInBand
```

### Playwright Debugging
```bash
# Run with headed browser
npx playwright test --headed

# Debug specific test
npx playwright test auth.spec.ts --debug

# Generate test code
npx playwright codegen localhost:3000
```

## Performance Testing

### Load Testing
- File upload performance with large files
- Concurrent user sessions
- Database query optimization
- API response times

### Memory Testing
- Memory leaks in React components
- File upload memory usage
- Stream player resource management

## Security Testing

### Authentication Testing
- Session hijacking prevention
- CSRF protection
- XSS vulnerability testing
- SQL injection prevention

### Authorization Testing
- Role-based access control
- Data isolation between users
- Admin privilege escalation
- API endpoint security

## Monitoring & Reporting

### Test Reports
- Jest HTML coverage reports
- Playwright HTML test reports
- CI/CD integration reports
- Performance benchmarks

### Metrics Tracking
- Test execution time
- Coverage trends
- Flaky test identification
- Bug detection rates

## Future Enhancements

### Planned Improvements
- Visual regression testing
- Accessibility testing automation
- Performance monitoring integration
- Chaos engineering tests
- Mobile app testing (when applicable)

### Tools to Consider
- Storybook for component testing
- Cypress for additional E2E testing
- Jest-axe for accessibility testing
- Lighthouse CI for performance testing
