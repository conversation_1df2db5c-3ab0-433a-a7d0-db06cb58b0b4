import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { EnhancedConfigEditor } from '@/components/enhanced-config-editor'

// Mock fetch
const mockFetch = jest.fn()
global.fetch = mockFetch

describe('EnhancedConfigEditor Component', () => {
  const defaultProps = {
    projectId: 'project-1',
    currentConfig: {
      autoConnect: false,
      touchInput: true,
      keyBoardInput: true,
      resolutionMode: 'Dynamic Resolution Mode',
      maxStreamQuality: '1080p (1920x1080)',
      primaryCodec: 'H264',
      fallBackCodec: 'VP8',
      isPasswordProtected: false,
      password: '',
      loadingMessage: 'Loading stream...',
      connectingMessage: 'Connecting to stream...',
      disconnectedMessage: 'Stream disconnected',
      reconnectingMessage: 'Reconnecting...',
      errorMessage: 'Stream error occurred',
      connectButtonText: 'Connect to Stream'
    },
    onConfigUpdate: jest.fn(),
    isAdmin: false
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockFetch.mockClear()
  })

  it('renders configuration tabs correctly', () => {
    render(<EnhancedConfigEditor {...defaultProps} />)
    
    expect(screen.getByText('Stream Configuration')).toBeInTheDocument()
    expect(screen.getByText('Stream Settings')).toBeInTheDocument()
    expect(screen.getByText('Security')).toBeInTheDocument()
    expect(screen.getByText('Messages')).toBeInTheDocument()
  })

  it('displays stream settings correctly', () => {
    render(<EnhancedConfigEditor {...defaultProps} />)
    
    // Check switches
    expect(screen.getByLabelText('Auto Connect')).toBeInTheDocument()
    expect(screen.getByLabelText('Touch Input')).toBeInTheDocument()
    expect(screen.getByLabelText('Keyboard Input')).toBeInTheDocument()
    
    // Check selects
    expect(screen.getByDisplayValue('Dynamic Resolution Mode')).toBeInTheDocument()
    expect(screen.getByDisplayValue('1080p (1920x1080)')).toBeInTheDocument()
    expect(screen.getByDisplayValue('H264')).toBeInTheDocument()
  })

  it('toggles auto connect setting', async () => {
    render(<EnhancedConfigEditor {...defaultProps} />)
    
    const autoConnectSwitch = screen.getByLabelText('Auto Connect')
    expect(autoConnectSwitch).not.toBeChecked()
    
    fireEvent.click(autoConnectSwitch)
    expect(autoConnectSwitch).toBeChecked()
  })

  it('shows password field when password protection is enabled', async () => {
    render(<EnhancedConfigEditor {...defaultProps} />)
    
    // Switch to security tab
    fireEvent.click(screen.getByText('Security'))
    
    // Password field should not be visible initially
    expect(screen.queryByLabelText('Stream Password')).not.toBeInTheDocument()
    
    // Enable password protection
    const passwordProtectionSwitch = screen.getByLabelText('Password Protection')
    fireEvent.click(passwordProtectionSwitch)
    
    // Password field should now be visible
    await waitFor(() => {
      expect(screen.getByLabelText('Stream Password')).toBeInTheDocument()
    })
  })

  it('allows editing custom messages', () => {
    render(<EnhancedConfigEditor {...defaultProps} />)
    
    // Switch to messages tab
    fireEvent.click(screen.getByText('Messages'))
    
    // Check message inputs
    expect(screen.getByDisplayValue('Loading stream...')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Connecting to stream...')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Stream disconnected')).toBeInTheDocument()
    
    // Edit a message
    const loadingInput = screen.getByDisplayValue('Loading stream...')
    fireEvent.change(loadingInput, { target: { value: 'Custom loading message' } })
    expect(screen.getByDisplayValue('Custom loading message')).toBeInTheDocument()
  })

  it('saves configuration successfully', async () => {
    const onConfigUpdate = jest.fn()
    const props = { ...defaultProps, onConfigUpdate }
    
    // Mock successful API response
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({ success: true })
    })
    
    // Mock window.alert
    window.alert = jest.fn()
    
    render(<EnhancedConfigEditor {...props} />)
    
    // Click save button
    const saveButton = screen.getByText('Save Configuration')
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/projects/project-1', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          config: defaultProps.currentConfig
        }),
      })
    })
    
    expect(window.alert).toHaveBeenCalledWith('Configuration saved successfully!')
    expect(onConfigUpdate).toHaveBeenCalledWith(defaultProps.currentConfig)
  })

  it('handles save errors correctly', async () => {
    // Mock failed API response
    mockFetch.mockResolvedValueOnce({
      ok: false,
      json: () => Promise.resolve({ error: 'Save failed' })
    })
    
    render(<EnhancedConfigEditor {...defaultProps} />)
    
    // Click save button
    const saveButton = screen.getByText('Save Configuration')
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(screen.getByText('Save failed')).toBeInTheDocument()
    })
  })

  it('resets configuration to original values', () => {
    render(<EnhancedConfigEditor {...defaultProps} />)
    
    // Change a value
    const loadingInput = screen.getByDisplayValue('Loading stream...')
    fireEvent.change(loadingInput, { target: { value: 'Modified message' } })
    expect(screen.getByDisplayValue('Modified message')).toBeInTheDocument()
    
    // Reset configuration
    const resetButton = screen.getByText('Reset')
    fireEvent.click(resetButton)
    
    // Should be back to original value
    expect(screen.getByDisplayValue('Loading stream...')).toBeInTheDocument()
  })

  it('uses admin API endpoint when isAdmin is true', async () => {
    const props = { ...defaultProps, isAdmin: true }
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({ success: true })
    })
    
    window.alert = jest.fn()
    
    render(<EnhancedConfigEditor {...props} />)
    
    const saveButton = screen.getByText('Save Configuration')
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/admin/projects', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          project_id: 'project-1',
          config: defaultProps.currentConfig
        }),
      })
    })
  })

  it('shows loading state during save', async () => {
    // Mock slow API response
    let resolvePromise: (value: any) => void
    const slowPromise = new Promise(resolve => {
      resolvePromise = resolve
    })
    mockFetch.mockReturnValueOnce(slowPromise)
    
    render(<EnhancedConfigEditor {...defaultProps} />)
    
    const saveButton = screen.getByText('Save Configuration')
    fireEvent.click(saveButton)
    
    // Should show loading state
    expect(screen.getByText('Saving...')).toBeInTheDocument()
    expect(saveButton).toBeDisabled()
    
    // Resolve the promise
    resolvePromise!({
      ok: true,
      json: () => Promise.resolve({ success: true })
    })
    
    await waitFor(() => {
      expect(screen.getByText('Save Configuration')).toBeInTheDocument()
    })
  })
})
