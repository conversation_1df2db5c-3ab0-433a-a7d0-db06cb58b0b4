declare module 'streampixelsdk' {
  export interface StreamPixelConfig {
    AutoConnect?: boolean
    appId: string
    useMic?: boolean
    primaryCodec?: 'AV1' | 'H264' | 'VP9' | 'VP8'
    fallBackCodec?: 'AV1' | 'H264' | 'VP9' | 'VP8'
    afktimeout?: number
    touchInput?: boolean
    mouseInput?: boolean
    gamepadInput?: boolean
    hoverMouse?: boolean
    xrInput?: boolean
    showResolution?: boolean
    keyBoardInput?: boolean
    fakeMouseWithTouches?: boolean
    maxStreamQuality?: string
    startResolutionMobile?: string
    startResolutionTab?: string
    startResolution?: string
    resolutionMode?: 'Fixed Resolution Mode' | 'Crop on Resize Mode' | 'Dynamic Resolution Mode'
    minBitrate?: number
    maxBitrate?: number
    maxQP?: number
    minQP?: number
  }

  export interface StreamPixelResponse {
    appStream: {
      onConnectAction?: () => void
      onWebRtcConnecting?: () => void
      onWebRtcConnected?: () => void
      onVideoInitialized?: () => void
      onDisconnect?: () => void
      rootElement: HTMLElement
      stream: {
        videoElementParent: HTMLElement
        disconnect: () => void
        emitUIInteraction: (descriptor: any) => void
      }
    }
    pixelStreaming: {
      addResponseEventListener: (event: string, handler: (response: any) => void) => void
      disconnect: () => void
      unmuteMicrophone: (enabled: boolean) => void
    }
    queueHandler: (callback: (msg: { position: number }) => void) => void
    UIControl: {
      toggleAudio: () => void
      handleResMax: (value: string) => void
      getStreamStats: () => any
      getResolution: () => any
    }
  }

  export function StreamPixelApplication(config: StreamPixelConfig): Promise<StreamPixelResponse>
}
