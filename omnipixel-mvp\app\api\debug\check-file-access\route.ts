import { NextRequest, NextResponse } from 'next/server'
import { checkFileAccessibility } from '@/lib/aws'

// Debug endpoint to check if a file URL is accessible
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { url } = body

    if (!url) {
      return NextResponse.json(
        { error: 'Missing required field: url' },
        { status: 400 }
      )
    }

    console.log('🔍 Checking file accessibility for:', url)
    const isAccessible = await checkFileAccessibility(url)

    return NextResponse.json({
      url,
      accessible: isAccessible,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error checking file accessibility:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
