import { NextRequest, NextResponse } from 'next/server'
import { uploadFileToS3, generateS3Key } from '@/lib/aws'
import { createClient } from '@/utils/supabase/server'

export async function POST(request: NextRequest) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    const projectId = formData.get('projectId') as string

    if (!file || !projectId) {
      return NextResponse.json(
        { error: 'Missing file or projectId' },
        { status: 400 }
      )
    }

    // Get user profile to check if they're a platform admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    const isPlatformAdmin = profile?.role === 'platform_admin'

    // Verify that the project exists and user has access (owner or platform admin)
    let query = supabase
      .from('projects')
      .select('id, user_id, name')
      .eq('id', projectId)

    // If not platform admin, restrict to user's own projects
    if (!isPlatformAdmin) {
      query = query.eq('user_id', user.id)
    }

    const { data: project, error: projectError } = await query.single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // Generate B2 key
    const b2Key = generateS3Key(user.id, projectId, file.name)

    // Upload to Backblaze B2
    const fileUrl = await uploadFileToS3(
      b2Key,
      buffer,
      file.type || 'application/octet-stream',
      {
        streamPixelProcess: false
      }
    )

    // Save to database
    const { data: builds, error: buildsError } = await supabase
      .from('builds')
      .select('version')
      .eq('project_id', projectId)
      .order('version', { ascending: false })
      .limit(1)

    if (buildsError) {
      console.error('Error fetching builds:', buildsError)
      return NextResponse.json(
        { error: 'Failed to fetch existing builds' },
        { status: 500 }
      )
    }

    const nextVersion = builds.length > 0 ? builds[0].version + 1 : 1

    const { data: newBuild, error: insertError } = await supabase
      .from('builds')
      .insert([
        {
          project_id: projectId,
          filename: file.name,
          b2_key: b2Key,
          version: nextVersion,
          status: 'processing',
          file_size: file.size,
        }
      ])
      .select()
      .single()

    if (insertError) {
      console.error('Error inserting build:', insertError)
      return NextResponse.json(
        { error: 'Failed to save build record' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'File uploaded successfully',
      build: newBuild,
      fileUrl,
      b2Key
    })

  } catch (error) {
    console.error('Error uploading file:', error)
    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    )
  }
}
