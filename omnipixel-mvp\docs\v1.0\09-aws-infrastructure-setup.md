# AWS Infrastructure Setup v1.0

## Overview
This document outlines the AWS infrastructure setup for the Omnipixel MVP, including S3 file storage, secure upload mechanisms, and file management capabilities.

## Architecture Components

### 1. AWS S3 Storage
- **Purpose**: Store game build files securely
- **Bucket Structure**: `omnipixel-uploads`
- **File Organization**: `uploads/{userId}/{projectId}/{timestamp}_{filename}`
- **Access Control**: Presigned URLs for secure uploads/downloads

### 2. File Upload Flow
1. **Client Request**: User selects file for upload
2. **Presigned URL**: Server generates secure upload URL
3. **Direct Upload**: File uploads directly to S3
4. **Database Record**: Build record created in Supabase
5. **Completion**: User notified of successful upload

## Implementation Details

### AWS SDK Integration
```typescript
// lib/aws.ts - Core AWS functionality
- S3Client configuration
- Presigned URL generation
- File validation
- Content type detection
```

### API Endpoints
```typescript
// app/api/upload/presigned-url/route.ts
- POST: Generate presigned upload URL
- Authentication: Required
- Validation: File type, project ownership

// app/api/upload/complete/route.ts  
- POST: Complete upload and create build record
- Database: Insert build record
- Versioning: Auto-increment build versions
```

### Frontend Components
```typescript
// components/file-upload.tsx
- Drag & drop interface
- Progress tracking
- File validation
- Error handling

// hooks/use-file-upload.ts
- Upload state management
- Progress callbacks
- Error handling
```

## Security Features

### 1. Authentication & Authorization
- **User Authentication**: Supabase session validation
- **Project Ownership**: Verify user owns project before upload
- **API Protection**: All endpoints require valid session

### 2. File Validation
- **File Types**: Restricted to game build formats
- **Size Limits**: Configurable upload limits
- **Content Type**: Automatic detection and validation

### 3. Secure Upload Process
- **Presigned URLs**: Time-limited (1 hour default)
- **Direct S3 Upload**: No server intermediary
- **Access Control**: User-specific file paths

## File Organization

### S3 Bucket Structure
```
omnipixel-uploads/
├── uploads/
│   ├── {userId}/
│   │   ├── {projectId}/
│   │   │   ├── {timestamp}_{filename}
│   │   │   └── ...
│   │   └── ...
│   └── ...
└── ...
```

### Supported File Types
- **Archives**: .zip, .rar, .7z, .tar.gz
- **Executables**: .exe, .app, .dmg
- **Mobile**: .apk, .ipa, .aab
- **Game Engines**: .unity3d, .unitypackage
- **Linux**: .deb, .rpm

## Database Schema Integration

### Builds Table
```sql
CREATE TABLE builds (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    filename TEXT NOT NULL,
    s3_key TEXT NOT NULL,
    version INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Version Management
- **Auto-increment**: Versions automatically assigned
- **Latest Build**: Easy identification of current version
- **History**: Complete build history maintained

## Environment Configuration

### Required Environment Variables
```env
# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_S3_BUCKET_NAME=omnipixel-uploads
```

### AWS IAM Permissions
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:GetObject",
                "s3:DeleteObject"
            ],
            "Resource": "arn:aws:s3:::omnipixel-uploads/*"
        }
    ]
}
```

## User Interface

### Dashboard Integration
- **Project Cards**: Show build count and latest build
- **Upload Button**: Direct access to upload functionality
- **Build History**: List of all project builds

### Project Detail Page
- **Upload Section**: Drag & drop file upload
- **Build List**: Complete build history
- **Download Links**: Secure download access
- **Version Display**: Clear version numbering

### Upload Experience
- **Drag & Drop**: Intuitive file selection
- **Progress Bar**: Real-time upload progress
- **Validation**: Immediate file type feedback
- **Error Handling**: Clear error messages

## Error Handling

### Client-Side Validation
- **File Type**: Check before upload attempt
- **File Size**: Prevent oversized uploads
- **Authentication**: Verify user session

### Server-Side Protection
- **Project Ownership**: Verify user access
- **File Validation**: Double-check file types
- **AWS Errors**: Handle S3 service errors

### User Feedback
- **Success Messages**: Confirm successful uploads
- **Error Messages**: Clear problem descriptions
- **Progress Indicators**: Visual upload status

## Performance Optimizations

### Direct S3 Upload
- **No Server Bottleneck**: Files upload directly to S3
- **Reduced Latency**: Eliminate server intermediary
- **Scalability**: Handle multiple concurrent uploads

### Presigned URLs
- **Security**: Time-limited access
- **Performance**: No authentication overhead
- **Flexibility**: Support various upload scenarios

## Monitoring & Logging

### Upload Tracking
- **Database Records**: All uploads logged
- **Error Logging**: Failed uploads tracked
- **Performance Metrics**: Upload times monitored

### AWS CloudWatch
- **S3 Metrics**: Storage usage and requests
- **Error Rates**: Failed upload monitoring
- **Cost Tracking**: Storage and transfer costs

## Future Enhancements

### Planned Features
1. **File Compression**: Automatic compression for large files
2. **CDN Integration**: CloudFront for faster downloads
3. **Backup Strategy**: Cross-region replication
4. **Analytics**: Upload and download statistics

### Scalability Considerations
1. **Multi-Region**: Support for global users
2. **Load Balancing**: Handle high upload volumes
3. **Caching**: Improve download performance
4. **Cost Optimization**: Intelligent storage classes

## Setup Instructions

### 1. AWS Account Setup
1. Create AWS account
2. Create S3 bucket: `omnipixel-uploads`
3. Create IAM user with S3 permissions
4. Generate access keys

### 2. Environment Configuration
1. Update `.env.local` with AWS credentials
2. Configure bucket name and region
3. Test AWS connectivity

### 3. Application Deployment
1. Deploy application with AWS environment variables
2. Test file upload functionality
3. Verify database integration

## Testing Checklist

### Upload Functionality
- [ ] File selection works
- [ ] Drag & drop functions
- [ ] Progress tracking displays
- [ ] Error handling works
- [ ] Success confirmation shows

### Security Validation
- [ ] Authentication required
- [ ] Project ownership verified
- [ ] File type validation works
- [ ] Presigned URLs expire

### Database Integration
- [ ] Build records created
- [ ] Version numbering works
- [ ] Project association correct
- [ ] History displays properly

## Troubleshooting

### Common Issues
1. **AWS Credentials**: Verify access keys and permissions
2. **Bucket Access**: Check bucket policies and CORS
3. **File Types**: Ensure supported formats
4. **Network**: Verify internet connectivity

### Debug Steps
1. Check browser console for errors
2. Verify server logs for API errors
3. Test AWS credentials independently
4. Validate environment variables

## Conclusion

The AWS infrastructure provides a robust, secure, and scalable foundation for file uploads in the Omnipixel MVP. The implementation supports direct S3 uploads, comprehensive security, and seamless user experience while maintaining cost efficiency and performance.
