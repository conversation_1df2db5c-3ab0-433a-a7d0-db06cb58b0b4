# Role-Based Access Control (RBAC) v1.0

## Overview
This document outlines the role-based access control system implemented in the Omnipixel MVP, defining permissions and restrictions for different user types.

## User Roles

### 1. Regular User (`user`)
**Default role for all new users**

#### Permissions:
- ✅ View assigned projects
- ✅ Upload build files to assigned projects
- ✅ Rename assigned projects
- ✅ Download builds from assigned projects
- ✅ View build history for assigned projects
- ✅ Update project configuration (metadata only)

#### Restrictions:
- ❌ Cannot create new projects
- ❌ Cannot modify Stream Project IDs
- ❌ Cannot assign projects to other users
- ❌ Cannot view other users' projects
- ❌ Cannot access admin panel
- ❌ Cannot delete projects

### 2. Platform Admin (`platform_admin`)
**Administrative role with elevated permissions**

#### Permissions:
- ✅ All regular user permissions
- ✅ Create new projects for any user
- ✅ Assign Stream Project IDs
- ✅ View all projects across the platform
- ✅ Modify any project settings
- ✅ Access admin panel
- ✅ Manage user assignments
- ✅ Delete projects
- ✅ Update Stream Project IDs

#### Additional Capabilities:
- ✅ Platform-wide project overview
- ✅ User management interface
- ✅ System administration tools

## Database Security Implementation

### Row Level Security (RLS) Policies

#### Projects Table
```sql
-- Users can only view their assigned projects
CREATE POLICY "Users can view their own projects" ON projects
    FOR SELECT USING (auth.uid() = user_id);

-- Users can update name and config only
CREATE POLICY "Users can update project name and config" ON projects
    FOR UPDATE USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Only platform admins can create projects
CREATE POLICY "Platform admins can create projects" ON projects
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'platform_admin'
        )
    );

-- Platform admins can update all fields
CREATE POLICY "Platform admins can update all project fields" ON projects
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'platform_admin'
        )
    );
```

#### Builds Table
```sql
-- Users can view builds for their projects
CREATE POLICY "Users can view builds for their projects" ON builds
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE id = builds.project_id AND user_id = auth.uid()
        )
    );

-- Users can upload builds to their projects
CREATE POLICY "Users can insert builds for their projects" ON builds
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE id = builds.project_id AND user_id = auth.uid()
        )
    );
```

## API Endpoint Security

### User Endpoints
```typescript
// /api/projects/[id] - Project management
- PATCH: Update project name and config only
- GET: View project details and builds
- Authentication: Required
- Authorization: Project ownership verified

// /api/upload/* - File upload endpoints
- POST: Upload builds to owned projects
- Authentication: Required
- Authorization: Project ownership verified
```

### Admin Endpoints
```typescript
// /api/admin/projects - Admin project management
- GET: View all projects across platform
- POST: Create projects for any user
- Authentication: Required
- Authorization: Platform admin role required

// /api/admin/projects/[id] - Admin project operations
- PATCH: Update any project field including Stream ID
- DELETE: Remove projects
- Authentication: Required
- Authorization: Platform admin role required
```

## Frontend Access Control

### Dashboard Behavior

#### Regular Users
- See only assigned projects
- No project creation button
- Upload functionality available for owned projects
- Project renaming allowed

#### Platform Admins
- Admin Panel button visible
- Can access all user projects
- Project creation through admin interface
- Full project management capabilities

### Admin Panel Features
- **Project Creation**: Assign projects to users with Stream IDs
- **User Management**: View all users and their projects
- **Platform Statistics**: Total projects, users, builds
- **Project Overview**: Complete project listing with user assignments

## Workflow Examples

### Project Creation Workflow
1. **Admin Action**: Platform admin creates project via admin panel
2. **User Assignment**: Project assigned to specific user email
3. **Stream ID Assignment**: Admin assigns unique Stream Project ID
4. **User Access**: User can now see and manage the project
5. **Build Uploads**: User can upload builds to the project

### User Project Management
1. **View Projects**: User sees only assigned projects
2. **Rename Project**: User can change project display name
3. **Upload Builds**: User uploads game builds via drag & drop
4. **Version Management**: System auto-increments build versions
5. **Download Access**: User can download their builds

### Admin Project Management
1. **Platform Overview**: Admin sees all projects and users
2. **Create Project**: Admin creates project for specific user
3. **Stream ID Management**: Admin assigns/updates Stream Project IDs
4. **User Support**: Admin can view user projects for support
5. **System Maintenance**: Admin can delete or modify projects

## Security Considerations

### Authentication
- All endpoints require valid Supabase session
- Session validation on every request
- Automatic session refresh handling

### Authorization
- Role-based permission checking
- Project ownership verification
- Database-level security with RLS
- API-level permission validation

### Data Isolation
- Users can only access their assigned data
- Admins have controlled elevated access
- No cross-user data leakage
- Secure file upload with user-specific paths

## Error Handling

### Access Denied Scenarios
```typescript
// User tries to access admin endpoint
{ error: 'Access denied. Platform admin role required.', status: 403 }

// User tries to access another user's project
{ error: 'Project not found or access denied', status: 404 }

// User tries to create project directly
{ error: 'Insufficient permissions', status: 403 }
```

### Graceful Degradation
- Admin features hidden for regular users
- Clear messaging about permission requirements
- Redirect to appropriate interfaces based on role

## Implementation Details

### Role Assignment
```sql
-- Default role assignment in profile creation trigger
INSERT INTO public.profiles (id, email, role)
VALUES (NEW.id, NEW.email, 'user');

-- Admin role assignment (manual process)
UPDATE profiles SET role = 'platform_admin' WHERE email = '<EMAIL>';
```

### Permission Checking
```typescript
// Frontend role checking
const isAdmin = profile?.role === 'platform_admin'

// Backend role verification
const { data: profile } = await supabase
  .from('profiles')
  .select('role')
  .eq('id', session.user.id)
  .single()

if (profile?.role !== 'platform_admin') {
  return NextResponse.json({ error: 'Access denied' }, { status: 403 })
}
```

## Testing Scenarios

### User Permission Tests
- [ ] User can view only assigned projects
- [ ] User can rename assigned projects
- [ ] User can upload builds to assigned projects
- [ ] User cannot create new projects
- [ ] User cannot access admin panel
- [ ] User cannot modify Stream Project IDs

### Admin Permission Tests
- [ ] Admin can create projects for users
- [ ] Admin can assign Stream Project IDs
- [ ] Admin can view all projects
- [ ] Admin can access admin panel
- [ ] Admin can modify any project field
- [ ] Admin can delete projects

### Security Tests
- [ ] RLS policies prevent unauthorized access
- [ ] API endpoints validate permissions
- [ ] Cross-user data access blocked
- [ ] Session validation working
- [ ] Role-based UI rendering correct

## Future Enhancements

### Additional Roles
- **Organization Admin**: Manage users within organization
- **Project Manager**: Manage specific projects across users
- **Viewer**: Read-only access to assigned projects

### Enhanced Permissions
- **Granular Project Permissions**: Per-project access control
- **Time-based Access**: Temporary project access
- **Feature Flags**: Role-based feature access
- **Audit Logging**: Track all permission-based actions

### Advanced Security
- **Multi-factor Authentication**: Enhanced admin security
- **IP Restrictions**: Location-based access control
- **Session Management**: Advanced session controls
- **API Rate Limiting**: Role-based rate limits

## Troubleshooting

### Common Issues
1. **User can't see projects**: Check project assignment in admin panel
2. **Upload fails**: Verify project ownership and permissions
3. **Admin features missing**: Confirm platform_admin role assignment
4. **RLS errors**: Check database policies and user authentication

### Debug Steps
1. Verify user authentication status
2. Check user role in profiles table
3. Validate project ownership relationships
4. Test RLS policies with direct database queries
5. Review API endpoint permission checks

## Conclusion

The role-based access control system provides secure, scalable user management for the Omnipixel MVP. It ensures proper data isolation while enabling administrative control over project creation and Stream Project ID assignment. The system is designed to be extensible for future role additions and permission refinements.
