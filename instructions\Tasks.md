# ✅ Omnipixel Task List

## Project Setup
- [ ] Initialize `omnipixel` git repo with Next.js + TypeScript
- [ ] Install Tailwind CSS & shadcn/ui
- [ ] Configure ESLint, <PERSON><PERSON><PERSON>, <PERSON><PERSON>
- [ ] Add `.gitignore`, `.env.example`, and initial README

## Infrastructure (AWS SAM)
- [ ] Run `sam init` for new project
- [ ] Define S3 bucket in `template.yaml` with:
  - Versioning enabled
  - CORS for frontend domain
  - Lifecycle policy (retain objects)
- [ ] Define API Gateway endpoints and Lambda functions:
  - `getPresignUrl`
  - `handleUploadComplete`
- [ ] Grant IAM permissions for S3 and CloudWatch
- [ ] Validate local environment with `sam local start-api`

## Supabase Setup
- [ ] Create Supabase project (Pro tier)
- [ ] Define tables:
  - `profiles` (id, email, role)
  - `projects` (id, user_id, name, stream_project_id)
  - `builds` (id, project_id, filename, s3_key, version, created_at)
- [ ] Add `profiles.role` enum (`user`, `platform_admin`)
- [ ] Implement RLS policies:
  - Users can only access own projects/builds
  - Platform Admin bypass
- [ ] Store `SUPABASE_URL`, `SUPABASE_ANON_KEY`, `SUPABASE_SERVICE_ROLE_KEY` in Vercel environment

## Next.js Frontend
### Authentication
- [ ] Create `/login` page using `@supabase/auth-ui-react`
- [ ] Wrap app in Supabase AuthProvider

### Dashboard
- [ ] Build `/dashboard` to list user’s projects via `useProjects` hook
- [ ] Create `ProjectCard` component

### Project Page
- [ ] Build `/projects/[id]/page.tsx` with:
  - Editable project name and config fields
  - Build history table showing 2 latest builds
- [ ] Inline editing handler to update project in Supabase

### Stream Page
- [ ] Create `/projects/[id]/stream/page.tsx`
- [ ] Implement `Player` component using streaming SDK and project’s `stream_project_id`
- [ ] Protect route via authentication

### Admin Pages
- [ ] `/admin/users` page:
  - Show list of users and their roles
  - Buttons to create, edit, delete users
  - Assign/unassign projects
- [ ] `/admin/projects` page:
  - Show all projects and assigned users
  - Buttons to create/edit/delete projects and update metadata

## Upload Flow
### API Endpoints
- [ ] `pages/api/get-presign-url.ts` endpoint:
  - Validate user/project access
  - Generate multipart presign URL via AWS SDK
- [ ] `pages/api/notify-upload.ts` endpoint:
  - Validate payload
  - Trigger `handleUploadComplete` Lambda

### Frontend Upload Logic
- [ ] Implement `useUpload()` hook to:
  - Request presign URLs
  - Upload file parts with progress tracking
  - Finalize upload (`completeMultipartUpload`)
  - Call `/api/notify-upload`
- [ ] `UploadProgress` component for visual feedback

## Lambda Functionality
### handleUploadComplete
- [ ] Parse incoming payload (projectId, s3Key, etc.)
- [ ] Invoke Streampixel Upload API
- [ ] List S3 objects under project prefix
- [ ] If >2 objects, delete oldest
- [ ] Insert new build entry to Supabase via service role key
- [ ] Log events and errors to CloudWatch

### Edge Cases & Testing
- [ ] Add timestamp-based filtering
- [ ] Handle network failures gracefully

## Testing & QA
### Unit Tests
- [ ] Test `useUpload()`, `getPresignUrl`, `notifyUpload` logic
- [ ] Mock Lambda environment for `handleUploadComplete`

### Integration Tests
- [ ] Simulate full flow: login → upload ZIP → retention check → build entry

### Manual QA
- [ ] Test large file uploads via frontend
- [ ] Verify only two ZIPs persist per project
- [ ] Validate streaming page works
- [ ] Test admin user management workflows

## Deployment
- [ ] Configure Vercel project with environment variables
- [ ] Deploy frontend previews on PRs
- [ ] Deploy SAM resources using CI/CD (`sam build` + `sam deploy`)
- [ ] Verify API endpoints and frontend connectivity

## Documentation
- [ ] Write README with architecture overview
- [ ] Include schema SQL and RLS policies
- [ ] Document setup steps for AWS, Supabase, Vercel, SAM
