import { notFound } from 'next/navigation'
import { StreamPlayer } from '@/components/stream-player'
import { createClient } from '@/utils/supabase/server'
import { Build } from '@/lib/supabase'
import Script from 'next/script'

interface EmbedPageProps {
  params: Promise<{
    projectId: string
  }>
  searchParams: Promise<{
    hideControls?: string
    hideHeader?: string
    hideEmbedButton?: string
    autoConnect?: string
    password?: string
  }>
}

async function getProject(projectId: string) {
  
  const supabase = await createClient();

  const { data: project, error } = await supabase
    .from('projects')
    .select(`
      *,
      builds!builds_project_id_fkey (
        id,
        filename,
        version,
        status,
        is_current,
        file_size,
        streampixel_build_id,
        streampixel_status,
        created_at
      )
    `)
    .eq('id', projectId)
    .single()

  if (error || !project) {
    return null
  }

  return project
}

export default async function EmbedPage({ params, searchParams }: EmbedPageProps) {
  const projectId = (await params).projectId
  const project = await getProject(projectId)

  if (!project) {
    notFound()
  }

  // Get the current active build
  const currentBuild = project.builds?.find((build: Build) => build.is_current)

  // Parse URL parameters for control visibility
  const sParams = await searchParams
  const hideControls = sParams.hideControls === 'true'
  const hideHeader = sParams.hideHeader === 'true'
  const hideEmbedButton = sParams.hideEmbedButton === 'true'
  const autoConnect = sParams.autoConnect === 'true'
  const urlPassword = sParams.password

  return (
    <>
      {/* Tailwind CSS */}
      <Script src="https://cdn.tailwindcss.com" strategy="beforeInteractive" />

      {/* Stream Service SDK */}
      {/* <Script src="https://cdn.streampixel.io/sdk/latest/streampixel.min.js" strategy="lazyOnload" /> */}

      <style dangerouslySetInnerHTML={{
        __html: `
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          html, body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            background: #000;
            font-family: system-ui, -apple-system, sans-serif;
            overflow: hidden;
          }

          .embed-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: #000;
            z-index: 1;
          }

          .stream-wrapper {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
          }

          /* Hide all scrollbars */
          * {
            scrollbar-width: none;
            -ms-overflow-style: none;
          }

          *::-webkit-scrollbar {
            display: none;
          }

          /* Ensure full coverage */
          #__next {
            width: 100%;
            height: 100%;
          }
        `
      }} />

      <div className="embed-container">
        <div className="stream-wrapper">
          <StreamPlayer
            projectId={project.id}
            buildId={currentBuild?.id}
            config={{
              ...project.config,
              autoConnect: autoConnect || project.config?.autoConnect,
              password: urlPassword || project.config?.password
            }}
            showControls={!hideControls}
            showHeader={!hideHeader}
            showEmbedButton={!hideEmbedButton}
            isEmbedded={true}
            enableIframeComms={true}
            className="absolute inset-0 w-full h-full border-0"
          />
        </div>
      </div>

      {/* Embedded player metadata */}
      <Script
        id="structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "VideoObject",
            "name": project.name,
            "description": `Interactive streaming experience for ${project.name}`,
            "thumbnailUrl": `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/thumbnails/${project.id}.jpg`,
            "embedUrl": `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,
            "uploadDate": project.created_at,
            "duration": "PT0S",
            "interactionStatistic": {
              "@type": "InteractionCounter",
              "interactionType": "https://schema.org/WatchAction",
              "userInteractionCount": 0
            }
          })
        }}
      />

      {/* Analytics and tracking */}
      <Script
        id="embed-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            // Track embed views
            if (window.parent !== window) {
              // This is embedded in an iframe
              console.log('Stream embedded and loaded');

              // Send message to parent frame if needed
              try {
                window.parent.postMessage({
                  type: 'stream-loaded',
                  projectId: '${project.id}',
                  timestamp: new Date().toISOString()
                }, '*');
              } catch (e) {
                // Cross-origin restrictions
              }
            }

            // Prevent right-click context menu
            document.addEventListener('contextmenu', function(e) {
              e.preventDefault();
            });

            // Prevent text selection
            document.addEventListener('selectstart', function(e) {
              e.preventDefault();
            });

            // Prevent drag and drop
            document.addEventListener('dragstart', function(e) {
              e.preventDefault();
            });
          `
        }}
      />
    </>
  )
}

// Generate metadata for SEO
export async function generateMetadata({ params }: EmbedPageProps) {
  const projectId = (await params).projectId
  const project = await getProject(projectId)

  if (!project) {
    return {
      title: 'Stream Not Found',
      description: 'The requested stream could not be found.'
    }
  }

  return {
    title: `${project.name} - Interactive Stream`,
    description: `Interactive streaming experience for ${project.name}`,
    openGraph: {
      title: `${project.name} - Interactive Stream`,
      description: `Interactive streaming experience for ${project.name}`,
      type: 'video.other',
      url: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,
    },
    twitter: {
      card: 'player',
      title: `${project.name} - Interactive Stream`,
      description: `Interactive streaming experience for ${project.name}`,
      players: {
        playerUrl: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,
        streamUrl: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,
        width: 1280,
        height: 720,
      },
    },
    robots: {
      index: false, // Don't index embed pages
      follow: false,
    },
  }
}
