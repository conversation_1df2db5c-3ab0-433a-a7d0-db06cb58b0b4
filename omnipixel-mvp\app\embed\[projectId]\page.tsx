import { notFound } from 'next/navigation'
import { StreamPlayer } from '@/components/stream-player'
import { createClient } from '@/utils/supabase/server'
import { Build } from '@/lib/supabase'
import Script from 'next/script'

interface EmbedPageProps {
  params: Promise<{
    projectId: string
  }>
  searchParams: Promise<{
    hideControls?: string
    hideHeader?: string
    hideEmbedButton?: string
    autoConnect?: string
    password?: string
  }>
}

async function getProject(projectId: string) {
  
  const supabase = await createClient();

  const { data: project, error } = await supabase
    .from('projects')
    .select(`
      *,
      builds!builds_project_id_fkey (
        id,
        filename,
        version,
        status,
        is_current,
        file_size,
        streampixel_build_id,
        streampixel_status,
        created_at
      )
    `)
    .eq('id', projectId)
    .single()

  if (error || !project) {
    return null
  }

  return project
}

export default async function EmbedPage({ params, searchParams }: EmbedPageProps) {
  const projectId = (await params).projectId
  const project = await getProject(projectId)

  if (!project) {
    notFound()
  }

  // Get the current active build
  const currentBuild = project.builds?.find((build: Build) => build.is_current)

  // Parse URL parameters for control visibility
  const sParams = await searchParams
  const hideControls = sParams.hideControls === 'true'
  const hideHeader = sParams.hideHeader === 'true'
  const hideEmbedButton = sParams.hideEmbedButton === 'true'
  const autoConnect = sParams.autoConnect === 'true'
  const urlPassword = sParams.password

  // Server-side password protection check
  const isPasswordProtected = project.config?.isPasswordProtected === true
  const projectPassword = project.config?.password

  if (isPasswordProtected && projectPassword) {
    // If password protection is enabled but no password provided in URL
    if (!urlPassword) {
      return (
        <>
          <style dangerouslySetInnerHTML={{
            __html: `
              body {
                margin: 0;
                padding: 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: #f8fafc;
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 100vh;
              }
              .password-container {
                background: white;
                padding: 2rem;
                border-radius: 8px;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                max-width: 400px;
                width: 100%;
                text-align: center;
              }
              .lock-icon {
                width: 48px;
                height: 48px;
                margin: 0 auto 1rem;
                color: #9ca3af;
              }
              h1 { margin: 0 0 0.5rem; color: #1f2937; }
              p { margin: 0 0 1.5rem; color: #6b7280; }
              input {
                width: 100%;
                padding: 0.75rem;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                margin-bottom: 1rem;
                font-size: 1rem;
              }
              button {
                width: 100%;
                padding: 0.75rem;
                background: #3b82f6;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 1rem;
                cursor: pointer;
              }
              button:hover { background: #2563eb; }
            `
          }} />
          <div className="password-container">
            <svg className="lock-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
            </svg>
            <h1>Password Protected</h1>
            <p>This stream requires a password to access.</p>
            <form method="GET">
              <input type="hidden" name="hideControls" value={hideControls.toString()} />
              <input type="hidden" name="hideHeader" value={hideHeader.toString()} />
              <input type="hidden" name="hideEmbedButton" value={hideEmbedButton.toString()} />
              <input type="hidden" name="autoConnect" value={autoConnect.toString()} />
              <input
                type="password"
                name="password"
                placeholder="Enter password"
                required
                autoFocus
              />
              <button type="submit">Access Stream</button>
            </form>
          </div>
        </>
      )
    }

    // If password provided but incorrect
    if (urlPassword !== projectPassword) {
      return (
        <>
          <style dangerouslySetInnerHTML={{
            __html: `
              body {
                margin: 0;
                padding: 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: #f8fafc;
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 100vh;
              }
              .password-container {
                background: white;
                padding: 2rem;
                border-radius: 8px;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                max-width: 400px;
                width: 100%;
                text-align: center;
              }
              .error-icon {
                width: 48px;
                height: 48px;
                margin: 0 auto 1rem;
                color: #ef4444;
              }
              h1 { margin: 0 0 0.5rem; color: #1f2937; }
              p { margin: 0 0 1.5rem; color: #6b7280; }
              .error { color: #ef4444; margin-bottom: 1rem; }
              input {
                width: 100%;
                padding: 0.75rem;
                border: 1px solid #ef4444;
                border-radius: 6px;
                margin-bottom: 1rem;
                font-size: 1rem;
              }
              button {
                width: 100%;
                padding: 0.75rem;
                background: #3b82f6;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 1rem;
                cursor: pointer;
              }
              button:hover { background: #2563eb; }
            `
          }} />
          <div className="password-container">
            <svg className="error-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <h1>Access Denied</h1>
            <p>The password you entered is incorrect.</p>
            <div className="error">Invalid password. Please try again.</div>
            <form method="GET">
              <input type="hidden" name="hideControls" value={hideControls.toString()} />
              <input type="hidden" name="hideHeader" value={hideHeader.toString()} />
              <input type="hidden" name="hideEmbedButton" value={hideEmbedButton.toString()} />
              <input type="hidden" name="autoConnect" value={autoConnect.toString()} />
              <input
                type="password"
                name="password"
                placeholder="Enter password"
                required
                autoFocus
              />
              <button type="submit">Try Again</button>
            </form>
          </div>
        </>
      )
    }
  }

  return (
    <>
      {/* Tailwind CSS */}
      <Script src="https://cdn.tailwindcss.com" strategy="beforeInteractive" />

      {/* Stream Service SDK */}
      {/* <Script src="https://cdn.streampixel.io/sdk/latest/streampixel.min.js" strategy="lazyOnload" /> */}

      <style dangerouslySetInnerHTML={{
        __html: `
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          html, body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            background: #000;
            font-family: system-ui, -apple-system, sans-serif;
            overflow: hidden;
          }

          .embed-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: #000;
            z-index: 1;
          }

          .stream-wrapper {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
          }

          /* Hide all scrollbars */
          * {
            scrollbar-width: none;
            -ms-overflow-style: none;
          }

          *::-webkit-scrollbar {
            display: none;
          }

          /* Ensure full coverage */
          #__next {
            width: 100%;
            height: 100%;
          }
        `
      }} />

      <div className="embed-container">
        <div className="stream-wrapper">
          <StreamPlayer
            projectId={project.id}
            buildId={currentBuild?.id}
            config={{
              ...project.config,
              autoConnect: autoConnect || project.config?.autoConnect,
              // Password protection is handled at server level, so disable client-side protection
              isPasswordProtected: false,
              password: undefined
            }}
            showControls={!hideControls}
            showHeader={!hideHeader}
            showEmbedButton={!hideEmbedButton}
            isEmbedded={true}
            enableIframeComms={true}
            projectData={project}
            className="absolute inset-0 w-full h-full border-0"
          />
        </div>
      </div>

      {/* Embedded player metadata */}
      <Script
        id="structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "VideoObject",
            "name": project.name,
            "description": `Interactive streaming experience for ${project.name}`,
            "thumbnailUrl": `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/thumbnails/${project.id}.jpg`,
            "embedUrl": `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,
            "uploadDate": project.created_at,
            "duration": "PT0S",
            "interactionStatistic": {
              "@type": "InteractionCounter",
              "interactionType": "https://schema.org/WatchAction",
              "userInteractionCount": 0
            }
          })
        }}
      />

      {/* Analytics and tracking */}
      <Script
        id="embed-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            // Track embed views
            if (window.parent !== window) {
              // This is embedded in an iframe
              console.log('Stream embedded and loaded');

              // Send message to parent frame if needed
              try {
                window.parent.postMessage({
                  type: 'stream-loaded',
                  projectId: '${project.id}',
                  timestamp: new Date().toISOString()
                }, '*');
              } catch (e) {
                // Cross-origin restrictions
              }
            }

            // Prevent right-click context menu
            document.addEventListener('contextmenu', function(e) {
              e.preventDefault();
            });

            // Prevent text selection
            document.addEventListener('selectstart', function(e) {
              e.preventDefault();
            });

            // Prevent drag and drop
            document.addEventListener('dragstart', function(e) {
              e.preventDefault();
            });
          `
        }}
      />
    </>
  )
}

// Generate metadata for SEO
export async function generateMetadata({ params }: EmbedPageProps) {
  const projectId = (await params).projectId
  const project = await getProject(projectId)

  if (!project) {
    return {
      title: 'Stream Not Found',
      description: 'The requested stream could not be found.'
    }
  }

  return {
    title: `${project.name} - Interactive Stream`,
    description: `Interactive streaming experience for ${project.name}`,
    openGraph: {
      title: `${project.name} - Interactive Stream`,
      description: `Interactive streaming experience for ${project.name}`,
      type: 'video.other',
      url: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,
    },
    twitter: {
      card: 'player',
      title: `${project.name} - Interactive Stream`,
      description: `Interactive streaming experience for ${project.name}`,
      players: {
        playerUrl: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,
        streamUrl: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/embed/${project.id}`,
        width: 1280,
        height: 720,
      },
    },
    robots: {
      index: false, // Don't index embed pages
      follow: false,
    },
  }
}
