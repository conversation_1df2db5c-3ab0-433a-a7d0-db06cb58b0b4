import requests

url = "https://api.streampixel.io/pixelStripeApi/projects/upload-file"
payload = {
  "apiKey": "00000000000000000000000000000000:47eca1276046d9d0c8d0c2303c962322f970626076b4653891dd5d977e58d563f8a8b82a3993c7f6119ccfc70aa839f",
  "userId": "686603e06285103f373d05fb",
  "fileUrl": "https://omnipixel-uploads-dev-851725278104.s3.us-east-1.amazonaws.com/uploads/53124e07-1037-4e9f-80f9-19f7ebf590c1/1684aea6-5444-44db-a390-bbb212c67d0a/1753563596222_package.zip?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIA4MTWILOMLIOIOCET%2F20250726%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250726T225159Z&X-Amz-Expires=86400&X-Amz-Signature=278ebba181d0734b9bfff6f4a7323585cdfcf5db8fb50e17848fd68ed4d2587a&X-Amz-SignedHeaders=host&x-amz-checksum-mode=ENABLED&x-id=GetObject",
  "projectId": "68668b0f6285103f373eb62d",
  "autoRelease": True
}

response = requests.post(url, json=payload)

if response.status_code == 200:
    print("Upload successful:", response.json())
else:
    print("Upload failed:", response.text)