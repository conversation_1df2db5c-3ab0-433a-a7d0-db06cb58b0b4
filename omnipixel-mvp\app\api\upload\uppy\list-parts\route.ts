import { NextRequest, NextResponse } from 'next/server'
import {
  isAWSConfigured
} from '@/lib/aws'
import { S3Client, ListPartsCommand } from '@aws-sdk/client-s3'
import { createClient } from '@/utils/supabase/server'

export async function POST(request: NextRequest) {
  try {
    // Check if AWS is configured
    if (!isAWSConfigured()) {
      return NextResponse.json(
        { error: 'File upload is not configured. Please contact the administrator.' },
        { status: 503 }
      )
    }

    // Create Supabase client for server-side auth
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { key, uploadId } = body

    // Validate required fields
    if (!key || !uploadId) {
      return NextResponse.json(
        { error: 'Missing required fields: key, uploadId' },
        { status: 400 }
      )
    }

    // Create S3 client
    const s3Client = new S3Client({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
      },
    })

    // List parts
    const command = new ListPartsCommand({
      Bucket: process.env.AWS_S3_BUCKET_NAME,
      Key: key,
      UploadId: uploadId,
    })

    const response = await s3Client.send(command)

    return NextResponse.json({
      parts: response.Parts || []
    })

  } catch (error) {
    console.error('Error in list-parts:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
