# Omnipixel MVP - Project Overview v1.0

## Project Description
Omnipixel is a serverless project dashboard and streaming platform that allows users to upload, manage, and stream their projects through a web interface. The platform integrates with Supabase for backend services and AWS for file storage and processing.

## Architecture Overview

### Frontend (Next.js 15)
- **Framework**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS 3.x
- **UI Components**: shadcn/ui
- **Authentication**: <PERSON>pabase Auth with SSR support
- **State Management**: React hooks and context

### Backend Services
- **Database**: Supabase (PostgreSQL with RLS)
- **Authentication**: Supabase Auth
- **File Storage**: AWS S3 with multipart upload
- **Processing**: AWS Lambda functions
- **Streaming**: Streampixel API integration

### Infrastructure
- **Frontend Hosting**: Vercel
- **Backend**: AWS SAM (Serverless Application Model)
- **Database**: Supabase hosted PostgreSQL
- **CDN**: Vercel Edge Network

## Key Features

### User Management
- User registration and authentication
- Role-based access control (User, Platform Admin)
- Profile management

### Project Management
- Create, read, update, delete projects
- Project configuration management
- Build version tracking

### File Upload System
- Multipart upload for large files (up to 24GB)
- Progress tracking and error handling
- S3 integration with presigned URLs

### Streaming Integration
- Project streaming through Streampixel API
- Real-time player controls
- Omnipixel branding

### Admin Interface
- User management for platform admins
- Project assignment and oversight
- System administration tools

## Technology Stack

### Frontend Dependencies
```json
{
  "next": "15.4.3",
  "react": "19.0.0",
  "typescript": "^5",
  "tailwindcss": "^3.4.0",
  "@supabase/supabase-js": "latest",
  "@supabase/ssr": "latest",
  "@aws-sdk/client-s3": "latest",
  "@aws-sdk/s3-request-presigner": "latest",
  "react-hook-form": "latest",
  "zod": "latest",
  "date-fns": "latest"
}
```

### Backend Services
- **Supabase**: Database, Auth, Real-time subscriptions
- **AWS S3**: File storage and management
- **AWS Lambda**: Serverless functions for processing
- **Streampixel API**: Streaming service integration

## Environment Variables
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY=your_supabase_publishable_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_S3_BUCKET_NAME=omnipixel-uploads

# Streampixel API Configuration
STREAMPIXEL_API_URL=https://api.streampixel.com
STREAMPIXEL_API_KEY=your_streampixel_api_key
```

## Project Structure
```
omnipixel-mvp/
├── app/                    # Next.js app directory
│   ├── dashboard/         # Dashboard pages
│   ├── login/            # Authentication pages
│   ├── projects/         # Project management pages
│   └── admin/            # Admin interface (future)
├── components/           # Reusable UI components
├── hooks/               # Custom React hooks
├── lib/                 # Utility libraries and configurations
├── docs/                # Project documentation
├── supabase/           # Database migrations and config
└── aws/                # AWS SAM templates (future)
```

## Current Status
- ✅ Frontend application fully functional
- ✅ Authentication system implemented
- ✅ Database schema and RLS policies configured
- ✅ Core UI components built
- ✅ Project management interface complete
- ✅ Supabase new API key system integrated
- 🚧 AWS infrastructure setup (next phase)
- 🚧 Upload system implementation (next phase)
- 🚧 Streaming integration (next phase)

## Next Steps
1. AWS Infrastructure Setup
2. Upload System Implementation
3. Lambda Functions Development
4. Streaming Integration
5. Admin Interface
6. Testing & QA
7. Deployment Configuration
