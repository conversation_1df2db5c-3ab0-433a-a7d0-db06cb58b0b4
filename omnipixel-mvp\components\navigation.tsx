'use client'

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { User, Settings, LogOut, Shield } from 'lucide-react'
import type { User as SupabaseUser } from '@supabase/supabase-js'
import type { Profile } from '@/lib/supabase'

interface NavigationProps {
  user?: SupabaseUser | null
  profile?: Profile | { role?: string } | null
}

export function Navigation({ user, profile }: NavigationProps = {}) {
  const router = useRouter()

  const handleSignOut = async () => {
    try {
      console.log('🔄 Signing out...')

      // Use direct Supabase client for more reliable signout
      const { error } = await supabase.auth.signOut()
      if (error) {
        console.error('❌ Supabase signout error:', error)
        throw error
      }
      console.log('✅ Direct Supabase signout successful')

      // Clear any local storage or session storage
      localStorage.clear()
      sessionStorage.clear()

      // Force a hard redirect to login page
      window.location.href = '/login'
    } catch (error) {
      console.error('❌ Failed to sign out:', error)
      // Fallback: clear storage and redirect anyway
      localStorage.clear()
      sessionStorage.clear()
      window.location.href = '/login'
    }
  }

  if (!user) return null

  return (
    <nav className="border-b bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/dashboard" className="flex-shrink-0">
              <h1 className="text-xl font-bold text-gray-900">Omnipixel</h1>
            </Link>
            <div className="hidden md:ml-6 md:flex md:space-x-8">
              <Link
                href="/dashboard"
                className="text-gray-900 hover:text-gray-700 px-3 py-2 text-sm font-medium"
              >
                Dashboard
              </Link>
              {profile?.role === 'platform_admin' && (
                <Link
                  href="/admin"
                  className="text-gray-900 hover:text-gray-700 px-3 py-2 text-sm font-medium flex items-center gap-1"
                >
                  <Shield className="h-4 w-4" />
                  Admin
                </Link>
              )}
            </div>
          </div>
          <div className="flex items-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <User className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <div className="flex items-center justify-start gap-2 p-2">
                  <div className="flex flex-col space-y-1 leading-none">
                    <p className="font-medium">{user.email}</p>
                    <p className="text-xs text-muted-foreground">
                      {profile?.role === 'platform_admin' ? 'Platform Admin' : 'User'}
                    </p>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                {profile?.role === 'platform_admin' && (
                  <DropdownMenuItem asChild>
                    <Link href="/admin">
                      <Shield className="mr-2 h-4 w-4" />
                      <span>Admin Panel</span>
                    </Link>
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onSelect={(e) => {
                    e.preventDefault()
                    console.log('🖱️ Signout clicked via onSelect!')
                    // Use setTimeout to ensure the dropdown closes first
                    setTimeout(() => {
                      handleSignOut()
                    }, 100)
                  }}
                  className="cursor-pointer focus:bg-red-50 focus:text-red-600"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </nav>
  )
}
